<template>
  <el-dialog
    append-to-body
    title="磁盘异常记录"
    :visible="true"
    :close-on-click-modal="false"
    width="900px"
    @close="handleCancel"
  >
    <el-row>
      <el-table ref="multipleTable" :data="tableData">
        <el-table-column prop="lake_name" label="LAKE" align="center"></el-table-column>
        <el-table-column prop="ip" label="IP" align="center"></el-table-column>
        <el-table-column prop="iowait" label="Iowait值" align="center"></el-table-column>
        <el-table-column prop="update_time" label="更新时间" align="center">
          <template slot-scope='scope'>
            <span>{{ (scope.row.update_time * 1000) | dateFormat }}</span>
          </template>
        </el-table-column>
      </el-table>

      <el-row style="text-align: center; padding-top: 20px">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.page_size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
        ></el-pagination>
      </el-row>
    </el-row>

    <div slot="footer" class="dialog-footer">
      <el-button size="medium" @click="handleCancel">关 闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import dateFormat from "@/utils/dateFormat"
import http from "@/api/http.js"

export default {
  components: {},
  filters: {
    dateFormat: dateFormat
  },
  props: {
    rowData: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      tableData: [],
      pagination: {
        total: 0,
        page: 1,
        page_size: 10,
      },
    }
  },
  computed: {},
  mounted() {
    if (this.rowData && Object.keys(this.rowData).length > 0) {
      this.getAbnormalDetail()
    }
  },
  methods: {
    // 获取磁盘异常记录
    async getAbnormalDetail() {
      let params = {
        lake_id: this.rowData.lake_id,
        ip: this.rowData.ip,
        page: this.pagination.page,
        page_size: this.pagination.page_size,
      };
      try {
        await http.get(`/sda/disks/abnormal/detail`, params).then((res) => {
          this.tableData = res && res.data && res.data.items;
          this.pagination.total = res && res.data && res.data.total;
        });
      } catch (error) {
        this.$message.error(error)
      }
    },
    handleSizeChange(val) {
      this.pagination.page = 1;
      this.pagination.page_size = val;
      this.getAbnormalDetail();
    },
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.getAbnormalDetail();
    },
    handleCancel() {
      this.$emit("close")
      this.$emit("refresh")
    },
  },
}
</script>

<style scoped lang="scss"></style>