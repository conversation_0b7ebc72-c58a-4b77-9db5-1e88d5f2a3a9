<template>
  <el-card>
    <ct-table
      :exportPartInfo="exportPartInfo"
      :exportAllName="exportAllName"
      :tableData="edgeTableData"
    ></ct-table>
  </el-card>
</template>
<script>

import listMixin from "@/utils/list-pager";
import hostGroupMixin from "@/views/redundancy-assessment/mixins/hostGroup.mixin";
import ctTable from '@/views/redundancy-assessment/host-group-redundancy/components/ctTable.vue'

export default {
  name: "edgeConfig",
  filters: {},
  mixins: [
    listMixin,
    hostGroupMixin
  ],
  components: {
    ctTable,
  },
  props: [],
  data() {
    let excelId = 'edge'
    let excelName = '边缘'
    return {
      form: {},
      edgeTableData: [], // 表格数据
      exportPartInfo: {
        excelId: excelId,
        excelName: excelName
      },
      exportAllName: '边缘',
    };
  },
  computed: {},
  created() {
    this.$eventBus.$on("edgeTableData", (data) => {
			this.edgeTableData = data
		})
  },
  beforeDestroy() {
    this.$eventBus.$off('edgeTableData');
  },
  watch: {},
  methods: {},
};
</script>
<style scoped lang="scss">
</style>
