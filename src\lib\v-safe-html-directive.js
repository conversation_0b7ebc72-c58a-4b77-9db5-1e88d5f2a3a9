/**
 * 将v-html改为v-safe-html
 */
import DOMPurify from 'dompurify'

export default {
  install(Vue) {
    function directiveUpdateComponent(el, binding) {
      let sanitizeRules = null
      const current_value = binding.value
      if (binding.oldValue === current_value) {
        return
      }
      if (typeof current_value === 'number') {
       el.innerHTML = current_value.toString()
       return
      }
      // 设置修复符.plaintext过滤所有html
      if (binding.modifiers.plaintext) {
        sanitizeRules = {
          ALLOWED_TAGS: [],
        }
      } else if (binding.arg) {
        sanitizeRules = binding.arg
      }
      el.innerHTML = DOMPurify.sanitize(current_value, sanitizeRules)
    }

    Vue.directive('safe-html', {
      bind: directiveUpdateComponent,
      update: directiveUpdateComponent
    })
  }
}