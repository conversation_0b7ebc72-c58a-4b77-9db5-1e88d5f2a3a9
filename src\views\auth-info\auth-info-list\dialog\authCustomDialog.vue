<template>
  <el-dialog title="新增自定义认证信息" :close-on-click-modal="false" :close-on-press-escape="false" :visible.sync="dialogVisible" width="500px" append-to-body @close="handleClose">
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="供应商" prop="supplier_id">
        <el-select v-model="form.supplier_id" placeholder="请选择供应商" style="width: 100%">
          <el-option
            v-for="supplier in supplierList"
            :key="supplier.id"
            :label="`${supplier.supplier_name} (${supplier.supplier_code})`"
            :value="supplier.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="IP" prop="ip">
        <el-input v-model="form.ip" placeholder="请输入IP地址"></el-input>
      </el-form-item>
      <el-form-item label="账号" prop="account">
        <el-input v-model="form.account" placeholder="请输入账号"></el-input>
      </el-form-item>
      <el-form-item label="密码" prop="pwd">
        <el-input v-model="form.pwd" type="password" placeholder="请输入密码" show-password></el-input>
      </el-form-item>
      <el-form-item label="认证信息" prop="auth_key">
        <el-input v-model="form.auth_key" type="password" placeholder="请输入认证信息" show-password></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import http from "../../http"
import { ipv4Regexp } from "@/utils/regexp"
import { encryptPassword } from "@/utils/crypto"

export default {
  name: "authCustomDialog",
  data() {
    // IP验证规则
    const validateIp = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入IP地址'))
      }
      if (!ipv4Regexp.test(value)) {
        return callback(new Error('请输入正确的IP地址格式'))
      }
      callback()
    }

    return {
      dialogVisible: true,
      loading: false,
      supplierList: [],
      form: {
        supplier_id: '',
        ip: '',
        account: '',
        pwd: '',
        auth_key: ''
      },
      rules: {
        supplier_id: [
          { required: true, message: '请选择供应商', trigger: 'change' }
        ],
        ip: [
          { required: true, message: '请输入IP地址', trigger: 'blur' },
          { validator: validateIp, trigger: 'blur' }
        ],
        account: [
          { required: true, message: '请输入账号', trigger: 'blur' },
        ],
        pwd: [
          { required: true, message: '请输入密码', trigger: 'blur' }
        ],
        auth_key: [
          { required: true, message: '请输入认证信息', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getSupplierList()
  },
  methods: {
    // 获取供应商列表
    async getSupplierList() {
      try {
        const res = await http.getSupplierList({
          page_size: 1000  // 获取所有供应商
        })
        if (res && res.code === 100000) {
          this.supplierList = res.data?.items || []
        }
      } catch (error) {
        console.error('获取供应商列表失败:', error)
      }
    },
    handleClose() {
      this.$refs.form.resetFields()
      this.$emit('close')
    },
    async handleSubmit() {
      try {
        await this.$refs.form.validate()
        this.loading = true

        // 加密密码和认证信息
        const encryptedPwd = encryptPassword(this.form.pwd)
        const encryptedAuthKey = encryptPassword(this.form.auth_key)

        const params = {
          supplier_id: this.form.supplier_id,
          ip: this.form.ip,
          account: this.form.account,
          pwd: encryptedPwd,
          auth_key: encryptedAuthKey
        }

        const res = await http.addCustomAuthInfo(params)
        if (res && res.code === 100000) {
          this.$message.success('新增成功')
          this.dialogVisible = false
          this.$emit('refresh')
        }
      } catch (error) {
        console.error('新增失败:', error)
        this.$message.error('新增失败，请检查输入信息或网络连接')
      } finally {
        this.loading = false
      }
    }
  }
}
</script> 
