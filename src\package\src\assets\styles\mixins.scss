@import './var.scss';

@mixin center {
  display: flex;
  justify-content: center;
  align-items: center;
}

// 全局字体
@mixin global-font-family {
  font-family: $font-family-chinese, $font-family-number;
}

$font-presets: (
    "title": (
        "font-size": $font-size-largest,
        "color": $font-color-primary
    ),
    "subtitle": (
        "font-size": $font-size-larger,
        "color": $font-color-primary
    ),
    "text": (
        "font-size": $font-size-normal,
        "color": $font-color-primary
    ),
    "text-1": (
        "font-size": $font-size-normal,
        "color": $font-color-primary2
    ),
    "text-2": (
        "font-size": $font-size-normal,
        "color": $font-color-secondary
    ),
    "font-text-secondary-1": (
        "font-size": $font-size-normal,
        "color": $font-color-secondary2
    ),
    "font-text-secondary-2": (
        "font-size": $font-size-small,
        "color": $font-color-primary
    )
);

@mixin use-font-preset($preset) {
  $font: map_get($font-presets, $preset);

  @include global-font-family;
  font-size: map_get($font, "font-size");
  color: map_get($font, "color");
}

//@each $name in map_keys($font-presets) {
//  .font-#{#name} {
//    @include use-font-preset($name);
//  }
//}
// 大标题
@mixin font-title {
  //font-size: $font-size-largest;
  //color: $font-color-primary;
  @include use-font-preset("title");
}

// 小标题
@mixin font-subtitle {
  //font-size: $font-size-larger;
  //color: $font-color-primary;
  @include use-font-preset("subtitle");
}

// 正文
@mixin font-text {
  //$font: map_get($font-presets, "text");
  //
  //font-size: map_get($font, 'font-size');
  //color: map_get($font, 'color');
  //font-size: $font-size-normal;
  //color: $font-color-primary;
  @include use-font-preset("text");
}

// 辅助文字1
@mixin font-text-1 {
  //@include global-font-family;
  //
  //font-size: $font-size-normal;
  //color: $font-color-primary2;
  @include use-font-preset("text-1");
}

// 辅助文字2
@mixin font-text-2 {
  //@include global-font-family;
  //
  //font-size: $font-size-normal;
  //color: $font-color-secondary;
  @include use-font-preset("text-2");
}

// 次要文字1
@mixin font-text-secondary-1 {
  //@include global-font-family;
  //
  //font-size: $font-size-normal;
  //color: $font-color-secondary2;
  @include use-font-preset("text-secondary-1");
}

// 次要文字2
@mixin font-text-secondary-2 {
  //@include global-font-family;
  //
  //font-size: $font-size-small;
  //color: $font-color-primary;
  @include use-font-preset("text-secondary-2");
}

// input组件通用属性
@mixin is-input {
  & + * {
    margin-left: $input-spacing;
  }
}

@mixin use-icon-font {
  &.el-icon-success:before {
    font-family: iconfont, serif;
    content: "\e6a5";
  }
  &.el-icon-warning:before {
    font-family: iconfont, serif;
    content: "\e6ac";
  }
  &.el-icon-info:before {
    font-family: iconfont, serif;
    content: "\e6a9";
  }
  &.el-icon-error:before {
    font-family: iconfont, serif;
    content: "\e6aa";
  }
}
