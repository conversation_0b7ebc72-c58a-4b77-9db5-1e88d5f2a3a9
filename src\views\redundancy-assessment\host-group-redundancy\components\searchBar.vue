<template>
  <div class="container-head">
    <el-form inline label-width="108px" :show-message="false">
      <el-form-item label="资源池" prop="template_ids" label-width="55px">
        <el-select
          v-model="queryForm.template_ids"
          placeholder="资源池"
          filterable
          style="width: 200px;"
          multiple
        >
          <el-option 
            v-for="item in resource_pool_list"
            :key="item.template_id"
            :label="item.template_name" 
            :value="item.template_id + ''" 
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="时间" prop="id" label-width="60px">
        <el-date-picker
          v-model="timeRange"
          type="datetimerange"
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="timestamp"
          :default-time="['00:00:00', '23:59:59']"
          :picker-options="pickerOptions"
          style="width:340px"
        />
      </el-form-item>
      <el-form-item label="考虑上下架节点" prop="include_future_lake" label-width="108px">
        <el-select v-model="queryForm.include_future_lake" filterable placeholder="" clearable style="width: 68px;">
          <el-option v-for="item in include_future_lake_list" :label="item.label" :value="item.value" :key="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="Lake带宽比例" prop="lake_weight" label-width="108px">
        <el-input v-model.number="queryForm.lake_weight" placeholder="" style="width: 68px;"></el-input>
      </el-form-item>
      <el-form-item label="组能力比例" prop="host_group_weight" label-width="90px">
        <el-input v-model.number="queryForm.host_group_weight" placeholder="" style="width: 68px;"></el-input>
      </el-form-item>
      <el-form-item label-width="108px">
        <el-button type="primary" size="medium" @click="handleSearch" :loading="loading" :disabled="loading">查询</el-button>
        <el-button size="medium" @click="resetForm" :loading="loading" :disabled="loading">重置</el-button>
      </el-form-item>
      <!-- 前端自己过滤的查询条件 -->
      <el-collapse v-model="activeNames" ref="collapse" class="collapse-style">
        <el-collapse-item name="1" :title="collapseTitle">
          <div>
            <el-form-item label="大区" prop="area_view" label-width="108px">
              <el-input v-model="searchForm.area_view" placeholder="请输入大区" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="省份" prop="province" label-width="108px">
              <el-input v-model="searchForm.province" placeholder="请输入省份" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="Lake名称" prop="lake_name" label-width="108px">
              <el-input v-model="searchForm.lake_name" placeholder="请输入Lake名称" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="组名称" prop="host_group" label-width="108px">
              <el-input v-model="searchForm.host_group" placeholder="请输入组名称" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="资源分组" prop="resource_groups" label-width="108px">
              <el-input v-model="searchForm.resource_groups" placeholder="请输入资源分组" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="组层级" prop="hg_level" label-width="108px">
              <el-select v-model="searchForm.hg_level" class="input-style" placeholder="请选择组层级" clearable>
                <el-option :value="1" label="边缘"></el-option>
                <el-option :value="2" label="一层父"></el-option>
                <el-option :value="3" label="二层父"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="运营商" prop="isp" label-width="108px">
              <el-input v-model="searchForm.isp" placeholder="请输入运营商" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="Lake出口带宽" prop="lake_upper_limit_bw" label-width="108px">
              <el-input v-model="searchForm.lake_upper_limit_bw" placeholder="请输入Lake出口带宽" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="rs数" prop="rip_count" label-width="108px">
              <el-input v-model="searchForm.rip_count" placeholder="请输入rs数" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="正常rs数" prop="healthy_rip_count" label-width="108px">
              <el-input v-model="searchForm.healthy_rip_count" placeholder="请输入正常rs数" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="内存" prop="memory" label-width="108px">
              <el-input v-model="searchForm.memory" placeholder="请输入内存" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="cpu核数" prop="cpu" label-width="108px">
              <el-input v-model="searchForm.cpu" placeholder="请输入cpu核数" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="磁盘" prop="disk" label-width="108px">
              <el-input v-model="searchForm.disk" placeholder="请输入磁盘" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="额定单机" prop="rip_rated_bw" label-width="108px">
              <el-input v-model="searchForm.rip_rated_bw" placeholder="请输入额定单机" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="健康值" prop="qfc" label-width="108px">
              <el-input v-model="searchForm.qfc" placeholder="请输入健康值" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="组额定能力" prop="hg_rated_bw" label-width="108px">
              <el-input v-model="searchForm.hg_rated_bw" placeholder="请输入组额定能力" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="组动态额定" prop="hg_dyn_rated_bw" label-width="108px">
              <el-input v-model="searchForm.hg_dyn_rated_bw" placeholder="请输入组动态额定" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="组额定qps" prop="hg_rated_qps" label-width="108px">
              <el-input v-model="searchForm.hg_rated_qps" placeholder="请输入组额定qps" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="组业务带宽" prop="hg_cur_bw" label-width="108px">
              <el-input v-model="searchForm.hg_cur_bw" placeholder="请输入组业务带宽" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="组折算带宽" prop="hg_dis_cur_bw" label-width="108px">
              <el-input v-model="searchForm.hg_dis_cur_bw" placeholder="请输入组折算带宽" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="峰值qps" prop="hg_peak_qps" label-width="108px">
              <el-input v-model="searchForm.hg_peak_qps" placeholder="请输入峰值qps" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="组能力冗余" prop="hg_redu_bw" label-width="108px">
              <el-input v-model="searchForm.hg_redu_bw" placeholder="请输入组能力冗余" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="组动态能力冗余" prop="hg_dyn_redu_bw" label-width="108px">
              <el-input v-model="searchForm.hg_dyn_redu_bw" placeholder="请输入组动态能力冗余" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="组冗余qps" prop="hg_redu_qps" label-width="108px">
              <el-input v-model="searchForm.hg_redu_qps" placeholder="请输入组冗余qps" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="Lake带宽冗余" prop="lake_redu_bw" label-width="108px">
              <el-input v-model="searchForm.lake_redu_bw" placeholder="请输入Lake带宽冗余" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="是否存在无效字段" prop="has_invalid_field" label-width="108px">
              <el-select v-model="searchForm.has_invalid_field" clearable class="input-style" placeholder="是否存在无效字段">
                <el-option :value="true" label="是"></el-option>
                <el-option :value="false" label="否"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label-width="420px">
              <span slot="label" class="tip-wrap">{{ tip }}</span>
            </el-form-item>
          </div>
        </el-collapse-item>
      </el-collapse>

    </el-form>

  </div>
</template>
<script>

import listMixin from "@/utils/list-pager";
import httpApi from '@/api/http.js'
import copyIpMixin from "@/utils/mixins/copyIp.mixin";
import ExportExcelCommon from '@/helper/exportExcelCommon'
import { download } from "@/utils/downloadCsv";
import edgeConfig from "@/views/redundancy-assessment/host-group-redundancy/components/edgeConfig.vue";
import firstParentConfig from "@/views/redundancy-assessment/host-group-redundancy/components/firstParentConfig.vue";
import secondParentConfig from "@/views/redundancy-assessment/host-group-redundancy/components/secondParentConfig.vue";
import hostGroupMixin from "@/views/redundancy-assessment/mixins/hostGroup.mixin";
import { getAm0 as get0 } from "@/utils/time";
import { shortcuts_arr} from "@/utils/pickerOption.js"

// 获取近7天的时间戳
function get7d() {
  const today = get0(new Date());
  let current = new Date()
  let timeStamp = current.getTime()
  return [new Date(+today - 6 * 24 * 60 * 60 * 1000), new Date(timeStamp - 10 * 60 * 1000)];
  // return [new Date(+today - 6 * 24 * 60 * 60 * 1000), new Date(+today + 24 * 60 * 60 * 1000 - 1)];
}

export default {
  name: "host-group-redundancy",
  mixins: [listMixin, copyIpMixin, hostGroupMixin],
  components: {
    ExportExcelCommon,
    download,
    edgeConfig,
    firstParentConfig,
    secondParentConfig,
    shortcuts_arr
  },
  props: [],
  data() {
    return {
      loading: false,
      tip: "提示：列表结果为数字的，查询支持 >、< 或者 =，只填数字表示等于该值",
      activeNames: [],
      timeRange: get7d(),
      resource_pool_list: [],
      include_future_lake_list: [
        { label: "否", value: false },
        { label: "是", value: true }
      ],
      queryForm: {
        template_ids: ['59'], // 资源池
        lake_weight: 85, // Lake带宽比例
        host_group_weight: 100, // 组能力比例
        include_future_lake: false, // 考虑上下架节点
      },
      searchForm: {
        area_view: "",
        province: "",
        lake_name: "",
        host_group: "",
        resource_groups: "",
        hg_level: "",
        isp: "",
        lake_upper_limit_bw: "",
        rip_count: "",
        healthy_rip_count: "",
        memory: "",
        cpu: "",
        disk: "",
        rip_rated_bw: "",
        qfc: "",
        hg_rated_bw: "",
        hg_dyn_rated_bw: "",
        hg_rated_qps: "",
        hg_cur_bw: "",
        hg_dis_cur_bw: "",
        hg_peak_qps: "",
        hg_redu_bw: "",
        hg_dyn_redu_bw: "",
        hg_redu_qps: "",
        lake_redu_bw: "",
      },
      paging: {
        total: 0,
        page: 1,
        page_size: 10
      },
      selectDataList: [],
      maxDayBeforeNow: 30,
      pickerOptions: {
        shortcuts: shortcuts_arr,
        // 设置禁用时间
        disabledDate: (time) => {
          // 获取今天0点数据
          const today0 = get0(new Date());
          // 禁用大于今天时间23：59：59
          if (+time > +new Date(+today0 + 24 * 60 * 60 * 1000 - 1)) return true;
          // 禁用小于设置的最早期限的，由于今天会算1天，需要倒推 x-1 天
          if (+time < +today0 - (this.maxDayBeforeNow- 1) * 24 * 60 * 60 * 1000) return true;
          return false;
        },
        onPick: ({ minDate }) => {
          this.minDate = +minDate;
        },
        reset: () => {
          this.minDate = null;
        },
      },
    };
  },
  watch: {
    searchForm: {
      deep: true,
      handler(val) {
        this.$eventBus.$emit("searchFormData", this.searchForm);
      },
      immediate: true
    }
  },
  computed: {
    collapseTitle() {
      return this.activeNames.includes("1") ? "收起其它查询条件" : "展开其它查询条件";
    },
  },
  filters: {},
  mounted() {
    this.getResourcePool()
    // this.handleSearch()
  },
  methods: {
    getResourcePool() {
      httpApi.get(`/rap/resource_pool`)
        .then((res) => {
          this.resource_pool_list = res
        })
    },
    resetForm() {
      this.queryForm = this.$options.data().queryForm;
      this.searchForm = this.$options.data().searchForm;
      this.timeRange = get7d()
    },
  },
};
</script>
<style scoped lang="scss">
.collapse-style {
  text-align: left;
  /deep/ {
    .el-collapse-item__header {
      border: 0;
      background-color: #f7f8fa;
      margin-bottom: 20px;
      padding-left: 12px;
      height: 25px;
    }
  }
}
.tip-wrap {
  font-size: 12px;
  color: #a8adaf;
}
.input-style {
  width: 165px;
}
</style>
