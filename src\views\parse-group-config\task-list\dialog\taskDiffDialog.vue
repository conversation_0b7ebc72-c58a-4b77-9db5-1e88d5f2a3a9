<template>
  <el-dialog
    append-to-body
    title=""
    :visible="true"
    :close-on-click-modal="false"
    width="1300px"
    @close="handleCancel"
  >
    <el-row>
      <el-table :data="tableData" v-loading="submiting" :span-method="listSpanMethod" :cell-style="listCellStyle" border>
        <el-table-column prop="pg_name" label="解析组" align="center" width="200"></el-table-column>
        <el-table-column prop="view_name" label="区域" align="center"></el-table-column>
        <el-table-column prop="v4_peak" label="v4峰值带宽" align="center">
          <template slot-scope="scope">
            <span>{{ parseBwByteString(scope.row.v4_peak) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="v6_peak" label="v6峰值带宽" align="center">
          <template slot-scope="scope">
            <span>{{ parseBwByteString(scope.row.v6_peak) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="diff_detail" label="删除" align="left" width="400">
          <template slot-scope="scope">
            <div v-for="(item, index) in scope.row.diff_detail" :key="index">
              <span class="text-red" v-if="item.op === 1"><span style="margin-left:8px">{{ item.content }}</span></span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="diff_detail" label="新增" align="left" width="400">
          <template slot-scope="scope">
            <div v-for="(item, index) in scope.row.diff_detail" :key="index">
              <span class="text-green" v-if="item.op === 0"><span style="margin-left:8px">{{ item.content }}</span></span>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-row>

    <div slot="footer" class="dialog-footer" v-if="dialogType === 'submit'">
      <el-button size="medium" @click="handleCancel">取 消</el-button>
      <el-button size="medium" type="primary" @click="handleSubmit" :loading="submiting" :disabled="submiting">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import http from '@/api/http.js'
import commonMixin from "@/utils/common";

export default {
  components: {},
  mixins: [
    commonMixin
  ],
  props: {
    rowData: {
      type: Object,
      default: null
    },
    dialogType: {
      type: String,
      default: ""
    },
    multipleSelection: {
      type: Array,
      default: []
    },
  },
  data() {
    return {
      submiting: false,
      tableData: [],
    }
  },
  computed: {},
  mounted() {
    if (this.multipleSelection && this.multipleSelection.length > 0) {
      this.handleDetailDiff()
    }
  },
  methods: {
    async handleDetailDiff() {
      let ids = this.multipleSelection && this.multipleSelection.map(item => item.id)
      let params = {
        detail_ids: ids,
      };
      const res = await http.post(`/sda/change_cover/detail/diff`, params)
      let result = (res && res.data) || []
      const data = this.handleTableData(result)
      this.tableData = data
    },
    handleTableData(data) {
      const arrData = []
      for (let i = 0; i < data.length; i++) {
        let diff = data[i].diff
        if (!diff) {
          diff = [{}]
        }
        for (let j = 0; j < diff.length; j++) {
          const info = {
            span_num: j === 0 ? diff.length : 0,
            pg_id: data[i].pg_id,
            pg_name: data[i].pg_name,
            diff_detail: diff[j].diff_detail,
            v4_peak: diff[j].v4_peak,
            v6_peak: diff[j].v6_peak,
            view_id: diff[j].view_id,
            view_name: diff[j].view_name,

          }
          arrData.push(info)
        }
      }
      return arrData;
    },
    async handleSubmit() {
      this.submiting = true
      const ids = this.multipleSelection && this.multipleSelection.map(item => item.id)
      let params = {
        detail_ids: ids,
      };
      let res = await http.post(`/sda/change_cover/task/create`, params)
      let successMsg = "保存成功"
      if (res && res.code === 100000) {
        this.$message.success(successMsg);
        this.$emit("close")
        this.$emit("refresh")
        this.submiting = false
      } else {
        this.submiting = false
      }
    },
    handleCancel() {
      this.$emit("close")
    },
    listSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex < 1) {
        if (row.span_num > 0) {
          return {
            rowspan: row.span_num,
            colspan: 1,
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          };
        }
      }
    },
    listCellStyle({ row, column, rowIndex, columnIndex }) {
      if (columnIndex < 1) {
        return {
          backgroundColor: "transparent",
        };
      }
    },
  },
}
</script>

<style scoped lang="scss">
.text-green {
  color: #1ac45d;
}
.text-red {
  color: #df0629;
}
</style>