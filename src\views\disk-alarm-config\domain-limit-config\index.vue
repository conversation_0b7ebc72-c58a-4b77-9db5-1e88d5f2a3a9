<template>
  <el-card>
    <div slot="header" class="clearfix">
      <span>域名限制配置</span>
    </div>
    <!-- 查询条件 -->
    <el-form :model="searchForm" ref="searchForm" label-position="left" inline>
      <el-row>
        <el-form-item label="LAKE名称">
          <el-select v-model="searchForm.lake_id" placeholder="请选择LAKE名称" style="width:220px" filterable clearable>
            <el-option :value="-1" label="ALL" />
            <el-option v-for="(item, index) in lakeList" :key="index" :label="item.bk_inst_name" :value="item.bk_inst_id"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="域名">
          <el-input v-model="searchForm.domain" placeholder="请输入域名" style="width:220px" clearable> </el-input>
        </el-form-item>
        
        <el-form-item>
          <el-button size="medium" type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
        <span style="float:right">
          <el-button size="medium" @click="handleAdd">新增</el-button>
        </span>
      </el-row>
    </el-form>

    <el-table :data="tableData" v-loading="querying">
      <el-table-column prop="lake_name" label="LAKE" align="center"></el-table-column>
      <el-table-column prop="domain" label="域名" align="center"></el-table-column>
      <el-table-column prop="account_id" label="客户ID" align="center"></el-table-column>
      <el-table-column prop="operator" label="操作人" align="center"></el-table-column>
      <el-table-column label="操作" align="center" width="130">
        <template slot-scope="scope">
          <div>
            <el-button type="text" @click="handleEdit(scope.row)">修改</el-button>
            <el-button type="text" @click="handleDelete(scope.row)">删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-row style="text-align: center; padding-top: 20px">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.page_size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      ></el-pagination>
    </el-row>

    <el-row>
      <!-- 新增 -->
      <domain-limit-dialog
        v-if="addDialog"
        @close="addDialog = false"
        @refresh="onSearch"
      ></domain-limit-dialog>
      <!-- 修改 -->
      <domain-limit-dialog
        v-if="editDialog"
        :is-edit="true"
        :rowData="rowData"
        @close="editDialog = false"
        @refresh="onSearch"
      ></domain-limit-dialog>
    </el-row>

  </el-card>
</template>
<script>

import http from "@/api/http.js"
import domainLimitDialog from "@/views/disk-alarm-config/domain-limit-config/dialog/domainLimitDialog.vue"
import { mapState } from "vuex";
import dateFormat from "@/utils/dateFormat"

export default {
  name: "domain-limit-config",
  components: {
    domainLimitDialog,
  },
  filters: {
    dateFormat: dateFormat
  },
  props: [],
  data() {
    return {
      querying: false,
      tableData: [],
      rowData: {},
      addDialog: false,
      editDialog: false,
      pagination: {
        total: 0,
        page: 1,
        page_size: 20,
      },
      searchForm: {
        lake_id: '',
        domain: '',
      },
    };
  },
  computed: {
    ...mapState({
      lakeList: (state) => state.baseData.lakeList,
    }),
  },
  watch: {},
  created() {},
  mounted() {
    this.onSearch()
  },
  methods: {
    handleAdd() {
      this.addDialog = true
    },
    handleEdit(row) {
      this.editDialog = true
      this.rowData = structuredClone(row)
    },
    async handleDelete(row) {
      await this.$confirm("确定删除该条数据吗？", "提示", {
        type: "warning",
      })
      let params = {
        operator: window.localStorage.getItem('userInfo'),
      };
      const res = await http.delete(`/sda/requests/lakes/domains/limit/${row.id}`, params)
      if (res && res.code === 100000) {
        let msg = (res && res.message) || "删除成功"
        this.$message.success(msg);
        this.onSearch()
      }
    },
    onSearch() {
      this.pagination.page = 1;
      this.query();
    },
    async query() {
      let params = {
        lake_id: this.searchForm.lake_id,
        domain: this.searchForm.domain,
        page: this.pagination.page,
        page_size: this.pagination.page_size,
      };
      this.querying = true;
      try {
        await http.get(`/sda/requests/lakes/domains/limit`, params).then((res) => {
          this.tableData = res && res.data && res.data.items;
          this.pagination.total = res && res.data && res.data.total;
          this.querying = false;
        });
      } catch (error) {
        this.querying = false;
      } finally {
        this.querying = false;
      }
    },
    handleSizeChange(val) {
      this.pagination.page = 1;
      this.pagination.page_size = val;
      this.query();
    },
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.query();
    },
  },
};
</script>
<style scoped lang="scss">
.btn {
  float: right;
}
</style>
