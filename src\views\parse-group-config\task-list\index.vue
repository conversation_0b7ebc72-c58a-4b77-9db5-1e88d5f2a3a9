<template>
  <el-card>
    <!-- 任务列表 -->
    <!-- 查询条件 -->
    <el-form :model="searchForm" ref="searchForm" label-position="left" inline>
      <el-row>
        <el-form-item label="">
          <el-select v-model="searchForm.state" placeholder="请选择" style="width:120px" filterable @change="onStateChange">
            <el-option value="0" label="配置中"></el-option>
            <el-option value="1" label="审核中"></el-option>
            <el-option value="2" label="已提交"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="" prop="pg_id">
          <el-select v-model="searchForm.pg_id" placeholder="请选择解析组名称" filterable clearable style="width:300px">
            <el-option v-for="(item, index) in parseGroupList" :key="index" :label="item.pg_name" :value="item.pg_id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="" v-if="searchForm.state !== '0'">
          <el-input v-model="searchForm.task_name" clearable placeholder="请输入任务编号"></el-input>
        </el-form-item>
        <el-form-item label="" v-if="searchForm.state === '0'">
          <el-input v-model="searchForm.id" clearable placeholder="请输入子任务id"></el-input>
        </el-form-item>
        
        <el-form-item>
          <el-button size="medium" type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
        <span style="float:right" v-if="searchForm.state === '0'">
          <el-button size="medium" @click="openTaskDialog('preview')">任务预览</el-button>
          <el-button size="medium" type="primary" @click="openTaskDialog('submit')">任务提交</el-button>
        </span>
      </el-row>
    </el-form>

    <el-table
      :data="tableData"
      ref="tableData"
      v-loading="querying"
      :span-method="listSpanMethod"
      :cell-style="listCellStyle"
      @selection-change="handleSelectionChange"
      @row-click="rowClick"
      border
    >
      <el-table-column v-if="searchForm.state === '0'" type="selection" align="center" width="55" :key="Math.random()"></el-table-column>
      <el-table-column v-if="searchForm.state !== '0'" prop="task_name" label="任务编号" align="center" key="task_name"></el-table-column>
      <el-table-column v-if="searchForm.state === '2'" prop="submit_time" label="提交时间" align="center" key="submit_time" :formatter="dateFormatUTCInTable"></el-table-column>
      <el-table-column v-if="searchForm.state === '2'" prop="finish_time" label="任务完成时间" align="center" key="finish_time" :formatter="dateFormatUTCInTable"></el-table-column>
      <el-table-column v-if="searchForm.state !== '0'" prop="operator_name" label="任务提交人" align="center" key="operator_name"></el-table-column>
      <el-table-column v-if="searchForm.state === '2'" prop="auditor_name" label="审核人" align="center" key="auditor_name"></el-table-column>
      <el-table-column prop="id" label="子任务ID" align="center" key="sub_task_id"></el-table-column>
      <el-table-column prop="parse_group_name" label="解析组名称" align="center" key="parse_group_name">
        <template slot-scope="scope">
          <span :class="{ 'text-wrap': searchForm.state === '2' }" @click="searchForm.state === '2' && handleCheck(scope.row, 'parseGroup')">{{ scope.row.parse_group_name }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="searchForm.state === '0'" prop="editor_name" label="提交人" align="center" key="editor_name"></el-table-column>
      <el-table-column v-if="searchForm.state === '2'" prop="result" label="子任务状态" align="center" key="result" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.result === 0" type="info">执行中</el-tag>
          <el-tag v-if="scope.row.result === 1" type="success">成功</el-tag>
          <el-tooltip content="" v-if="scope.row.result === 2">
            <div slot="content">
              <div class="text-width">{{ scope.row.msg }}</div>
            </div>
            <el-tag v-if="scope.row.result === 2" type="danger">失败</el-tag>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="180" key="operate">
        <template slot-scope="scope">
          <div>
            <span v-if="searchForm.state === '0'">
              <el-button type="text" @click="handlePreview(scope.row)">预览</el-button>
              <el-button type="text" @click="showConfig(scope.row)">配置</el-button>
              <el-button type="text" @click="handleDelete(scope.row)">删除</el-button>
            </span>
            <el-button v-if="searchForm.state === '1'" type="text" @click="handleCheck(scope.row, 'check')">审核</el-button>
            <el-button v-if="searchForm.state === '2' && scope.row.result === 2" type="text" @click="handleRevert(scope.row)">重新配置</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-row style="text-align: center; padding-top: 20px">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.page_size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      ></el-pagination>
    </el-row>

    <el-row>
      <!-- 任务预览/任务提交 -->
      <task-diff-dialog
        v-if="taskDiffDialogVisible"
        :is-edit="true"
        :rowData="rowData"
        :dialogType="dialogType"
        :multipleSelection="multipleSelection"
        @close="taskDiffDialogVisible = false"
        @refresh="onSearch"
      ></task-diff-dialog>
      <!-- 审核中，点击：审核 -->
      <check-dialog
        v-if="checkDialogVisible"
        :checkRowData="checkRowData"
        :checkDialogType="checkDialogType"
        @close="checkDialogVisible = false"
        @refresh="onSearch"
      ></check-dialog>
    </el-row>

  </el-card>
</template>
<script>

import http from "@/api/http.js"
import taskDiffDialog from "@/views/parse-group-config/task-list/dialog/taskDiffDialog.vue"
import checkDialog from "@/views/parse-group-config/task-list/dialog/checkDialog.vue"
import dateFormat from "@/utils/dateFormat"
import commonMixin from "@/utils/common";

export default {
  name: "task-list",
  components: {
    taskDiffDialog,
    checkDialog,
  },
  mixins: [
    commonMixin
  ],
  filters: {
    dateFormat: dateFormat
  },
  props: [],
  data() {
    return {
      querying: false,
      tableData: [],
      checkDialogVisible: false, // 审核弹窗
      rowData: {},
      checkRowData: {},
      dialogType: '',
      checkDialogType: '',
      taskDiffDialogVisible: false,
      multipleSelection: [],
      parseGroupList: [],
      pagination: {
        total: 0,
        page: 1,
        page_size: 20,
      },
      searchForm: {
        task_name: '',
        id: '',
        pg_id: '',
        state: '1',
      },
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {
    this.onSearch()
    this.getParseGroupList()
  },
  methods: {
    // 任务提交/任务预览
    openTaskDialog(type) {
      if (!(this.multipleSelection && this.multipleSelection.length > 0)) {
        this.$message.warning("请先选择数据！")
        return;
      }
      this.dialogType = type
      this.taskDiffDialogVisible = true
    },
    // 点击：预览
    handlePreview(row) {
      this.multipleSelection = [row]
      this.taskDiffDialogVisible = true
    },
    // 重新配置
    async handleRevert(row) {
      let res = await http.get(`/sda/change_cover/detail/revert?id=${row.id}`)
      if (res && res.code === 100000) {
        this.$message.success("重新配置成功");
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = JSON.parse(JSON.stringify(val));
    },
    rowClick(row) {
      this.$refs.tableData.toggleRowSelection(row)
    },
    showConfig(row) {
      this.$router.push({name: 'parse-config', query: { id: row.parse_group_id, subTaskId: row.id, from: "taskList" }})
    },
    // 点击：审核
    handleCheck(row, type) {
      this.checkDialogType = type
      this.checkRowData = JSON.parse(JSON.stringify(row))
      this.checkDialogVisible = true
    },
    async handleDelete(row) {
      await this.$confirm("确定删除该条数据吗？", "提示", {
        type: "warning",
      })
      const res = await http.delete(`/sda/change_cover/detail/delete/${row.id}`)
      if (res && res.code === 100000) {
        let msg = (res && res.message) || "删除成功"
        this.$message.success(msg);
        this.onSearch()
      }
    },
    onStateChange() {
      this.query();
    },
    onSearch() {
      this.pagination.page = 1;
      this.query();
    },
    async query() {
      this.querying = true;
      let params = {
        pg_id: this.searchForm.pg_id,
        page: this.pagination.page,
        page_size: this.pagination.page_size,
      };
      if (this.searchForm.state === "0") {
        params.id = this.searchForm.id
      } else if (this.searchForm.state !== "0") {
        params.task_name = this.searchForm.task_name
      }
      let res = {}
      try {
        // 配置中
        if (this.searchForm.state === '0') {
          params.task_id = 0
          res = await http.get(`/sda/change_cover/detail/list`, params)
          this.tableData = res && res.data && res.data.items;
        } else {
          // 审核中/已提交：调用另一个接口
          params.state = 1
          if (this.searchForm.state === '2') {
            params.state = 2
          }
          res = await http.get(`/sda/change_cover/task/list`, params)
          let result = (res && res.data && res.data.items) || []
          const data = this.handleTableData(result)
          this.tableData = data
        }
        this.pagination.total = res && res.data && res.data.total;
        this.querying = false;
      } catch (error) {
        this.querying = false;
      } finally {
        this.querying = false;
      }
    },
    handleTableData(data) {
      const arrData = []
      for (let i = 0; i < data.length; i++) {
        let detail = data[i].detail
        if (!detail) {
          detail = [{}]
        }
        for (let j = 0; j < detail.length; j++) {
          const info = {
            span_num: j === 0 ? detail.length : 0,
            auditor_id: data[i].auditor_id,
            auditor_name: data[i].auditor_name,
            create_time: data[i].create_time,
            finish_time: data[i].finish_time,
            operator_id: data[i].operator_id,
            operator_name: data[i].operator_name,
            state: data[i].state,
            submit_time: data[i].submit_time,
            task_id: data[i].task_id,
            task_name: data[i].task_name,
            update_time: data[i].update_time,
            detail: data[i].detail,
            id: detail[j].id,
            parse_group_id: detail[j].parse_group_id,
            parse_group_name: detail[j].parse_group_name,
            result: detail[j].result,
            msg: detail[j].msg,
            subTaskId: detail[j].task_id,
          }
          arrData.push(info)
        }
      }
      return arrData;
    },
    listSpanMethod({ row, column, rowIndex, columnIndex }) {
      if ((this.searchForm.state === '1' && (columnIndex < 2 || columnIndex > 3)) || (this.searchForm.state === '2' && (columnIndex < 5 || columnIndex > 8))) {
        if (row.span_num > 0) {
          return {
            rowspan: row.span_num,
            colspan: 1,
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          };
        }
      }
    },
    listCellStyle({ row, column, rowIndex, columnIndex }) {
      if ((this.searchForm.state === '1' && (columnIndex < 2 || columnIndex > 3)) || this.searchForm.state === '2' && (columnIndex < 5 || columnIndex > 8)) {
        return {
          backgroundColor: "transparent",
        };
      }
    },
    handleSizeChange(val) {
      this.pagination.page = 1;
      this.pagination.page_size = val;
      this.query();
    },
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.query();
    },
    getParseGroupList() {
      http.get(`/sda/parse_group/get`).then(res => {
        this.parseGroupList = (res && res.data && res.data.items) || []
      })
    },
  },
};
</script>
<style scoped lang="scss">
.btn {
  float: right;
}
.text-wrap {
  cursor: pointer;
}
.text-btn {
  color: #FF9831;
  margin-left: 4px;
}
.text-width {
  max-width: 1200px;
}
</style>
