/* eslint-disable no-undef */
import axios from 'axios';
import QS from 'qs';

export const iamURL = window.api.iamURL;
export const apiURL = process.env.NODE_ENV === 'development' ? window.api.apiURLLocal : window.api.apiURL;

export const ajax = axios.create({
  baseURL: apiURL,
  timeout: 30000
})
export const ajaxAuth = axios.create({
  baseURL: window.api.apiAuthURL,
  timeout: 30000
})

ajax.interceptors.request.use(config => {
  return config
}, error => {
  return Promise.reject(error)
})

ajax.interceptors.response.use(response => {
  let code = response.status || response.data.code
  // let message = response.data && response.data.message
  if (code === 100000 || code === 200) {
    return Promise.resolve(response)
  } else {
    return Promise.reject(response)
  }
}, error => {
  return Promise.reject(error?.response || error)
})
// iam
export const iamAjax = axios.create({
  baseURL: iamURL,
  timeout: 30000,
  paramsSerializer: function (params) {
    return QS.stringify(params, {
      arrayFormat: 'repeat'
    })
  }
})
// 重构后的资源 拦截器：在请求或响应被 then 或 catch 处理前拦截
iamAjax.interceptors.request.use(config => {
  return config
}, error => {
  return Promise.reject(error)
})

iamAjax.interceptors.response.use(response => {
  let code = response.data.code || response.data.status.code
  let message = response.data || response.data.message
  if (code == 100000 || code == 0) {
    return Promise.resolve(response)
  } else {
    return Promise.reject(message)
  }
}, error => {
  return Promise.reject(error)
})

let cancel = null
const requestList = []
const CancelToken = axios.CancelToken
const defaultConfig = {
  baseURL: apiURL,
  timeout: 30000
}
const http = axios.create(defaultConfig)
// 取消请求
function cancelRequest(url, message) {
  message = message || '阻止重复请求'
  requestList.indexOf(url) > -1 && cancel({
    message: message,
    url: url
  })
  requestList.push(url)
}

// 允许请求
function allowRequest(url, ms = 500) {
  setTimeout(() => {
    let list = requestList.slice(0)
    list.forEach((item, index) => {
      item === url && requestList.splice(index, 1)
    })
  }, ms)
}

function notify(message, title = '错误提示', type = 'error') {
  Notification({
    type: type,
    title: title,
    message: message,
    position: 'bottom-right'
  })
}

// 拦截器：在请求或响应被 then 或 catch 处理前拦截
http.interceptors.request.use(config => {
  allowRequest(config.url)
  return config
}, error => {
  return Promise.reject(error)
})

http.interceptors.response.use(response => {
  let code = response.data && response.data.status && response.data.status.code
  let message = response.data && response.data.status && response.data.status.message
  switch (code) {
    case 400:
      notify(message)
      break
    default: //
  }
  return response
}, error => {
  let code = error.response && error.response.status
  let message = error.response && error.response.statusText
  switch (code) {
    case 400:
      notify(message)
      break
    case 401:
      location.href = '/v1/auth/logout' // IAM登出
      break
    case 404:
      notify(message)
      break
    case 500:
      notify(message)
      break
    case 502:
      notify(message)
      break
    case 503:
      notify(message)
      break
    case 504:
      notify(message)
      break
    default: //
  }
  return Promise.reject(error)
})

export default {
  get(url, config = {}, canCancel = true, cancelMessage) {
    config.cancelToken = new CancelToken(c => cancel = c)
    canCancel && cancelRequest(url, cancelMessage)
    return http.get(url, Object.assign(defaultConfig, config))
  },
  delete(url, config = {}, canCancel = true, cancelMessage) {
    config.cancelToken = new CancelToken(c => cancel = c)
    canCancel && cancelRequest(url, cancelMessage)
    return http.delete(url, Object.assign(defaultConfig, config))
  },
  head(url, config = {}, canCancel = true, cancelMessage) {
    config.cancelToken = new CancelToken(c => cancel = c)
    canCancel && cancelRequest(url, cancelMessage)
    return http.head(url, Object.assign(defaultConfig, config))
  },
  options(url, config = {}, canCancel = true, cancelMessage) {
    config.cancelToken = new CancelToken(c => cancel = c)
    canCancel && cancelRequest(url, cancelMessage)
    return http.options(url, Object.assign(defaultConfig, config))
  },
  post(url, data = {}, config = {}, canCancel = true, cancelMessage) {
    config.cancelToken = new CancelToken(c => cancel = c)
    canCancel && cancelRequest(url, cancelMessage)
    return http.post(url, data, Object.assign(defaultConfig, config))
  },
  put(url, data = {}, config = {}, canCancel = true, cancelMessage) {
    config.cancelToken = new CancelToken(c => cancel = c)
    canCancel && cancelRequest(url, cancelMessage)
    return http.put(url, data, Object.assign(defaultConfig, config))
  },
  patch(url, data = {}, config = {}, canCancel = true, cancelMessage) {
    config.cancelToken = new CancelToken(c => cancel = c)
    canCancel && cancelRequest(url, cancelMessage)
    return http.patch(url, data, Object.assign(defaultConfig, config))
  }
}
