@import "../var.scss";
@import "../mixins.scss";

// 日期选择器输入框
.el-date-editor {
  @include is-input;
  @include font-text;

  border-radius: $border-radius-primary;

  .el-input__inner {
    padding-left: 30px;
    padding-right: 30px;
  }

  &.el-range-editor {
    margin-top: 1px;
    padding-top: 2px;
  }
}

// 日期选择器选取面板
.el-picker-panel {
  &.el-date-range-picker {
    .el-picker-panel__body {
      .in-range {
        div {
          background: $bg-color-range;
        }
      }
    }
  }

  &.time-select {
    .time-select-item {
      &:hover {
        background: $bg-color-range;
      }
      &.disabled {
        color: $font-color-secondary2;
      }
    }
  }
}
