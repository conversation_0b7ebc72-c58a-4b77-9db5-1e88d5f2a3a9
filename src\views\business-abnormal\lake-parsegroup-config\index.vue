<template>
  <el-card>
    <!-- LAKE解析组配置 -->
    <!-- 查询条件 -->
    <el-form :model="searchForm" ref="searchForm" label-position="left" inline>
      <el-row>
        <el-form-item label="LAKE" prop="lake_id">
          <el-select v-model="searchForm.lake_id" placeholder="请选择LAKE" style="width:300px" filterable clearable>
            <el-option :value="-1" label="ALL" />
            <el-option v-for="(item, index) in lakeList" :key="index" :label="item.bk_inst_name" :value="item.bk_inst_id"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="解析组" prop="pg_id">
          <el-select v-model="searchForm.pg_id" placeholder="请选择解析组" filterable clearable style="width:300px">
            <el-option :value="-1" label="ALL" />
            <el-option v-for="(item, index) in parseGroupList" :key="index" :label="item.parse_group_name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button size="medium" type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
        <span style="float:right">
          <el-button size="medium" @click="handleAdd">新增</el-button>
        </span>
      </el-row>
    </el-form>

    <el-table :data="tableData" v-loading="querying">
      <el-table-column prop="lake_name" label="LAKE" align="center"></el-table-column>
      <el-table-column prop="pg_name" label="解析组" align="center"></el-table-column>
      <el-table-column prop="abnormal_count" label="业务5xx异常个数" align="center"></el-table-column>
      <el-table-column prop="abnormal_times" label="连续异常次数" align="center"></el-table-column>
      <el-table-column prop="abnormal_period" label="持续异常周期" align="center"></el-table-column>
      <el-table-column prop="state" label="状态" align="center">
        <template slot-scope="scope">
          <el-tag slot="reference" v-if="scope.row.state === 0" type="success">启用</el-tag>
          <el-tag slot="reference" v-if="scope.row.state === 1" type="danger">禁用</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="abnormal_period" label="vip告警切换" align="center">
        <template v-slot="{ row }">
          <el-switch :value="row.vip_dispatch_switch === 1" disabled></el-switch>
        </template>
      </el-table-column>
      <el-table-column prop="vip_auto_rec_switch" label="vip告警自动恢复" align="center">
        <template v-slot="{ row }">
          <el-switch :value="row.vip_auto_rec_switch === 1" disabled></el-switch>
        </template>
      </el-table-column>
      <el-table-column prop="vip_auto_rec_period" label="自动恢复周期" align="center"></el-table-column>
      <el-table-column prop="vip_auto_rec_limit" label="自动恢复限制次数" align="center"></el-table-column>
      <el-table-column prop="vip_auto_rec_limit_period" label="自动恢复限制周期" align="center"></el-table-column>
      <el-table-column prop="operator" label="操作人" align="center"></el-table-column>
      <el-table-column prop="update_time" label="更新时间" align="center" width="145">
        <template slot-scope='scope'>
          <span>{{ (scope.row.update_time * 1000) | dateFormat }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="操作" align="center" width="180">
        <template slot-scope="scope">
          <div>
            <el-button type="text" @click="handleEdit(scope.row)">修改</el-button>
            <el-button type="text" @click="handleDelete(scope.row)">删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-row style="text-align: center; padding-top: 20px">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.page_size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      ></el-pagination>
    </el-row>

    <el-row>
      <!-- 新增 -->
      <lake-parse-group-dialog
        v-if="addDialog"
        @close="addDialog = false"
        @refresh="onSearch"
      ></lake-parse-group-dialog>
      <!-- 修改 -->
      <lake-parse-group-dialog
        v-if="editDialog"
        :is-edit="true"
        :rowData="rowData"
        @close="editDialog = false"
        @refresh="onSearch"
      ></lake-parse-group-dialog>
    </el-row>

  </el-card>
</template>
<script>

import http from "@/api/http.js"
import { mapState } from "vuex";
import lakeParseGroupDialog from "@/views/business-abnormal/lake-parsegroup-config/dialog/lakeParseGroupDialog.vue"
import dateFormat from "@/utils/dateFormat"

export default {
  name: "lake-parsegroup-config",
  components: {
    lakeParseGroupDialog
  },
  filters: {
    dateFormat: dateFormat
  },
  props: [],
  data() {
    return {
      querying: false,
      tableData: [],
      rowData: {},
      addDialog: false,
      editDialog: false,
      pagination: {
        total: 0,
        page: 1,
        page_size: 20,
      },
      searchForm: {
        lake_id: '',
        pg_id: '',
      },
    };
  },
  computed: {
    ...mapState({
      lakeList: (state) => state.baseData.lakeList,
      parseGroupList: (state) => state.baseData.parseGroupList,
    }),
  },
  created() {},
  watch: {},
  mounted() {
    this.onSearch()
  },
  methods: {
    handleAdd() {
      this.addDialog = true
    },
    onSearch() {
      this.pagination.page = 1;
      this.query();
    },
    async query() {
      let params = {
        ...this.searchForm,
        page: this.pagination.page,
        page_size: this.pagination.page_size,
      };
      this.querying = true;
      try {
        await http.get(`/sda/req_abnormal/lake/parse_groups`, params).then((res) => {
          this.tableData = res && res.data && res.data.items;
          this.pagination.total = res && res.data && res.data.total;
          this.querying = false;
        });
      } catch (error) {
        this.querying = false;
      } finally {
        this.querying = false;
      }
    },
    handleEdit(row) {
      this.editDialog = true
      this.rowData = structuredClone(row)
    },
    async handleDelete(row) {
      await this.$confirm("确定删除该条数据吗？", "提示", {
        type: "warning",
      })
      let params = {
        operator: window.localStorage.getItem('userInfo'),
      }
      const res = await http.delete(`/sda/req_abnormal/lake/parse_groups/${row.id}`, params)
      if (res && res.code === 100000) {
        let msg = (res && res.message) || "删除成功"
        this.$message.success(msg);
        this.onSearch()
      }
    },
    handleSizeChange(val) {
      this.pagination.page = 1;
      this.pagination.page_size = val;
      this.query();
    },
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.query();
    },
  },
};
</script>
<style scoped lang="scss">
.btn {
  float: right;
}
</style>
