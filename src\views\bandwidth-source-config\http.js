import { ajax } from "@/api/ajax";
import { formPromise } from "@/utils/fetch";

const prefix = "/api/v1"
export default {
  // Lake带宽数据源配置相关API
  async getLakeBandwidthSourceList(params) {
    return await formPromise(ajax.get(`${prefix}/sda/lake_bw_source_config/get`, {
      params
    }))
  },
  
  async addLakeBandwidthSource(data) {
    return await formPromise(ajax.post(`${prefix}/sda/lake_bw_source_config/create`, data))
  },
  
  async updateLakeBandwidthSource(data) {
    return await formPromise(ajax.post(`${prefix}/sda/lake_bw_source_config/update`, data))
  },
  
  async deleteLakeBandwidthSource(id) {
    return await formPromise(ajax.delete(`${prefix}/sda/lake_bw_source_config/delete/${id}`))
  },

  // Node带宽数据源配置相关API
  async getNodeBandwidthSourceList(params) {
    return await formPromise(ajax.get(`${prefix}/sda/node_bw_source_config/get`, {
      params
    }))
  },
  
  async addNodeBandwidthSource(data) {
    return await formPromise(ajax.post(`${prefix}/sda/node_bw_source_config/create`, data))
  },
  
  async updateNodeBandwidthSource(data) {
    return await formPromise(ajax.post(`${prefix}/sda/node_bw_source_config/update`, data))
  },
  
  async deleteNodeBandwidthSource(id) {
    return await formPromise(ajax.delete(`${prefix}/sda/node_bw_source_config/delete/${id}`))
  },
} 
