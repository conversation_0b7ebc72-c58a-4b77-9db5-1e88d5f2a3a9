import { encryptPassword } from '@/utils/crypto'
import http from '@/views/auth-info/http'

const AuthStore = {
  namespaced: true,
  state: {
    isVerified: false,
    verifyTimestamp: null, // 认证时间戳，用于控制认证有效期
    verifyTimeout: 60 * 60 * 1000, // 60分钟有效期
    encryptedPassword: null, // 存储加密后的认证密码
  },
  mutations: {
    SET_VERIFIED: (state, value) => {
      state.isVerified = value
      if (value) {
        state.verifyTimestamp = Date.now()
      } else {
        state.verifyTimestamp = null
      }
    },

    SET_ENCRYPTED_PASSWORD: (state, password) => {
      state.encryptedPassword = password
    },

    RESET_AUTH_STATE: (state) => {
      state.isVerified = false
      state.verifyTimestamp = null
      state.encryptedPassword = null
    }
  },
  getters: {
    isVerified: (state) => {
      // 检查认证是否过期
      if (!state.isVerified || !state.verifyTimestamp) {
        return false
      }
      const now = Date.now()
      const isExpired = now - state.verifyTimestamp > state.verifyTimeout
      if (isExpired) {
        // 如果过期，自动重置状态
        return false
      }
      return true
    },

    encryptedPassword: (state) => {
      return state.encryptedPassword
    }
  },
  actions: {
    // 权限认证
    async verifyPassword({ commit }, password) {
      try {
        // 加密密码
        const encryptedPassword = encryptPassword(password)
        
        // 权限认证
        commit('SET_VERIFIED', false)
        const verifyRes = await http.verifyPassword({
          pwd: encryptedPassword,
        })
        
        if (verifyRes && verifyRes.code === 100000) {
          // 认证成功，存储加密后的密码
          commit('SET_VERIFIED', true)
          commit('SET_ENCRYPTED_PASSWORD', encryptedPassword)
          return {
            success: true,
            message: '认证成功',
            encryptedPassword
          }
        } else {
          return {
            success: false,
            message: '认证失败，请检查密码'
          }
        }
      } catch (error) {
        console.error('权限认证失败:', error)
        return {
          success: false,
          message: '认证失败，请检查密码'
        }
      }
    },
    
    // 检查是否需要认证
    checkAuthRequired({ getters }, callback) {
      if (!getters.isVerified) {
        // 需要认证，返回false
        return false
      } else {
        // 已认证，直接执行回调
        if (callback && typeof callback === 'function') {
          callback()
        }
        return true
      }
    },
    
    // 重置认证状态
    resetAuthState({ commit }) {
      commit('RESET_AUTH_STATE')
    }
  }
}

export default AuthStore
