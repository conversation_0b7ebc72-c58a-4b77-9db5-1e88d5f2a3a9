@import "../var.scss";
@import "../mixins.scss";

$table-item-height: 45px;

.el-table {
  &.theme1 {
    border: none;
    border-collapse: separate;
    border-spacing: 0;
    .el-table__header th {
      background: #EFF7F5;
      border-right-color: #fff;
      &:last-child {
        border-right-color: #EFF7F5;
      }
    }
    .el-table__body td {
      border-right: transparent;
      border-bottom: 1px solid #EFF7F5;
      .cell {
        line-height: 32px;
      }
    }
    &.el-table--border, &.el-table--group {
      border: none;
      &:after {
        background-color: transparent;
      }
    }
  }
  .el-table__header {
    th {
      background: $bg-color-table__header;
      color: $font-color-primary2;
      height: $table-item-height;
    }
  }
  .el-table__body {
    // 有子列表children的情况
    .el-table__row.el-table__row--level-0 {
      height: $table-item-height;
    }

    .el-table__row.el-table__row--level-1 {
      height: $table-item-height;
      td {
        height: $table-item-height;
        padding: 0;
      }
    }
    td {
      color: $font-color-primary;
    }
  }
}
