import { ajax } from "@/api/ajax";
import { formPromise } from "@/utils/fetch";

const prefix = "/api/v1"
export default {
  // Lake单机能力配置相关API
  async getLakeCapacityList(params) {
    return await formPromise(ajax.get(`${prefix}/sda/lake_basic_config/get`, {
      params
    }))
  },
  
  async addLakeCapacity(data) {
    return await formPromise(ajax.post(`${prefix}/sda/lake_basic_config/create`, data))
  },
  
  async updateLakeCapacity(data) {
    return await formPromise(ajax.post(`${prefix}/sda/lake_basic_config/update`, data))
  },
  
  async deleteLakeCapacity(id) {
    return await formPromise(ajax.delete(`${prefix}/sda/lake_basic_config/delete/${id}`))
  },

  // 主机组单机能力配置相关API
  async getHostGroupCapacityList(params) {
    return await formPromise(ajax.get(`${prefix}/sda/hg_basic_config/get`, {
      params
    }))
  },
  
  async addHostGroupCapacity(data) {
    return await formPromise(ajax.post(`${prefix}/sda/hg_basic_config/create`, data))
  },
  
  async updateHostGroupCapacity(data) {
    return await formPromise(ajax.post(`${prefix}/sda/hg_basic_config/update`, data))
  },
  
  async deleteHostGroupCapacity(id) {
    return await formPromise(ajax.delete(`${prefix}/sda/hg_basic_config/delete/${id}`))
  },
}

