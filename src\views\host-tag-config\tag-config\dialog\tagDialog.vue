<template>
  <!-- 标签配置对话框 - 统一批量处理模式 -->
  <el-dialog
    :title="dialogTitle"
    :visible.sync="visible"
    width="1200px"
    :close-on-click-modal="false"
    :show-close="true"
    @close="$emit('close')"
    append-to-body
  >
    <!-- 统一的批量表单界面 -->
    <div class="batch-container">
      <!-- 表头区域：标题和新增按钮 -->
      <div class="batch-header">
        <el-button
          v-if="!isEdit"
          type="primary"
          size="small"
          @click="addRow"
        >
          新增一行
        </el-button>
      </div>

      <!-- 批量数据表格 -->
      <div class="batch-table">
        <el-table :data="batchForms" border style="width: 100%">
          <!-- 序号列 -->
          <el-table-column label="序号" width="60" align="center">
            <template slot-scope="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>

          <!-- CDN类型列：支持多选 -->
          <el-table-column label="CDN类型" width="150">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.cdn_type"
                multiple
                placeholder="选择"
                size="small"
                style="width: 100%"
                :class="{ 'error-field': getFieldError(scope.$index, 'cdn_type') }"
              >
                <el-option label="PCDN" value="2"></el-option>
                <el-option label="LCDN" value="4"></el-option>
              </el-select>
            </template>
          </el-table-column>

          <!-- 标签键列 -->
          <el-table-column label="标签键" width="180">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.tag_key"
                placeholder="标签键"
                size="small"
                :class="{ 'error-field': getFieldError(scope.$index, 'tag_key') }"
              ></el-input>
            </template>
          </el-table-column>

          <!-- 标签名列 -->
          <el-table-column label="标签名" width="180">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.tag_name"
                placeholder="标签名"
                size="small"
                :class="{ 'error-field': getFieldError(scope.$index, 'tag_name') }"
              ></el-input>
            </template>
          </el-table-column>

          <!-- 标签值列：支持多个值的动态管理 -->
          <el-table-column label="标签值" width="300">
            <template slot-scope="scope">
              <div class="batch-tag-values">
                <!-- 每个标签值的输入行 -->
                <div v-for="(tagValue, valueIndex) in scope.row.tag_value_list" :key="valueIndex" class="batch-value-row">
                  <el-input
                    v-model="scope.row.tag_value_list[valueIndex]"
                    placeholder="标签值"
                    size="small"
                    style="width: calc(100% - 30px); margin-right: 5px"
                  ></el-input>
                  <!-- 删除标签值按钮，至少保留一个 -->
                  <el-button
                    v-if="scope.row.tag_value_list.length > 1"
                    type="danger"
                    size="mini"
                    @click="removeTagValue(scope.$index, valueIndex)"
                    icon="el-icon-delete"
                  ></el-button>
                </div>
                <!-- 添加新标签值按钮 -->
                <el-button type="text" size="small" @click="addTagValue(scope.$index)">+ 添加标签值</el-button>
              </div>
            </template>
          </el-table-column>

          <!-- 说明列 -->
          <el-table-column label="说明" width="200">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.remark"
                placeholder="说明"
                size="small"
                type="textarea"
                :rows="2"
              ></el-input>
            </template>
          </el-table-column>

          <!-- 操作列：删除行按钮 -->
          <el-table-column label="操作" min-width="80" align="center">
            <template slot-scope="scope">
              <el-button
                type="danger"
                size="mini"
                @click="removeRow(scope.$index)"
                :disabled="batchForms.length <= 1"
                icon="el-icon-delete"
              ></el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="$emit('close')">取 消</el-button>
      <el-button type="primary" :loading="submitting" @click="onSubmit">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import http from "@/views/host-tag-config/http.js";

/**
 * 标签配置对话框组件
 *
 * 重构说明：
 * 1. 移除了单个新增的概念，统一使用批量处理模式
 * 2. 单个新增被视为批量新增的特殊情况（批量数量为1）
 * 3. 采用统一的表格界面，支持动态添加/删除行和标签值
 * 4. 简化了提交逻辑，统一使用批量验证和处理
 */
export default {
  name: "tag-dialog",
  props: {
    // 对话框显示状态
    visible: {
      type: Boolean,
      default: false
    },
    // 是否为编辑模式
    isEdit: {
      type: Boolean,
      default: false
    },
    // 当前编辑的行数据
    currentRowData: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    return {
      submitting: false,
      batchForms: [], // 统一的表单数组，每个元素代表一行数据
      validationErrors: [] // 验证错误信息数组
    };
  },
  computed: {
    // 对话框标题
    dialogTitle() {
      return this.isEdit ? '修改标签' : '新增标签';
    },
  },
  watch: {
    // 监听对话框显示状态，显示时初始化表单
    visible(val) {
      if (val) {
        this.initForm();
      }
    }
  },
  methods: {
    // 初始化表单数据
    initForm() {
      this.validationErrors = [];
      console.log('error = ', this.currentRowData)

      if (this.isEdit && this.currentRowData && this.currentRowData.id) {
        // 编辑模式，将当前数据填充到批量表单数组中
        this.batchForms = [this.createFormFromData(this.currentRowData)];
      } else {
        // 新增模式，初始化空表单
        this.batchForms = [this.createEmptyForm()];
      }
    },

    // 从现有数据创建表单对象
    createFormFromData(data) {
      // 处理 cdn_type 字段
      let cdnTypeArray = [];
      if (data.cdn_type) {
        if (Array.isArray(data.cdn_type)) {
          cdnTypeArray = data.cdn_type.map(item => String(item));
        } else {
          cdnTypeArray = String(data.cdn_type).split(",").filter(Boolean);
        }
      }

      // 处理 tag_value_list 字段
      let tagValueList = [''];
      if (data.tag_value_list) {
        if (Array.isArray(data.tag_value_list) && data.tag_value_list.length > 0) {
          // 确保所有值都是字符串，并过滤空值
          tagValueList = data.tag_value_list
            .map(item => String(item))
            .filter(item => item.trim() !== '');
          // 如果过滤后为空，至少保留一个空字符串
          if (tagValueList.length === 0) {
            tagValueList = [''];
          }
        }
      }

      return {
        id: data.id,
        cdn_type: cdnTypeArray,
        tag_key: data.tag_key || '',
        tag_name: data.tag_name || '',
        tag_value_list: tagValueList,
        remark: data.remark || ''
      };
    },

    // 创建空表单对象
    createEmptyForm() {
      return {
        cdn_type: [],
        tag_key: '',
        tag_name: '',
        tag_value_list: [''],
        remark: ''
      };
    },

    // 添加新行
    addRow() {
      this.batchForms.push(this.createEmptyForm());
    },

    // 删除行
    removeRow(index) {
      if (this.batchForms.length > 1) {
        this.batchForms.splice(index, 1);
      }
    },

    // 添加标签值
    addTagValue(rowIndex) {
      this.batchForms[rowIndex].tag_value_list.push('');
    },

    // 删除标签值
    removeTagValue(rowIndex, valueIndex) {
      if (this.batchForms[rowIndex].tag_value_list.length > 1) {
        this.batchForms[rowIndex].tag_value_list.splice(valueIndex, 1);
      }
    },
    // 验证表单数据
    validateForms() {
      this.validationErrors = [];

      this.batchForms.forEach((form, index) => {
        if (!form.cdn_type || form.cdn_type.length === 0) {
          this.validationErrors.push(`第${index + 1}行：请选择CDN类型`);
        }
        if (!form.tag_key) {
          this.validationErrors.push(`第${index + 1}行：请输入标签键`);
        }
        if (!form.tag_name) {
          this.validationErrors.push(`第${index + 1}行：请输入标签名`);
        }
        const hasEmptyValue = form.tag_value_list.some(value => !value || !value.trim());
        if (hasEmptyValue || form.tag_value_list.length === 0) {
          this.validationErrors.push(`第${index + 1}行：请完善标签值`);
        }
      });

      return this.validationErrors.length === 0;
    },

    // 获取字段错误状态
    getFieldError(rowIndex, fieldName) {
      return this.validationErrors.some(error =>
        error.includes(`第${rowIndex + 1}行`) && error.includes(this.getFieldLabel(fieldName))
      );
    },

    // 获取字段标签
    getFieldLabel(fieldName) {
      const labels = {
        cdn_type: 'CDN类型',
        tag_key: '标签键',
        tag_name: '标签名',
        tag_value_list: '标签值'
      };
      return labels[fieldName] || fieldName;
    },

    // 提交表单
    async onSubmit() {
      // 验证表单数据
      if (!this.validateForms()) {
        this.$message.error(this.validationErrors[0]);
        return;
      }

      this.submitting = true;
      try {
        await this.handleSubmit();
      } catch (error) {
        console.error('提交失败:', error);
        this.$message.error('提交失败，请重试');
      } finally {
        this.submitting = false;
      }
    },

    // 处理提交逻辑
    async handleSubmit() {
      const operator = window.localStorage.getItem('userInfo') || 'system';

      // 根据是否有id字段判断是新增还是修改
      const hasId = this.batchForms.some(form => form.id);

      if (hasId) {
        // 修改操作
        await this.handleUpdate(operator);
      } else {
        // 新增操作
        await this.handleCreate(operator);
      }
    },

    // 处理新增操作
    async handleCreate(operator) {
      const createPromises = this.batchForms.map(form => {
        const data = {
          cdn_type: form.cdn_type.join(','),
          tag_key: form.tag_key,
          tag_name: form.tag_name,
          tag_value_list: form.tag_value_list.filter(value => value && value.trim()),
          remark: form.remark || '',
          operator
        };
        return http.addTag(data);
      });

      await Promise.all(createPromises);
      this.$message.success(`新增成功，共新增${this.batchForms.length}条记录`);
      this.$emit('refresh');
      this.$emit('close');
    },

    // 处理修改操作
    async handleUpdate(operator) {
      const updatePromises = this.batchForms.map(form => {
        const data = {
          id: form.id,
          cdn_type: form.cdn_type.join(','),
          tag_key: form.tag_key,
          tag_name: form.tag_name,
          tag_value_list: form.tag_value_list.filter(value => value && value.trim()),
          remark: form.remark || '',
          operator
        };
        return http.updateTag(data);
      });

      await Promise.all(updatePromises);
      this.$message.success('修改成功');
      this.$emit('refresh');
      this.$emit('close');
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}

.batch-container {
  margin-top: 10px;
}

.batch-header {
  margin-bottom: 15px;

  h4 {
    margin: 0;
    color: #303133;
  }
}

.batch-table {
  max-height: 400px;
  overflow-y: auto;

  .el-table {
    font-size: 12px;
  }
}

.batch-tag-values {
  .batch-value-row {
    display: flex;
    align-items: center;
    margin-bottom: 5px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.error-field {
  border-color: #f56c6c !important;
}
</style>
