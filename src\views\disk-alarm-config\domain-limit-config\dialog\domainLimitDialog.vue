<template>
  <el-dialog
    append-to-body
    :title="title"
    :visible="true"
    :close-on-click-modal="false"
    width="600px"
    @close="handleCancel"
  >
    <el-row>
      <el-form :inline="true" ref="dialogForm" :model="dialogForm" :rules="rules" label-width="140px" label-position="right">
        <el-row>
          <el-form-item label="lake" prop="lake_id">
            <el-select v-model="dialogForm.lake_id" placeholder="请选择lake" style="width:300px" :disabled="isEdit" filterable clearable>
              <el-option :value="-1" label="ALL" />
              <el-option v-for="(item, index) in lakeList" :key="index" :label="item.bk_inst_name" :value="item.bk_inst_id"></el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="域名" prop="domain">
            <el-input
              v-model="dialogForm.domain"
              placeholder="请输入域名"
              clearable
              style="width:300px"
            ></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="客户ID" prop="account_id">
            <el-input
              v-model="dialogForm.account_id"
              placeholder="请输入客户ID"
              clearable
              style="width:300px"
            ></el-input>
          </el-form-item>
        </el-row>
      </el-form>
    </el-row>

    <div slot="footer" class="dialog-footer">
      <el-button size="medium" @click="handleCancel">取 消</el-button>
      <el-button size="medium" type="primary" @click="handleSubmit" :loading="submiting" :disabled="submiting">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import http from '@/api/http.js'
import { mapState } from "vuex";

export default {
  components: {},
  props: {
    isEdit: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: null
    },
  },
  data() {
    return {
      submiting: false,
      dialogForm: {
        lake_id: "", // lake
        domain: "", // 域名
        account_id: "", // 客户ID
      },
      rules: {
        lake_id: [
          { required: true, message: "请选择lake", trigger: "change"}
        ],
        domain: [{ required: true, message: "请输入域名", trigger: "blur"}],
        account_id: [{ required: true, message: "请输入客户ID", trigger: "blur"}],
      },
    }
  },
  computed: {
    title() {
      return this.isEdit ? "修改" : "新增";
    },
    ...mapState({
      lakeList: (state) => state.baseData.lakeList,
    }),
  },
  mounted() {
    if (this.isEdit) {
      this.handleInitData()
    }
  },
  methods: {
    async handleSubmit() {
      try {
        await new Promise((resolve, reject) => {
          this.$refs.dialogForm.validate(valid => {
            valid ? resolve() : reject('检查错误')
          })
        })
      } catch (err) {
        return false
      }
      this.submiting = true
      let params = {
        ...this.dialogForm,
        operator: window.localStorage.getItem('userInfo'),
      }
      let res = {}
      let successMsg = "添加成功"
      if (this.isEdit) {
        // 修改
        res = await http.patch(`/sda/requests/lakes/domains/limit/${this.rowData.id}`, params)
        successMsg = "修改成功"
      } else {
        // 新增
        res = await http.post(`/sda/requests/lakes/domains/limit`, params)
      }
      if (res && res.code === 100000) {
        this.$message.success(successMsg);
        this.$emit("close")
        this.$emit("refresh")
        this.submiting = false
      } else {
        this.submiting = false
      }
    },
    handleCancel() {
      this.$emit("close")
      this.$emit("refresh")
    },
    /**
     * 初始化表单数据
     * 此方法用于将rowData对象的数据复制到dialogForm对象中
     * 它创建rowData的深拷贝以避免直接修改原始数据
     */
    handleInitData() {
      const row = structuredClone(this.rowData)
      this.dialogForm.lake_id = row.lake_id
      this.dialogForm.domain = row.domain
      this.dialogForm.account_id = row.account_id
    }
  },
}
</script>

<style scoped lang="scss"></style>