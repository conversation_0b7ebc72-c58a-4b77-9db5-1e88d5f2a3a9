<template>
  <el-card>
    <div slot="header" class="clearfix">
      <span>调度数据配置</span>
    </div>
    <!-- 查询条件 -->
    <el-form :model="searchForm" ref="searchForm" label-position="left" inline>
      <el-row>
        <el-form-item label="解析组">
          <el-select v-model="searchForm.pg_id" placeholder="请选择解析组" filterable clearable style="width:300px" :loading="parge_group_loading">
            <el-option v-for="(item, index) in parseGroupList" :key="index" :label="item.parse_group_name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="区域">
          <el-select v-model="searchForm.view_id" placeholder="请选择区域" filterable clearable style="width:300px">
            <el-option v-for="(item, index) in viewList" :key="index" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button size="medium" type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
        <span style="float:right">
          <el-button size="medium" :disabled="!authCtrl.specialAreaAuth" @click="handleAdd">新增</el-button>
        </span>
      </el-row>
    </el-form>

    <el-table :data="tableData" v-loading="querying">
      <el-table-column prop="pg_name" label="解析组" align="center" width="200"></el-table-column>
      <el-table-column prop="view_name" label="区域" align="center" width="200"></el-table-column>
      <el-table-column prop="v4_rate" label="v4比例" align="center" width="100"></el-table-column>
      <el-table-column prop="v6_rate" label="v6比例" align="center" width="100"></el-table-column>
      <el-table-column prop="operator" label="操作人" align="center"></el-table-column>
      <el-table-column prop="message" label="备注" align="center"></el-table-column>
      <el-table-column prop="create_time" label="创建时间" align="center">
        <template slot-scope='scope'>
          <span>{{ (scope.row.create_time * 1000) | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="update_time" label="修改时间" align="center">
        <template slot-scope='scope'>
          <span>{{ (scope.row.update_time * 1000) | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="180">
        <template slot-scope="scope">
          <div>
            <el-button type="text" :disabled="!authCtrl.specialAreaAuth" @click="handleEdit(scope.row)">修改</el-button>
            <el-button type="text" :disabled="!authCtrl.specialAreaAuth" @click="handleDelete(scope.row)">删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-row style="text-align: center">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.page_size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      ></el-pagination>
    </el-row>

    <el-row>
      <!-- 新增 -->
      <special-area-dialog
        v-if="addDialog"
        @close="addDialog = false"
        @refresh="onSearch"
      ></special-area-dialog>
      <!-- 修改 -->
      <special-area-dialog
        v-if="editDialog"
        :is-edit="true"
        :rowData="rowData"
        @close="editDialog = false"
        @refresh="onSearch"
      ></special-area-dialog>
    </el-row>

  </el-card>
</template>
<script>

import http from "@/api/http.js"
import specialAreaDialog from "@/views/schedule-data-config/special-area/dialog/specialAreaDialog.vue"
import { mapState } from "vuex";
import dateFormat from "@/utils/dateFormat"
import services from "@/api/services"

export default {
  name: "special-area",
  components: {
    specialAreaDialog,
  },
  filters: {
    dateFormat: dateFormat
  },
  props: [],
  data() {
    return {
      querying: false,
      tableData: [],
      rowData: {},
      addDialog: false,
      editDialog: false,
      pagination: {
        total: 0,
        page: 1,
        page_size: 20,
      },
      searchForm: {
        pg_id: '',
        view_id: ''
      },
      authCtrl: {},
    };
  },
  computed: {
    ...mapState({
      parseGroupList: (state) => state.baseData.parseGroupList,
      viewList: (state) => state.baseData.viewList,
      parge_group_loading: (state) => state.baseData.parge_group_loading,
    }),
  },
  created() {},
  async mounted() {
    this.onSearch()
    // 权限控制
    let privileges = localStorage.getItem('privileges')
    if(!privileges) {
      const data = await services.getAuth()
      privileges = data.data.privileges
    }
    // 权限控制
    this.authCtrl = {
      specialAreaAuth: privileges.indexOf('b_sda_sdcp_special_conf') > -1 // 新增、修改、删除
    }
  },
  watch: {},
  methods: {
    handleAdd() {
      this.addDialog = true
    },
    handleEdit(row) {
      this.editDialog = true
      this.rowData = structuredClone(row)
    },
    async handleDelete(row) {
      await this.$confirm("确定删除该条数据吗？", "提示", {
        type: "warning",
      })
      const res = await http.delete(`/sdcp/pvbw/special_conf/delete/${row.id}`)
      if (res && res.code === 100000) {
        let msg = (res && res.message) || "删除成功"
        this.$message.success(msg);
        this.onSearch()
      }
    },
    onSearch() {
      this.pagination.page = 1;
      this.query();
    },
    async query() {
      let params = {
        pg_id: this.searchForm.pg_id,
        view_id: this.searchForm.view_id,
        page: this.pagination.page,
        page_size: this.pagination.page_size,
      };
      this.querying = true;
      try {
        await http.get(`/sdcp/pvbw/special_conf/get`, params).then((res) => {
          this.tableData = res && res.data && res.data.items;
          this.pagination.total = res && res.data && res.data.total;
          this.querying = false;
        });
      } catch (error) {
        this.querying = false;
      } finally {
        this.querying = false;
      }
    },
    handleSizeChange(val) {
      this.pagination.page = 1;
      this.pagination.page_size = val;
      this.query();
    },
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.query();
    },
  },
};
</script>
<style scoped lang="scss">
.btn {
  float: right;
}
</style>
