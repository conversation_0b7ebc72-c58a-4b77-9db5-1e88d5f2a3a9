// validate domain regexp
export const domainRegexp = /^[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+\.?$/
// 校验是否为数字
export const numberRegexp = /^-?\d+(\.\d+)?$/;
// 校验ipv4
export const ipv4Regexp = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
// 校验邮箱格式
export const emailRegexp = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
// 校验纯数字（不限位数）
export const pureNumberRegexp = /^\d+$/
