<template>
  <el-dialog
    id='configFileAdd'
    :title='titleName'
    :visible="true"
    :width='width'
    append-to-body
    :close-on-press-escape='false'
    :close-on-click-modal='false'
    @close='handleClose'
  >
    <el-row>
      <el-form :inline='true' ref='detailForm' :model='detailForm' label-width='85px' style='padding-left: 5px;' :rules="rules">
        <!-- 配置文件名 -->
        <el-form-item style='margin: 0; padding: 0' label='配置文件名' prop="file_name">
          <el-input v-model='detailForm.file_name' placeholder='请输入文件名'></el-input>
        </el-form-item>
        <!-- md5 -->
        <el-form-item label='md5' label-width='80px'>
          <el-input v-model='file_md5' placeholder='请输入md5' style='width: 250px'></el-input>
        </el-form-item>
      </el-form>
    </el-row>
    <el-row>
      <el-col :span='24' class='el-transfer'>
        <div class='upload_file'>
            <div class='div_right'>
              <div class='progress_css'>
                <span v-show='this.showProgress'>文件上传进度
                  <span v-if='this.progressLength === 100' style='color: #67c23a; font-size: 14px'>&nbsp;上传成功</span>
                </span>
                 <span v-if='this.progressLength === 99 && this.failProgress' style='color: red; font-size: 14px'>&nbsp;{{tipInfo}}</span>
                <el-progress v-show='showProgress' :percentage='progressLength'></el-progress>
              </div>
              <el-upload
                drag
                action="https://jsonplaceholder.typicode.com/posts/"
                accept=".xls,.xlsx,.xlsm,.xlsb"
                :http-request='httpRequest'
                :on-change='onChange'
                class='upload-demo'
                ref='upload'
                :on-preview='handlePreview'
                :on-remove='handleRemove'
                :file-list='fileList'
                :auto-upload='false'
                :limit='1'
                :on-exceed='handleExceed'
                :before-remove='beforeRemove'
                :before-upload='beforeUpload'
                :on-success='onSuccess'
                :on-error='onErr'
              >
                <i class='el-icon-upload'></i>
                <div class='el-upload__text'>将文件拖到此处，或<em>点击上传</em></div>
              </el-upload>
            </div>
        </div>
        
      </el-col>
      <el-col :span='24' style='text-align: center; padding-top: 20px'>
        <el-button :disabled='this.fileuploading' type='primary' @click='onSave()' :loading="saveLoading">保存</el-button>
        <el-button @click='onClose'>取消</el-button>
      </el-col>
    </el-row>
  </el-dialog>
</template>
<script>

import { Message } from "element-ui"
import http from '@/api/http.js'
import SparkMD5 from 'spark-md5'

  export default {
    name: 'configFileAdd',
    components: {},
    mixins: [],
    props: ['isShow', 'isUploadFile'],
    data() {
      return {
        width: '40%',
        fileType: "",
        fileList: [],
        tipInfo: '',
        beforeFilelist: [],
        titleName: '新增配置',
        addConfigFileDialog: false,
        file_md5: '',
        detailForm: {
          file_name: '',
        },
        rules: {
          file_name: [{ required: true, message: '配置文件名称不能为空', trigger: ['blur', 'change']}],
          base_path: [{ required: true, message: '配置路径不能为空', trigger: ['blur', 'change']}],
        },
        saveLoading: false,
        validateDisabled: false,
        isUploading: false,
        fileuploading: false,
        nowTime: '',
        optionsFileName: '',
        isEmptyFile: false,
        id: '',
        cmOption: {
          value: 'detailForm.temp_content',
          origLeft: 'detailForm.file_content',
          mode: 'text/x-nginx-conf', // 模式
          theme: 'xq-light', // 主题
          foldGutter: true, // 启用行槽中的代码折叠
          lineWrapping: true,
        },
        showProgress: false,
        failProgress: false,
        progressLength: 0,
        uploadStatus: false,
        isZip: false,
      }
    },
    methods: {
      onSave() {
        let num = 0
        this.$refs.detailForm.validate((valid) => {
          if (!valid) {
            num += 1
            return false
          }
        })
        if (num > 0) return
        this.saveLoading = true
        this.$confirm('此操作将保存该文件, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.preservationUpload()
          }).catch(() => {
            this.saveLoading = false
            this.validateDisabled = false
        })
      },
      preservationUpload() {
        if (this.isUploading && this.beforeFilelist.length > 0 && this.file_md5 === '') {
          this.$message({
            showClose: true,
            message: '上传文件时 md5值 不能为空',
            type: 'warning'
          })
          this.saveLoading = false
          this.validateDisabled = false
          return false
        }
        if (this.isUploading && this.titleName === '新增配置' && this.beforeFilelist.length > 0) {
          this.submitUpload()
        } else if (this.isUploading && this.titleName !== '新增配置') {
          if (this.beforeFilelist.length > 0) {
            this.submitUpload()
          } else {
            this.uploadStatus = true
            this.onSuccess()
          }
        } else {
          this.preservation()
        }
      },
      submitUpload() {
        this.$refs.upload.submit()
      },
      async preservation() {
        if (this.isUploading && this.titleName === '新增配置' && this.beforeFilelist.length <= 0) {
          this.$message({
            showClose: true,
            message: '新增上传配置文件不能为空',
            type: 'warning'
          })
          this.saveLoading = false
          this.validateDisabled = false
          return false
        }
      },
      async httpRequest(options) {
        let vm = this
        vm.tipInfo = ''
        var fileReader = new FileReader()
        var Spark = new SparkMD5.ArrayBuffer()
        fileReader.readAsArrayBuffer(options.file)
        try {
          fileReader.onload = function(e) {
            Spark.append(e.target.result)
            var res = Spark.end()
            
            if (res && res === vm.file_md5) {
              vm.fileUploadRequest(options)
            } else {
              vm.saveLoading = false
              vm.validateDisabled = false
              Message({
                showClose: true,
                message: `md5 值效验不通过，${res}`,
                type: 'warning'
              })
              return false
            }
          }
        } catch(err) {
          vm.saveLoading = false
          vm.validateDisabled = false
          Message({
            showClose: true,
            message: `md5 值获取失败`,
            type: 'warning'
          })
          return false
        }
      },
      async fileUploadRequest(options) {
        this.fileuploading = true
        this.nowTime = this.getNowTime()
        this.optionsFileName = options.file
        const formData = new FormData()
        formData.append(`file`, options.file)
        formData.append(`file_name`, this.detailForm.file_name + this.fileType)
        formData.append(`file_md5`, this.file_md5)
        await http.post('/sda/redundancy/upload/business', formData).then((res) => {
          if (res && res.code === 100000) {
            this.saveLoading = false
            this.uploadStatus = true
            this.progressLoadSuccess()
            options.onSuccess()
            this.$emit('refresh')
            this.$eventBus.$emit("refresh");
            this.$message({
              showClose: true,
              message: '上传成功',
              type: 'success'
            })
          } else {
            // this.$message({
            //   showClose: true,
            //   message: '上传失败',
            //   type: 'error'
            // })
            this.tipInfo = '上传失败'
            this.saveLoading = false
            this.validateDisabled = false
            this.progressLoadFail()
            options.onError()
          }
          this.fileuploading = false
        })
      },
      onSuccess() {
        this.fileuploading = false
        if (!this.uploadStatus) return
        this.uploadStatus = false
        if (this.isUploading && this.titleName === '新增配置' && this.beforeFilelist.length <= 0) {
          this.$message({
            showClose: true,
            message: '新增上传配置文件不能为空',
            type: 'warning'
          })
          this.saveLoading = false
          this.validateDisabled = false
          return false
        }
        if (this.isEmptyFile) {
          this.isEmptyFile = false
          return false
        }
        let body = {
          file_name: this.detailForm.file_name,
          operator: this.thisOperator,
          file_md5: this.file_md5,
          file_size_temp: parseInt(this.detailForm.file_size_temp),
          file_size: parseInt(this.detailForm.file_size),
        }
        if (this.isUploading) {
          body.use_minio = true
          body.store_type = 1
          if (this.id !== '' && this.nowTime !== '') {
            body.temp_content = this.detailForm.file_name + '_' + this.id + '_' + this.nowTime + '.gz'
            if (!this.optionsFileName || this.optionsFileName == '') {
              body.temp_content = this.detailForm.temp_content
            }
          }
        }
      },
      onErr(response, file, fileList) {
        this.fileuploading = false
      },
      getNowTime() {
        let dateTime
        let yy = new Date().getFullYear()
        let mm = new Date().getMonth() + 1 < 10 ? '0' + (new Date().getMonth() + 1) : new Date().getMonth() + 1
        let dd = new Date().getDate() < 10 ? '0' + new Date().getDate() : new Date().getDate()
        let hh = new Date().getHours() < 10 ? '0' + new Date().getHours() : new Date().getHours()
        let mf = new Date().getMinutes() < 10 ? '0' + new Date().getMinutes() : new Date().getMinutes()
        let ss = new Date().getSeconds() < 10 ? '0' + new Date().getSeconds() : new Date().getSeconds()
        dateTime = yy + '' + mm + '' + dd + '' + hh + '' + mf + '' + ss
        return parseInt(dateTime)
      },
      handleExceed(files, fileList) {
        this.$message.warning(`只允许上传 1 个文件`)
      },
      beforeRemove(file, fileList) {
        this.optionsFileName = ''
        this.nowTime = ''
      },
      beforeUpload(file) {
        if (file.size / (1024 * 1024) <= 0) { // 限制文件大小
          this.$message.warning(`上传文件大小必须大于0M`)
          this.fileList = []
          this.isEmptyFile = true
          return false
        }

        let isFile = file.name.split('.')[file.name.split('.').length - 1] == 'zip'
        this.isZip = false
        if (isFile) {
          this.isZip = true
        }
      },
      progressLoadSuccess() {
        this.progressLength = 100
        this.showProgress = true
      },
      progressLoadFail() {
        this.progressLength = 99
        this.showProgress = true
        this.failProgress = true
        var elementsByName = document.getElementsByClassName('el-progress-bar__inner')
        if (elementsByName && elementsByName.length > 0) {
          elementsByName[0].style.backgroundColor = 'red'
        }
      },
      onChange(file, fileList) {
        let name = fileList && fileList[0] && fileList[0].name
        if (!name) return
        let lastIndex = name.lastIndexOf(".")
        this.fileType = name.substring(lastIndex)
        
        this.beforeFilelist = fileList
        if (file.status === 'success') {
          file.status = 'ready'
        }
      },
      
      onClose() {
        this.$emit('close')
        if (this.isUploading && this.fileuploading) {
          source.cancel()
          location.reload()
        }
        this.saveLoading = false
        this.validateDisabled = false
        this.addConfigFileDialog = false
        this.fileList = []
        this.id = ''
        this.nowTime = ''
        this.beforeFilelist = []
        this.file_md5 = ''
        this.progressLength = 0
        this.showProgress = false
        this.failProgress = false
        this.fileuploading = false
      },

      handleRemove(file, fileList) {
        this.beforeFilelist = []
        this.showProgress = false
        this.failProgress = false
        this.progressLength = 0
      },
      handlePreview(file) {
      },
      handleClose() {
        this.$emit('close')
        if (this.isUploading && this.fileuploading) {
          source.cancel()
          location.reload()
        }
        this.optionsFileName = ''
        this.saveLoading = false
        this.validateDisabled = false
        this.addConfigFileDialog = false
        this.fileList = []
        this.id = ''
        this.nowTime = ''
        this.beforeFilelist = []
        this.file_md5 = ''
        this.progressLength = 0
        this.showProgress = false
        this.fileuploading = false
      },
    },
  async mounted() {},
  watch: {
    isUploadFile: {
      deep: true,
      handler() {
        this.isUploading = this.isUploadFile
        this.addConfigFileDialog = true
      },
      immediate: true
    },
    isShow: function() {
      this.cmOption.value = ''
      this.cmOption.origLeft = ''
      if (this.isTitle === 'add') {
        this.titleName = '新增配置'
        this.addConfigFileDialog = true
      }
    }
  },
  computed: {
    thisOperator() {
      return localStorage.getItem('userInfo') || 'unknow'
    },
  },
  }
</script>

<style scoped>
  /deep/ #checkbox {
    margin-left: -120px;
    margin-right: 10px;
  }

  .oldtextarea /deep/ textarea {
    color: #000 !important;
  }

  .el-button--info.is-plain:hover,
  .el-button--info.is-plain:focus {
    background: #909399 !important;
    border-color: #909399 !important;
    color: #fff !important;
  }

</style>

<style>
  .div_right {
    /* float: right; */
    /* width: 50%; */
    height: 320px;
    border: 1px solid #DCDFE6;
    margin-left: 0px;
    margin-top: 0px;
    text-align: center;
  }

  .progress_css {
    bottom: 0;
    width: 100%;
    height: 30px;
    margin-top: 20px;
    text-align: center;
  }

</style>
