<template>
  <el-card class="host-group-capacity-config">
    <div class="search-container">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="主机组名">
          <el-select
            v-model="searchForm.hg_name"
            placeholder="请输入/选择主机组名"
            allow-create
            filterable
            default-first-option
            clearable
          >
            <el-option
              v-for="item in hostGroupOptions"
              :key="item.hg_code"
              :label="item.hg_name"
              :value="item.hg_name"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchData">查询</el-button>
          <el-button @click="resetForm">重置</el-button>
          <el-button type="primary" @click="showAddDialog">新增</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table :data="tableData" v-loading="loading">
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column prop="hg_id" label="主机组Id"></el-table-column>
      <el-table-column prop="hg_name" label="主机组名"></el-table-column>
      <el-table-column prop="hg_code" label="主机组code"></el-table-column>
      <el-table-column prop="host_rated_bw" label="单机额定带宽(Mbps)">
      </el-table-column>
      <el-table-column prop="host_rated_pps" label="单机额定PPS">
      </el-table-column>
      <el-table-column prop="operator" label="操作人"></el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleEdit(scope.row)"
            >修改</el-button
          >
          <el-button size="mini" type="text" @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="currentPage"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
    >
    </el-pagination>

    <!-- 新增/编辑弹窗 -->
    <el-dialog
      append-to-body
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="550px"
      :show-close="false"
    >
      <el-form :model="form" :rules="rules" ref="form" label-width="150px">
        <el-form-item label="主机组名" prop="hg_name">
          <el-select
            v-model="form.hg_id"
            placeholder="请选择主机组名"
            filterable
            :disabled="isEdit"
            style="width: 100%"
            @change="handleHostGroupChange"
          >
            <el-option
              v-for="item in hostGroupOptions"
              :key="item.hg_code"
              :label="item.hg_name"
              :value="item.hg_id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="主机组Id" prop="hg_id">
          <el-input v-model="form.hg_id" disabled></el-input>
        </el-form-item>
        <el-form-item label="主机组code" prop="hg_code">
          <el-input v-model="form.hg_code" disabled></el-input>
        </el-form-item>
        <el-form-item label="单机额定带宽" prop="host_rated_bw">
          <el-input-number
            v-model="form.host_rated_bw"
            :min="-1"
            :precision="0"
            placeholder="请输入单机额定带宽"
            @change="handleHostRatedBwChange"
          >
          </el-input-number>
          <span class="unit">Mbps</span>
        </el-form-item>
        <el-form-item label="单机额定PPS" prop="host_rated_pps">
          <el-input-number
            v-model="form.host_rated_pps"
            :min="-1"
            :precision="0"
            placeholder="请输入单机额定PPS"
            @change="handleHostRatedPpsChange"
          >
          </el-input-number>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCloseDialog">取 消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitLoading"
          >确 定</el-button
        >
      </div>
    </el-dialog>

    <!-- 删除确认框 -->
    <el-dialog
      append-to-body
      title="提示"
      :visible.sync="deleteDialogVisible"
      width="400px"
    >
      <div>确定要删除该配置吗？</div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="confirmDelete"
          :loading="deleteLoading"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </el-card>
</template>

<script>
import http from "../http";
import { mapState } from "vuex";

export default {
  name: "HostGroupCapacityConfig",
  data() {
    return {
      // 表格数据
      tableData: [],
      loading: false,
      // 分页
      currentPage: 1,
      pageSize: 10,
      total: 0,
      // 搜索
      searchForm: {
        hg_name: "",
      },
      // 弹窗
      dialogVisible: false,
      dialogTitle: "新增主机组单机能力配置",
      isEdit: false,
      submitLoading: false,
      form: {
        id: "",
        hg_id: "",
        hg_name: "",
        hg_code: "",
        host_rated_bw: -1,
        host_rated_pps: -1,
      },
      rules: {
        hg_name: [
          { required: true, message: "请选择主机组名", trigger: "change" },
        ],
        host_rated_bw: [
          { required: true, message: "请输入单机额定带宽", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (value === -1 && this.form.host_rated_pps === -1) {
                callback(new Error("单机额定带宽和单机额定PPS不能同时为-1"));
              } else {
                callback();
              }
            },
            trigger: "change",
          },
        ],
        host_rated_pps: [
          { required: true, message: "请输入单机额定PPS", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (value === -1 && this.form.host_rated_bw === -1) {
                callback(new Error("单机额定带宽和单机额定PPS不能同时为-1"));
              } else {
                callback();
              }
            },
            trigger: "change",
          },
        ],
      },
      // 删除确认框
      deleteDialogVisible: false,
      deleteLoading: false,
      deleteId: null,
    };
  },
  created() {
    this.fetchData();
  },
  computed: {
    ...mapState({
      hostGroupList: (state) => state.baseData.hostGroupList,
    }),
    hostGroupOptions() {
      return [
        ...this.hostGroupList.map((itm) => {
          return {
            hg_name: itm.group_name,
            hg_id: itm.id,
            hg_code: itm.group_code,
          };
        }),
      ];
    },
  },
  methods: {
    handleHostRatedBwChange() {
      this.$nextTick(() => {
        this.$refs.form.validateField(["host_rated_pps", "host_rated_bw"]);
      });
    },
    handleHostRatedPpsChange() {
      this.$nextTick(() => {
        this.$refs.form.validateField(["host_rated_pps", "host_rated_bw"]);
      });
    },
    handleCloseDialog() {
      this.dialogVisible = false;
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    },
    handleHostGroupChange() {
      const hostGroupOption = this.hostGroupOptions.find(
        (itm) => itm.hg_id === this.form.hg_id
      );
      if (!hostGroupOption) return;
      this.form.hg_code = hostGroupOption.hg_code;
      this.form.hg_name = hostGroupOption.hg_name;
    },
    // 获取表格数据
    async fetchData() {
      this.loading = true;
      try {
        const res = await http.getHostGroupCapacityList({
          page: this.currentPage,
          page_size: this.pageSize,
          hg_name: this.searchForm.hg_name,
        });

        if (res && res.data) {
          this.tableData = res.data.items || [];
          this.total = res.data.total || 0;
        } else {
          this.tableData = [];
          this.total = 0;
        }

        this.loading = false;
      } catch (error) {
        console.error(error);
        this.loading = false;
        this.$message.error("获取数据失败");
      }
    },

    // 搜索
    searchData() {
      this.currentPage = 1;
      this.fetchData();
    },

    // 重置
    resetForm() {
      this.searchForm = {
        hg_name: "",
      };
      this.searchData();
    },

    // 分页
    handleSizeChange(val) {
      this.pageSize = val;
      this.fetchData();
    },

    handleCurrentChange(val) {
      this.currentPage = val;
      this.fetchData();
    },

    // 新增
    showAddDialog() {
      this.dialogTitle = "新增主机组单机能力配置";
      this.isEdit = false;
      this.form = {
        id: "",
        hg_id: "",
        hg_name: "",
        hg_code: "",
        host_rated_bw: -1,
        host_rated_pps: -1,
      };
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate();
      });
    },

    // 编辑
    handleEdit(row) {
      this.dialogTitle = "修改主机组单机能力配置";
      this.isEdit = true;
      this.form = JSON.parse(JSON.stringify(row));
      this.dialogVisible = true;
    },

    // 提交表单
    submitForm() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.submitLoading = true;
          let res;
          try {
            // 准备提交的数据
            const submitData = {
              id: this.form.id,
              hg_id: this.form.hg_id,
              hg_name: this.form.hg_name,
              hg_code: this.form.hg_code,
              host_rated_bw: this.form.host_rated_bw,
              host_rated_pps: this.form.host_rated_pps,
            };

            if (this.isEdit) {
              // 编辑
              res = await http.updateHostGroupCapacity(submitData);
              if (!res) {
                this.submitLoading = false;
                return;
              }

              // 重新加载数据
              this.fetchData();
              this.$message.success("修改成功");
            } else {
              delete submitData.id;
              // 新增
              res = await http.addHostGroupCapacity(submitData);
              if (!res) {
                this.submitLoading = false;
                return;
              }

              // 重新加载数据
              this.fetchData();
              this.$message.success("新增成功");
            }

            this.dialogVisible = false;
          } catch (error) {
            console.error(error);
          } finally {
            this.submitLoading = false;
          }
        } else {
          return false;
        }
      });
    },

    // 删除
    handleDelete(row) {
      this.deleteId = row.id;
      this.deleteDialogVisible = true;
    },

    // 确认删除
    async confirmDelete() {
      if (!this.deleteId) return;

      this.deleteLoading = true;
      try {
        // 调用删除API
        await http.deleteHostGroupCapacity(this.deleteId);

        // 从表格移除
        const index = this.tableData.findIndex(
          (item) => item.id === this.deleteId
        );
        if (index !== -1) {
          this.tableData.splice(index, 1);
          this.total--;
        }

        this.$message.success("删除成功");
        this.deleteDialogVisible = false;
        this.deleteLoading = false;
      } catch (error) {
        console.error(error);
        this.deleteLoading = false;
        this.$message.error("删除失败");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.host-group-capacity-config {
  .unit {
    margin-left: 10px;
  }

  .el-pagination {
    margin-top: 20px;
    text-align: right;
  }
}
</style> 
