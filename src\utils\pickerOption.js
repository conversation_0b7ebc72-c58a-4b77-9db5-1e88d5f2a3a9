export const shortcuts_obj = {
  // recent_15_minutes: {
  //   text: '最近15分钟',
  //   onClick(picker) {
  //     const end = new Date();
  //     const start = new Date();
  //     start.setTime(start.getTime() - 15 * 60 * 1000);
  //     picker.$emit('pick', [start, end]);
  //   }
  // },
  // recent_1_hour: {
  //   text: '最近1小时',
  //   onClick(picker) {
  //     const end = new Date();
  //     const start = new Date();
  //     start.setTime(start.getTime() - 3600 * 1000);
  //     picker.$emit('pick', [start, end]);
  //   }
  // },
  recent_6_hours: {
    text: '最近6小时',
    onClick(picker) {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 6);
      picker.$emit('pick', [start, end]);
    }
  },
  recent_24_hours: {
    text: '最近24小时',
    onClick(picker) {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24);
      picker.$emit('pick', [start, end]);
    }
  },
  recent_1_week: {
    text: '最近七天',
    onClick(picker) {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      picker.$emit('pick', [start, end]);
    }
  },
  // recent_1_month: {
  //   text: '最近一个月',
  //   onClick(picker) {
  //     const end = new Date();
  //     const start = new Date();
  //     start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
  //     picker.$emit('pick', [start, end]);
  //   }
  // },
  // recent_3_months: {
  //   text: '最近三个月',
  //   onClick(picker) {
  //     const end = new Date();
  //     const start = new Date();
  //     start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
  //     picker.$emit('pick', [start, end]);
  //   }
  // }
}

export const shortcuts_arr = [
  ...Object.values(shortcuts_obj)
]
