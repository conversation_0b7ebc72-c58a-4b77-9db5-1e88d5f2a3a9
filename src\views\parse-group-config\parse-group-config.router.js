import EmptyLayout from "@/package/src/components/EmptyLayout";
import Config from "@/views/parse-group-config/cover-adjust/config.vue"

export default [
  {
    path: '/',
    redirect: { name: 'coverAdjust' }
  },
  {
    path:'/parseGroupConfig',
    component: EmptyLayout,
    name: "parseGroupConfig",
    meta: {
      title: "解析组配置",
    },
    children: [
      {
        path: 'coverAdjust',
        name: 'coverAdjust',
        component: resolve => require(['@/views/parse-group-config/cover-adjust/index.vue'], resolve),
        meta: {
          title: '覆盖调整',
          home: true,
          keepAlive: true
        },
        hidden: false
      },
      {
        path: '/parse-config',
        props: (route) => ({query: route.query.id}),
        name: 'parse-config',
        component: Config
      },
      {
        path: 'todoTask',
        name: 'todoTask',
        component: resolve => require(['@/views/parse-group-config/todo-task/index.vue'], resolve),
        meta: {
          title: '待办任务',
          home: true,
          keepAlive: true
        },
        hidden: false
      },
      {
        path: 'autoGenerate',
        name: 'autoGenerate',
        component: resolve => require(['@/views/parse-group-config/auto-generate/index.vue'], resolve),
        meta: {
          title: '待办任务-自动生成',
          home: true,
          keepAlive: true
        },
        hidden: false
      },
      {
        path: 'auditTask',
        name: 'auditTask',
        component: resolve => require(['@/views/parse-group-config/audit-task/index.vue'], resolve),
        meta: {
          title: '待审任务',
          home: true,
          keepAlive: true
        },
        hidden: false
      },
      {
        path: 'completedTask',
        name: 'completedTask',
        component: resolve => require(['@/views/parse-group-config/completed-task/index.vue'], resolve),
        meta: {
          title: '已下发任务',
          home: true,
          keepAlive: true
        },
        hidden: false
      },
    ]
  }
]