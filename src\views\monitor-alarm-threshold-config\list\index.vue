<template>
  <el-card>
    <!-- 监控告警阈值配置 -->
    <el-form :model="searchForm" ref="searchForm" label-position="left" inline>
      <el-row>
        <el-form-item label="域名" prop="domain">
          <el-select v-model="searchForm.domain" placeholder="请输入域名" style="width:300px" filterable clearable>
            <el-option v-for="(item, index) in domainList" :key="index" :label="item.domain" :value="item.domain">
              <span style="float:left; text-overflow: ellipsis;overflow: hidden;">{{ item.domain }}</span>
              <span style="float:right; font-size: 12px;color: #b4b4b4;">&emsp;{{item.accid }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button size="medium" type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
        <span style="float:right">
          <el-button size="medium" @click="handleAdd">新增</el-button>
        </span>
      </el-row>
    </el-form>

    <el-table :data="tableData" v-loading="querying">
      <el-table-column type="index" label="序号" width="50" align="center"></el-table-column>
      <el-table-column prop="domain" label="域名" align="center" width="200">
        <template slot-scope="scope">
          <div @click="handleView(scope.row)" class="text-wrapper">{{ scope.row.domain }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="accid" label="客户id" align="center" width="160"></el-table-column>
      <el-table-column prop="persistentAbnormal" label="持续异常时间" align="center" width="95"></el-table-column>

      <el-table-column label="首帧时间" align="center">
        <el-table-column prop="firstFrameCount" label="首帧记录数" align="center" width="85"></el-table-column>
        <el-table-column prop="firstFrameTimeCostThre" label="首帧时长阈值（ms）" align="center" width="100"></el-table-column>
        <el-table-column prop="firstFrameIncreasePercentThre" label="首帧突增比例阈值（%）" align="center" width="100"></el-table-column>
      </el-table-column>

      <el-table-column label="渲染百秒卡顿时长" align="center">
        <el-table-column prop="videoRenderLagTimeCostCount" label="渲染百秒卡顿时长记录数" align="center"></el-table-column>
        <el-table-column prop="videoRenderLagTimeCostThre" label="渲染百秒卡顿时长阈值（ms）" align="center" width="85"></el-table-column>
        <el-table-column prop="videoRenderLagTimeCostIncreasePercentThre" label="渲染百秒卡顿时长突增比例阈值（%）" align="center" width="85"></el-table-column>
      </el-table-column>

      <el-table-column label="渲染百秒卡顿次数" align="center">
        <el-table-column prop="videoRenderLagCount" label="渲染百秒卡顿次数记录数" align="center"></el-table-column>
        <el-table-column prop="videoRenderLagCountThre" label="渲染百秒卡顿次数阈值" align="center" width="85"></el-table-column>
        <el-table-column prop="videoRenderLagCountIncreasePercentThre" label="渲染百秒卡顿次数突增比例阈值（%）" align="center" width="85"></el-table-column>
      </el-table-column>

      <el-table-column label="review拉流成功率" align="center">
        <el-table-column prop="newSumPlayCount" label="review拉流记录数" align="center"></el-table-column>
        <el-table-column prop="newSumPlaySuccessRateThre" label="review拉流成功率阈值（ms）" align="center" width="85"></el-table-column>
        <el-table-column prop="newSumPlaySuccessRateDecreasePercentThre" label="review拉流成功率突降比例阈值（%）" align="center" width="85"></el-table-column>
      </el-table-column>

      <el-table-column label="命中首包时间" align="center">
        <el-table-column prop="firstPackageCount" label="首包记录数" align="center" width="85"></el-table-column>
        <el-table-column prop="firstPackageTimeCostThre" label="首包时间阈值（ms）" align="center" width="85"></el-table-column>
        <el-table-column prop="firstPackageIncreasePercentThre" label="首包时间突增比例阈值（%）" align="center" width="85"></el-table-column>
      </el-table-column>

      <el-table-column label="指定省份运营商收到302请求数比例" align="center">
        <el-table-column prop="response302QueryCount" label="响应的302请求数" align="center"></el-table-column>
        <el-table-column prop="ispProvince302QueryPercentThre" label="指定省份运营商收到302请求数比例阈值（%）" align="center" width="85"></el-table-column>
        <el-table-column prop="ispProvince302QueryDecreasePercentThre" label="指定省份运营商收到302请求数比例突增阈值（%）" align="center" width="85"></el-table-column>
      </el-table-column>

      <el-table-column label="操作" align="center" width="140" fixed="right">
        <template slot-scope="scope">
          <div>
            <el-button type="text" @click="handleView(scope.row)">查看</el-button>
            <el-button type="text" @click="handleEdit(scope.row)">修改</el-button>
            <el-button type="text" @click="handleDelete(scope.row)">删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-row style="text-align: center; padding-top: 20px">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.page_size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      ></el-pagination>
    </el-row>

    <el-row>
      <!-- 新增 -->
      <add-dialog
        v-if="addDialog"
        :rowData="rowData"
        @close="addDialog = false"
        @refresh="onSearch"
      ></add-dialog>
      <!-- 修改 -->
      <add-dialog
        v-if="editDialog"
        :is-edit="true"
        :rowData="rowData"
        @close="editDialog = false"
        @refresh="onSearch"
      ></add-dialog>
      <!-- 查看 -->
      <add-dialog
        v-if="viewDialog"
        :is-view="true"
        :rowData="rowData"
        @close="viewDialog = false"
        @refresh="onSearch"
      ></add-dialog>
    </el-row>

  </el-card>
</template>
<script>

import http from "@/api/http.js"
import addDialog from "@/views/monitor-alarm-threshold-config/list/dialog/addDialog.vue"

export default {
  name: "monitor-alarm-threshold-config",
  components: {
    addDialog
  },
  props: [],
  data() {
    return {
      querying: false,
      tableData: [],
      domainList: [],
      editDialog: false,
      addDialog: false,
      viewDialog: false,
      rowData: {},
      pagination: {
        total: 0,
        page: 1,
        page_size: 20,
      },
      searchForm: {
        lakeName: '',
      },
    };
  },
  computed: {},
  created() {},
  watch: {},
  mounted() {
    this.onSearch()
  },
  methods: {
    onSearch() {
      this.pagination.page = 1;
      this.query();
      this.getDomainList()
    },
    async query() {
      let params = {
        domain: this.searchForm.domain,
        page: this.pagination.page,
        page_size: this.pagination.page_size,
      };
      this.querying = true;
      try {
        await http.get(`/sda/monitor/domains/thresholdConf/get`, params).then((res) => {
          this.tableData = res && res.data && res.data.items;
          this.pagination.total = res && res.data && res.data.total;
          this.querying = false;
        });
      } catch (error) {
        this.querying = false;
      } finally {
        this.querying = false;
      }
    },
    handleAdd() {
      this.addDialog = true
    },
    handleEdit(row) {
      this.editDialog = true
      this.rowData = structuredClone(row)
    },
    handleView(row) {
      this.viewDialog = true
      this.rowData = structuredClone(row)
    },
    async handleDelete(row) {
      await this.$confirm("确定删除该条数据吗？", "提示", {
        type: "warning",
      })
      const res = await http.delete(`/sda/monitor/domains/thresholdConf/${row.id}`)
      if (res && res.code === 100000) {
        let msg = (res && res.message) || "删除成功"
        this.$message.success(msg);
        this.onSearch()
      }
    },
    handleSizeChange(val) {
      this.pagination.page = 1;
      this.pagination.page_size = val;
      this.query();
    },
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.query();
    },
    // 获取域名数据
    async getDomainList() {
      try {
        await http.get(`/sda/monitor/domains/thresholdConf/domains`).then((res) => {
          this.domainList = (res && res.data) || []
        });
      } catch (error) {
        this.$message.warning(error)
      }
    },
  },
};
</script>
<style scoped lang="scss">
.btn {
  float: right;
}
.text-wrapper {
  color: #FF9831;
  cursor: pointer;
}
</style>
