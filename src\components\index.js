import Vue from 'vue'

// 扫描当前目录下.vue结尾的文件，不扫描子文件夹
const componentsContext = require.context('./', false, /\.vue$/)

// 枚举componentsContext对象的属性(文件名)，结果是一个数组
componentsContext.keys().forEach(component => {
  const componentConfig = componentsContext(component)
  
  // 兼容 import export 和 require module.export 两种规范
  const comp = componentConfig.default || componentConfig
  Vue.component(comp.name, comp)
})