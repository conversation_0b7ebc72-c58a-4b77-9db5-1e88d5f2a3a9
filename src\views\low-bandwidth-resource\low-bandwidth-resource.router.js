import EmptyLayout from "@/package/src/components/EmptyLayout";

export default [
  {
    path: '/lowBandwidthResource',
    component: EmptyLayout,
    name: "lowBandwidthResource",
    meta: {
      title: '小带宽设备验收'
    },
    children: [
      {
        path: 'deviceInfo',
        name: "lowBandwidthResourceDeviceInfo",
        component: resolve => require(['@/views/low-bandwidth-resource/list/index.vue'], resolve),
        meta: {
          title: '小带宽资源设备信息',
        },
      },
      {
        path: 'deviceAcceptance',
        name: "deviceAcceptance",
        component: resolve => require(['@/views/low-bandwidth-resource/device-acceptance/index.vue'], resolve),
        meta: {
          title: '设备验收信息',
        },
      }
    ]
  },
]
