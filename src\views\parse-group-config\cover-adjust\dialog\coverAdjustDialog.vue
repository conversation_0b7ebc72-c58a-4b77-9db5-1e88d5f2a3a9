<template>
  <el-dialog
    append-to-body
    :title="dialogTitle"
    :visible="true"
    :close-on-click-modal="false"
    width="600px"
    @close="handleCancel"
  >
    <el-row>
      <el-form :inline="true" ref="dialogForm" :model="dialogForm" :rules="rules" label-width="140px" label-position="right">
        <el-row>
          <el-form-item label="解析组" prop="pg_id">
            <el-select v-model="dialogForm.pg_id" placeholder="请选择解析组" filterable clearable :disabled="isEdit" class="w-300">
              <el-option v-for="(item, index) in parseGroupList" :key="index" :label="item.parse_group_name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="优先级" prop="priority">
            <el-input v-model.number="dialogForm.priority" placeholder="优先级 0-100" clearable class="w-300"></el-input>
          </el-form-item>
        </el-row>
      </el-form>
    </el-row>

    <div slot="footer" class="dialog-footer">
      <el-button size="medium" @click="handleCancel">取 消</el-button>
      <el-button size="medium" type="primary" @click="handleSubmit" :loading="submitLoading" :disabled="submitLoading">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import http from '@/api/http.js'
import { mapState } from "vuex";

export default {
  components: {},
  props: {
    rowData: {
      type: Object,
      default: null
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      submitLoading: false,
      dialogForm: {
        pg_id: "",
        priority: 0,
      },
      rules: {
        pg_id: [
          { required: true, message: "请选择解析组", trigger: "change"}
        ],
        priority: [
          { required: true, message: "请输入优先级", trigger: "blur"},
          { trigger: "blur", pattern: /^[0-9]*$/, message: "请输入整数，最大100，最小0" },
          {
            validator: (rule, value, callback) => {
              if (value > 100 || value < 0) {
                callback(new Error("请输入整数，最大100，最小0"));
              } else callback();
            },
            trigger: ["blur", "change"],
          },
        ],
      },
    }
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? '修改覆盖管理' : '新增覆盖管理';
    },
    ...mapState({
      parseGroupList: (state) => state.baseData.parseGroupList,
    }),
  },
  mounted() {
    if (this.isEdit) {
      this.handleInitData()
    }
  },
  methods: {
    async handleSubmit() {
      try {
        await new Promise((resolve, reject) => {
          this.$refs.dialogForm.validate(valid => {
            valid ? resolve() : reject('检查错误')
          })
        })
      } catch (err) {
        return false
      }
      this.submitLoading = true
      let params = {
        ...this.dialogForm,
        operator: window.localStorage.getItem('userInfo'),
      }
      let res = {}
      let successMsg = "添加成功"
      if (this.isEdit) {
        if (params.pg_id) {
          delete params.pg_id
        }
        res = await http.patch(`/sda/parse_group/update/${this.rowData.id}`, params)
        successMsg = "修改成功"
      } else {
        res = await http.post(`/sda/parse_group/create`, params)
      }
      if (res && res.code === 100000) {
        this.$message.success(successMsg);
        this.$emit("close")
        this.$emit("refresh")
        this.submitLoading = false
      } else {
        this.submitLoading = false
      }
    },
    handleCancel() {
      this.$emit("close")
      this.$emit("refresh")
    },
    handleInitData() {
      const row = structuredClone(this.rowData)
      this.dialogForm.pg_id = row.pg_id
      this.dialogForm.priority = row.priority
    },
  },
}
</script>

<style scoped lang="scss">
.w-300 {
  width: 300px;
}
</style>