<template>
  <el-dialog
    :title="title"
    :visible="visible"
    append-to-body
    destroy-on-close
    :close-on-click-modal="false"
    :width="dialogWidth"
    :close-on-press-escape="false"
    :before-close="onCloseDialog"
    custom-class="preview-dialog"
  >
    <div class="nested">
      <slot></slot>
    </div>
    <template v-if="isDiff && showCodemirror">
      <codemirror
        class="codemirror-custom-preview"
        v-if="showCodemirror"
        ref="cmMerge"
        :merge="true"
        :options="cmOption"
      />
    </template>
    <template v-if="!isDiff && showCodemirror">
      <codemirror
        class="codemirror-custom-preview"
        v-if="showCodemirror"
        v-model="cmOption.value"
        ref="cmMerge"
        :merge="false"
        :options="cmOption"
      />
    </template>
  </el-dialog>
</template>

<script>
import { codemirror } from "vue-codemirror";
import "codemirror/lib/codemirror.css";
import "codemirror/mode/nginx/nginx.js";
//引入自动更新
import "codemirror/addon/display/autorefresh";
//代码版本差异比较
import "codemirror/addon/merge/merge.css";
import "codemirror/addon/merge/merge.js";
// theme
import "codemirror/theme/eclipse.css";
import DiffMatchPatch from "diff-match-patch";

window.diff_match_patch = DiffMatchPatch;
window.DIFF_DELETE = -1;
window.DIFF_INSERT = 1;
window.DIFF_EQUAL = 0;

export default {
  name: "codemirrorPreviewDialog",
  components: { codemirror },
  props: {
    title: {
      type: String,
      default: "预览",
    },
    visible: {
      type: Boolean,
      default: false,
    },
    dialogWidth: {
      type: String,
      default: "800px",
    },
    isDiff: {
      type: Boolean,
      default: false,
    },
    origin: {
      type: null,
      default: null,
    },
    originLeft: {
      type: null,
      default: null,
    },
  },
  data() {
    return {
      cmOption: {
        value: "",
        origLeft: "",
        autoRefresh: true,
        mode: "text/x-nginx-conf", // 模式
        theme: "eclipse", // 主题
        lineNumbers: true, // 是否显示行号
        lineWrapping: true, // 是否自动换行
        showDifferences: true, // 当为true(默认值)时, 更改的文本片段将高亮显示
        collapseIdentical: true, // 当为true(默认为false)时，未修改的文本段将被折叠。
        connect: "center", // 设置用于连接更改的代码块的样式
        matchBrackets: true, // 匹配括号
        smartIndent: true, // 智能缩进
        readOnly: true, // 只读
        revertButtons: false,
      },
      showCodemirror: false,
    };
  },
  watch: {
    visible(val) {
      if (!val) return;
      this.$set(this.cmOption, "value", JSON.stringify(this.origin, null, 2));
      if (this.isDiff) {
        this.$set(
          this.cmOption,
          "origLeft",
          JSON.stringify(this.originLeft, null, 2)
        );
      }
      this.showCodemirror = true;
    },
  },
  methods: {
    onCloseDialog() {
      this.showCodemirror = false;
      this.$emit("update:visible", false);
    },
  },
};
</script>

<style lang="scss">
@mixin codeMirror {
  height: calc(65vh - 20px) !important;
}
.codemirror-custom-preview {
  height: 65vh !important;
  .CodeMirror-merge {
    @include codeMirror;
  }
  .CodeMirror-code {
    div {
      line-height: 20px;
    }
  }
  .CodeMirror {
    @include codeMirror;
  }
}
.preview-dialog {
  margin-top: 4vh !important;
  .el-dialog__body {
    padding-bottom: 8px !important;
    height: 65vh;
  }
}
</style>
