@import "../var";
@import "../mixins";

@mixin tag-style($type) {
  color: $type;
  background-color: rgba($type, 0.1);
  border: 1px solid rgba($type, 0.2);
  .el-tag__close {
    color: $type;
    background-color: transparent;
  }
}

$tag-padding-top-bottom: 2px;
$tag-padding-left-right: 7px;
$tag-line-height: 16px;
$tag-height: $tag-line-height + ($tag-padding-top-bottom * 2) + 2px;

.el-tag {
  @include font-text;
  padding: $tag-padding-top-bottom $tag-padding-left-right;
  line-height: $tag-line-height;
  height: $tag-height;
  border-radius: $border-radius-small;

  @include tag-style($color-assistant-blue);

  &.el-tag--info {
    background-color: $bg-color-table__header;
    border: 1px solid $border-color;
    color: $font-color-primary;
    .el-tag__close {
      color: $font-color-primary;
      background-color: transparent;
    }
  }

  &.el-tag--danger {
    //color: $color-assistant-red;
    //background-color: rgba($color-assistant-red, 0.1);
    //border: rgba($color-assistant-red, 0.2);
    @include tag-style($color-assistant-red);
  }

  &.el-tag--warning {
    @include tag-style($color-assistant-yellow);
  }

  &.el-tag--success {
    @include tag-style($color-assistant-green);
  }
}
