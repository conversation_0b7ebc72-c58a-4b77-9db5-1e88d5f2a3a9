<template>
  <el-dialog
    append-to-body
    :title="title"
    :visible="true"
    :close-on-click-modal="false"
    width="600px"
    @close="handleCancel"
    :validate-on-rule-change="false"
  >
    <el-row>
      <el-form :inline="true" ref="dialogForm" :model="dialogForm" :rules="rules" label-width="140px" label-position="right">
        <el-row>
          <el-form-item label="lake" prop="lake_id">
            <el-select v-model="dialogForm.lake_id" placeholder="请选择lake" style="width:300px" :disabled="isEdit" filterable clearable>
              <el-option :value="-1" label="ALL" />
              <el-option v-for="(item, index) in lakeList" :key="index" :label="item.bk_inst_name" :value="item.bk_inst_id"></el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="解析组" prop="pg_id">
            <el-select v-model="dialogForm.pg_id" placeholder="请选择解析组" filterable clearable style="width:300px" :disabled="isEdit">
              <el-option :value="-1" label="ALL" />
              <el-option v-for="(item, index) in parseGroupList" :key="index" :label="item.parse_group_name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="业务5xx异常个数" prop="abnormal_count">
            <el-input
              v-model.number="dialogForm.abnormal_count"
              placeholder="请输入业务5xx异常个数"
              clearable
              style="width:300px"
            ></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="连续异常次数" prop="abnormal_times">
            <el-input
              v-model.number="dialogForm.abnormal_times"
              placeholder="请输入连续异常次数"
              clearable
              style="width:300px"
            ></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="持续异常周期" prop="abnormal_period">
            <el-input
              v-model.number="dialogForm.abnormal_period"
              placeholder="请输入持续异常周期"
              clearable
              style="width:300px"
            ></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="状态" prop="state">
            <el-select
              v-model.number="dialogForm.state"
              filterable
              placeholder="请选择状态"
              clearable
              style="width:300px"
            >
              <el-option :value="0" label="启用"></el-option>
              <el-option :value="1" label="禁用"></el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <!-- vip告警切换 -->
        <el-row>
          <el-form-item label="vip告警切换" prop="vip_dispatch_switch">
            <el-switch
              v-model="dialogForm.vip_dispatch_switch"
              :inactive-value="0"
              :active-value="1"
              active-color="#13ce66"
              @change="handleVipDispatchSwitchChange"
            ></el-switch>
          </el-form-item>
        </el-row>
        <!-- vip告警自动恢复配置 -->
        <el-row>
          <el-form-item label="vip告警自动恢复" prop="vip_auto_rec_switch">
            <el-switch
              v-model="dialogForm.vip_auto_rec_switch"
              :inactive-value="0"
              :active-value="1"
              active-color="#13ce66"
              :disabled="!dialogForm.vip_dispatch_switch"
            ></el-switch>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="自动恢复周期" prop="vip_auto_rec_period">
            <el-input
              v-model.number="dialogForm.vip_auto_rec_period"
              placeholder="请输入自动恢复周期"
              clearable
              style="width:300px"
            >
              <template slot="append">s</template>
          </el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="自动恢复限制次数" prop="vip_auto_rec_limit">
            <el-input
              v-model.number="dialogForm.vip_auto_rec_limit"
              placeholder="请输入自动恢复限制次数"
              clearable
              style="width:300px"
            ></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="自动恢复限制周期" prop="vip_auto_rec_limit_period">
            <el-input
              v-model.number="dialogForm.vip_auto_rec_limit_period"
              placeholder="请输入自动恢复限制周期"
              clearable
              style="width:300px"
            >
            </el-input>
          </el-form-item>
        </el-row>
      </el-form>
    </el-row>

    <div slot="footer" class="dialog-footer">
      <el-button size="medium" @click="handleCancel">取 消</el-button>
      <el-button size="medium" type="primary" @click="handleSubmit" :loading="submiting" :disabled="submiting">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import http from '@/api/http.js'
import { mapState } from "vuex";

export default {
  components: {},
  props: {
    isEdit: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: null
    },
  },
  data() {
    return {
      submiting: false,
      dialogForm: {
        lake_id: "", // lake
        abnormal_count: "", // 业务5xx异常个数
        abnormal_times: "", // 连续异常次数
        abnormal_period: "", // 持续异常周期
        state: "", // 状态
        vip_dispatch_switch: 0, // vip告警切换
        vip_auto_rec_switch: 0, // vip告警自动恢复
        vip_auto_rec_period: null, // 自动恢复周期
        vip_auto_rec_limit: null, // 自动恢复限制次数
        vip_auto_rec_limit_period: null, // 自动恢复限制周期
      },
    }
  },
  computed: {
    rules() {
      return {
        lake_id: [
          { required: true, message: "请选择lake", trigger: "change"}
        ],
        pg_id: [
          { required: true, message: "请选择解析组", trigger: "change"}
        ],
        abnormal_count: [
          { required: true, message: "请输入业务5xx异常个数", trigger: "blur"},
          { trigger: "blur", pattern: /^[0-9]*$/, message: "请输入大于等于0的整数" },
          {
            validator: (rule, value, callback) => {
              if (value < 0) {
                callback(new Error("请输入大于等于0的整数"));
              } else callback();
            },
            trigger: ["blur", "change"],
          },
        ],
        abnormal_times: [
          { required: true, message: "请输入连续异常次数", trigger: "blur"},
          { trigger: "blur", pattern: /^[0-9]*$/, message: "请输入大于等于0的整数" },
          {
            validator: (rule, value, callback) => {
              if (value < 0) {
                callback(new Error("请输入大于等于0的整数"));
              } else callback();
            },
            trigger: ["blur", "change"],
          },
        ],
        abnormal_period: [
          { required: true, message: "请输入持续异常周期", trigger: "blur"},
          { trigger: "blur", pattern: /^[0-9]*$/, message: "请输入大于等于0的整数" },
          {
            validator: (rule, value, callback) => {
              if (value < 0) {
                callback(new Error("请输入大于等于0的整数"));
              } else callback();
            },
            trigger: ["blur", "change"],
          },
        ],
        state: [
          { required: true, message: "请选择状态", trigger: "change"}
        ],
        vip_auto_rec_switch: [
          { required: true, message: "请选择vip告警自动恢复状态", trigger: "change"}
        ],
        vip_auto_rec_period: [
          { required: !!this.dialogForm.vip_auto_rec_switch, message: "请输入自动恢复周期", trigger: "blur"},
          this.limitRules,
        ],
        vip_auto_rec_limit: [
          { required: !!this.dialogForm.vip_auto_rec_switch, message: "请输入自动恢复限制次数", trigger: "blur"},
          this.limitRules,
        ],
        vip_auto_rec_limit_period: [
          { required: !!this.dialogForm.vip_auto_rec_switch, message: "请输入自动恢复限制周期", trigger: "blur"},
          this.limitRules,
        ],
      }
    },
    limitRules() {
      return {
        type: "number",
        min: this.dialogForm.vip_auto_rec_switch ? 1 : 0,
        max: Math.MAX_SAFE_INTEGER,
        message: `请输入大于等于${this.dialogForm.vip_auto_rec_switch ? 1 : 0}的整数`,
        trigger: ["blur", "change"],
      }
    },
    title() {
      return this.isEdit ? "修改" : "新增";
    },
    ...mapState({
      lakeList: (state) => state.baseData.lakeList,
      parseGroupList: (state) => state.baseData.parseGroupList,
    }),
  },
  mounted() {
    if (this.isEdit) {
      this.handleInitData()
    }
  },
  methods: {
    async handleSubmit() {
      try {
        await new Promise((resolve, reject) => {
          this.$refs.dialogForm.validate(valid => {
            valid ? resolve() : reject('检查错误')
          })
        })
      } catch (err) {
        return false
      }
      this.submiting = true
      let params = {
        ...this.dialogForm,
        operator: window.localStorage.getItem('userInfo'),
      }
      let res = {}
      let successMsg = "添加成功"
      if (this.isEdit) {
        // 修改
        res = await http.patch(`/sda/req_abnormal/lake/parse_groups/${this.rowData.id}`, params)
        successMsg = "修改成功"
      } else {
        // 新增
        res = await http.post(`/sda/req_abnormal/lake/parse_groups`, params)
      }
      if (res && res.code === 100000) {
        this.$message.success(successMsg);
        this.$emit("close")
        this.$emit("refresh")
        this.submiting = false
      } else {
        this.submiting = false
      }
    },
    handleCancel() {
      this.$emit("close")
      this.$emit("refresh")
    },
    handleVipDispatchSwitchChange(value) {
      if (!value) {
        this.dialogForm.vip_auto_rec_switch = 0;
      }
    },
    handleInitData() {
      const row = structuredClone(this.rowData)
      this.dialogForm.lake_id = row.lake_id
      this.dialogForm.pg_id = row.pg_id
      // 业务5xx异常个数
      this.dialogForm.abnormal_count = row.abnormal_count
      // 连续异常次数
      this.dialogForm.abnormal_times = row.abnormal_times
      // 持续异常周期
      this.dialogForm.abnormal_period = row.abnormal_period
      this.dialogForm.state = row.state
      // vip告警切换
      this.dialogForm.vip_dispatch_switch = row.vip_dispatch_switch
      // vip告警自动恢复相关配置
      this.dialogForm.vip_auto_rec_switch = row.vip_auto_rec_switch || 0
      this.dialogForm.vip_auto_rec_period = row.vip_auto_rec_period
      this.dialogForm.vip_auto_rec_limit = row.vip_auto_rec_limit
      this.dialogForm.vip_auto_rec_limit_period = row.vip_auto_rec_limit_period
    }
  },
}
</script>

<style scoped lang="scss"></style>
