<template>
  <el-card>
    <div slot="header" class="clearfix">
      <span>业务异常记录</span>
    </div>
    <!-- 查询条件 -->
    <el-form :model="searchForm" ref="searchForm" label-position="left" inline>
      <el-row>
        <el-form-item label="LAKE名称">
          <el-select v-model="searchForm.lake_id" placeholder="请选择LAKE名称" style="width:200px" filterable clearable @change="lakeChange">
            <el-option :value="-1" label="ALL" />
            <el-option v-for="(item, index) in lakeList" :key="index" :label="item.bk_inst_name" :value="item.bk_inst_id"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="IP">
          <el-select v-model="searchForm.ip" placeholder="请选择IP" filterable clearable style="width:220px">
            <el-option v-for="(item, index) in ipList" :key="index" :label="item.ip" :value="item.ip">
              <span style="float:left; text-overflow: ellipsis;overflow: hidden;">{{ item.hostgroup_name }}</span>
              <span style="float:right; font-size: 12px;color: #b4b4b4;">&emsp;{{item.ip }}</span>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="域名">
          <el-input v-model="searchForm.domain" placeholder="请输入域名" filterable clearable style="width:220px"></el-input>
        </el-form-item>

        <el-form-item label="客户ID">
          <el-input v-model="searchForm.account_id" placeholder="请输入客户ID" filterable clearable style="width:260px"></el-input>
        </el-form-item>
        
        <el-form-item>
          <el-button size="medium" type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-row>
    </el-form>

    <el-table :data="tableData" v-loading="querying">
      <el-table-column prop="lake_name" label="LAKE" align="center"></el-table-column>
      <el-table-column prop="ip" label="IP" align="center"></el-table-column>
      <el-table-column prop="domain" label="域名" align="center"></el-table-column>
      <el-table-column prop="account_id" label="客户ID" align="center"></el-table-column>
      <el-table-column prop="abnormal_count" label="5xx个数" align="center"></el-table-column>
      <el-table-column prop="update_time" label="更新时间" align="center">
        <template slot-scope='scope'>
          <span>{{ (scope.row.update_time * 1000) | dateFormat }}</span>
        </template>
      </el-table-column>
    </el-table>
    <el-row style="text-align: center; padding-top: 20px">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.page_size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      ></el-pagination>
    </el-row>

  </el-card>
</template>
<script>

import http from "@/api/http.js"
import { mapState } from "vuex";
import dateFormat from "@/utils/dateFormat"

export default {
  name: "disk-alarm-record",
  components: {},
  filters: {
    dateFormat: dateFormat
  },
  props: [],
  data() {
    return {
      querying: false,
      tableData: [],
      ipList: [],
      pagination: {
        total: 0,
        page: 1,
        page_size: 20,
      },
      searchForm: {
        lake_id: '',
        ip: '',
        domain: '',
        account_id: '',
      },
    };
  },
  computed: {
    ...mapState({
      lakeList: (state) => state.baseData.lakeList,
      ipListArr: (state) => state.baseData.ipList,
    }),
  },
  created() {},
  watch: {
    ipListArr: {
      deep: true,
      handler(val) {
        this.ipList = structuredClone(val)
      },
      immediate: true
    }
  },
  mounted() {
    this.onSearch()
  },
  methods: {
    async lakeChange(lake_id) {
      let params = {
        lake_id: lake_id,
        lake_type: 4,
      };
      try {
        await http.get(`/sda/basic_data/hosts`, params).then((res) => {
          this.ipList = res && res.data
        });
      } catch (error) {
        let message = error
        this.$message.error(message)
      }
    },
    onSearch() {
      this.pagination.page = 1;
      this.query();
    },
    async query() {
      let params = {
        lake_id: this.searchForm.lake_id,
        ip: this.searchForm.ip,
        domain: this.searchForm.domain,
        account_id: this.searchForm.account_id,
        page: this.pagination.page,
        page_size: this.pagination.page_size,
      };
      this.querying = true;
      try {
        await http.get(`/sda/requests/hosts/domains`, params).then((res) => {
          this.tableData = res && res.data && res.data.items;
          this.pagination.total = res && res.data && res.data.total;
          this.querying = false;
        });
      } catch (error) {
        this.querying = false;
      } finally {
        this.querying = false;
      }
    },
    handleSizeChange(val) {
      this.pagination.page = 1;
      this.pagination.page_size = val;
      this.query();
    },
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.query();
    },
  },
};
</script>
<style scoped lang="scss">
.btn {
  float: right;
}
</style>
