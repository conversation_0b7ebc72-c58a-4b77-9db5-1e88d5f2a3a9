const CryptoJS = require('crypto-js')

/**
 * 自定义十六进制字符串解析为WordArray
 * 每两个十六进制字符代表一个字节
 * @param {string} hexStr - 十六进制字符串
 * @returns {CryptoJS.lib.WordArray} WordArray对象
 */
function parseHexToWordArray(hexStr) {
  // 验证输入是否为有效的十六进制字符串
  if (!/^[0-9a-fA-F]*$/.test(hexStr)) {
    throw new Error('输入必须是有效的十六进制字符串')
  }

  // 确保字符串长度为偶数（每两个字符代表一个字节）
  if (hexStr.length % 2 !== 0) {
    throw new Error('十六进制字符串长度必须为偶数')
  }

  const words = []
  const sigBytes = hexStr.length / 2  // 字节数

  // 每4个字节（8个十六进制字符）组成一个32位字
  for (let i = 0; i < hexStr.length; i += 8) {
    let word = 0
    // 处理当前字的4个字节
    for (let j = 0; j < 8 && i + j < hexStr.length; j += 2) {
      const byte = parseInt(hexStr.substring(i + j, i + j + 2), 16)
      word = (word << 8) | byte
    }
    words.push(word)
  }

  // 创建WordArray对象
  return CryptoJS.lib.WordArray.create(words, sigBytes)
}

// AES密钥和初始化向量
const AES_KEY = window.api.aesKey  // 64个十六进制字符的密钥，解析成32字节
const AES_IV = window.api.aesIv  // 32个十六进制字符的初始化向量，解析为16字节

// 验证密钥和初始化向量的长度
if (!AES_KEY || AES_KEY.length !== 64) {
  throw new Error('AES密钥必须是64个十六进制字符（32字节）')
}
if (!AES_IV || AES_IV.length !== 32) {
  throw new Error('AES初始化向量必须是32个十六进制字符（16字节）')
}

// 使用自定义方法将十六进制字符串解析为WordArray对象
const key = parseHexToWordArray(AES_KEY);
const iv = parseHexToWordArray(AES_IV);

console.log(key, iv)

/**
 * AES加密 + Base64编码
 * @param {string} text - 需要加密的明文
 * @returns {string} 加密后的密文
 */
export function encryptPassword(text) {
  try {
    const encrypted = CryptoJS.AES.encrypt(text, key, {
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    })

    return encrypted.toString();
  } catch (error) {
    console.error('加密失败:', error)
    return text
  }
}

console.log(encryptPassword('aP9#xT7&vQ2@LmZ4'))

/**
 * Base64解码 + AES解密
 * @param {string} encryptedText - 需要解密的密文
 * @returns {string} 解密后的明文
 */
export function decryptPassword(encryptedText) {
  try {
    const decrypted = CryptoJS.AES.decrypt(encryptedText, key, {
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    })
    return decrypted.toString(CryptoJS.enc.Utf8)
  } catch (error) {
    console.error('解密失败:', error)
    return encryptedText
  }
}
