@import '../var.scss';
@import '../mixins.scss';

$alert-icon-size-normal: 14px;
$alert-icon-size-big: 24px;

.el-alert {
  // 消息提示
  &.el-alert--info {
    border: 1px solid $border-color;
  }

  &.el-alert--success {
    border: 1px solid rgba($color-assistant-green, 0.2);
  }

  &.el-alert--warning {
    border: 1px solid rgba($color-assistant-yellow, 0.2);
  }

  &.el-alert--error {
    border: 1px solid rgba($color-assistant-red, 0.2);
  }

  & + .el-alert {
    margin-top: $input-spacing;
  }

  .el-alert__icon {
    //width: $alert-icon-size-normal;
    //height: $alert-icon-size-normal;
    font-size: $alert-icon-size-normal;

    margin-right: 10px;

    &.is-big, &.is-big:before {
      //width: $alert-icon-size-big;
      //height: $alert-icon-size-big;
      font-size: $alert-icon-size-big;

      margin-right: 16px;
    }

    @include use-icon-font;
  }

  .el-alert__content {
    color: $font-color-primary2;

    .el-alert__title {
      font-size: $font-size-normal;

      &.is-bold {
        color: $font-color-primary;
      }
    }

    .el-alert__description {
      font-size: $font-size-normal;
      color: $font-color-primary2;
    }
  }

  .el-alert__closebtn {
    color: $font-color-primary2;
  }
}
