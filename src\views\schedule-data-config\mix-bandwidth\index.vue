<template>
  <el-card>
    <div slot="header">
      <span>融合带宽灰度</span>
    </div>
    <!-- 查询条件 -->
    <el-form :model="searchForm" ref="searchForm" label-position="left" inline>
      <el-row>
        <el-form-item label="接入层">
          <el-select v-model="searchForm.access_id" placeholder="请选择接入层" filterable clearable style="width:300px">
            <el-option value="-1" label="all" />
            <el-option v-for="(item, index) in accessList" :key="index" :label="item.access_name" :value="item.access_id"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button size="medium" type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
        <span style="float:right">
          <el-button size="medium" @click="handleBatchDelete">批量删除</el-button>
          <el-button size="medium" @click="handleAdd">新增</el-button>
        </span>
      </el-row>
    </el-form>

    <el-table :data="tableData" v-loading="querying" @selection-change="handleSelectionChange">
      <el-table-column type="selection" align="center" width="55"></el-table-column>
      <el-table-column prop="access_id" label="接入层ID" align="center"></el-table-column>
      <el-table-column prop="access_name" label="接入层名称" align="center"></el-table-column>
      <el-table-column prop="switch" label="开关" align="center">
        <template slot-scope='scope'>
          <span v-if="scope.row.switch === 1">开启</span>
          <span v-else>关闭</span>
        </template>
      </el-table-column>
      <el-table-column prop="create_time" label="创建时间" align="center">
        <template slot-scope='scope'>
          <span>{{ (scope.row.create_time * 1000) | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="update_time" label="修改时间" align="center">
        <template slot-scope='scope'>
          <span>{{ (scope.row.update_time * 1000) | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="130">
        <template slot-scope="scope">
          <div>
            <el-button type="text" @click="handleEdit(scope.row)">修改</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-row class="pager">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.page_size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      ></el-pagination>
    </el-row>

    <el-row>
      <!-- 新增 -->
      <mix-bandwidth-dialog
        v-if="addDialog"
        :accessIdList="accessIdList"
        @close="addDialog = false"
        @refresh="onSearch"
      ></mix-bandwidth-dialog>
      <!-- 修改 -->
      <mix-bandwidth-dialog
        v-if="editDialog"
        :is-edit="true"
        :rowData="rowData"
        @close="editDialog = false"
        @refresh="onSearch"
      ></mix-bandwidth-dialog>
    </el-row>

  </el-card>
</template>
<script>

import http from "@/api/http.js"
import mixBandwidthDialog from "@/views/schedule-data-config/mix-bandwidth/dialog/mixBandwidthDialog.vue"
import { mapState } from "vuex";
import dateFormat from "@/utils/dateFormat"

export default {
  name: "mix-bandwidth",
  components: {
    mixBandwidthDialog,
  },
  filters: {
    dateFormat: dateFormat
  },
  props: [],
  data() {
    return {
      querying: false,
      tableData: [],
      accessIdList: [],
      rowData: {},
      addDialog: false,
      editDialog: false,
      pagination: {
        total: 0,
        page: 1,
        page_size: 20,
      },
      searchForm: {
        access_id: '',
      },
      multipleSelection: [],
    };
  },
  computed: {
    ...mapState({
      accessList: (state) => state.baseData.accessList,
    }),
  },
  created() {},
  async mounted() {
    this.onSearch()
  },
  watch: {},
  methods: {
    handleAdd() {
      this.addDialog = true
    },
    handleEdit(row) {
      this.editDialog = true
      this.rowData = structuredClone(row)
    },
    // 批量删除
    async handleBatchDelete(row) {
      if (!(this.multipleSelection && this.multipleSelection.length > 0)) {
        this.$message.warning("请先选择数据！")
        return;
      }
      await this.$confirm("确定删除数据吗？", "提示", {
        type: "warning",
      })
      let ids = this.multipleSelection && this.multipleSelection.map(item => item.id)
      let params = {
        data: ids
      }
      const res = await http.post(`/sdcp/mix_bw/conf/delete`, params)
      if (res && res.code === 100000) {
        let msg = (res && res.message) || "删除成功"
        this.$message.success(msg);
        this.onSearch()
      }
    },
    onSearch() {
      this.pagination.page = 1;
      this.query();
      this.getAll();
    },
    async query() {
      let params = {
        access_id: this.searchForm.access_id,
        page: this.pagination.page,
        page_size: this.pagination.page_size,
      };
      this.querying = true;
      try {
        await http.get(`/sdcp/mix_bw/conf/get`, params).then((res) => {
          this.tableData = (res && res.data && res.data.items) || [];
          this.pagination.total = res && res.data && res.data.total;
          this.querying = false;
        });
      } catch (error) {
        this.querying = false;
      } finally {
        this.querying = false;
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleSizeChange(val) {
      this.pagination.page = 1;
      this.pagination.page_size = val;
      this.query();
    },
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.query();
    },
    async getAll() {
      await http.get(`/sdcp/mix_bw/conf/get_all`).then((res) => {
        const result = (res && res.data) || [];
        this.accessIdList = result.map(item => item.access_id)
      });
    },
  },
};
</script>
<style scoped lang="scss">
.btn {
  float: right;
}
.pager {
  text-align: center;
  padding-top: 20px
}
</style>
