<template>
  <el-card>
    <!-- vip业务异常切换记录 -->
    <!-- 查询条件 -->
    <el-form :model="searchForm" ref="searchForm" label-position="left" inline>
      <div>
        <el-form-item label="状态" prop="state" class="ml12">
          <el-select v-model.number="searchForm.state" placeholder="请选择状态" filterable clearable style="width:120px">
            <el-option v-for="(item, index) in stateList" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="LAKE" prop="lake_id">
          <el-select v-model="searchForm.lake_id" placeholder="请选择LAKE" style="width:260px" filterable clearable>
            <el-option v-for="(item, index) in lakeList" :key="index" :label="item.bk_inst_name" :value="item.bk_inst_id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="主机组" prop="host_group">
          <el-input v-model="searchForm.host_group" placeholder="请输入主机组" style="width:200px" clearable></el-input>
        </el-form-item>
        <el-form-item label="vip" prop="vip">
          <el-input v-model="searchForm.vip" placeholder="请输入vip" style="width:200px" clearable></el-input>
        </el-form-item>
      </div>
      <div>
        <el-form-item label="解析组" prop="pg_id">
          <el-select v-model="searchForm.pg_id" placeholder="请选择解析组" filterable clearable style="width:260px">
            <el-option v-for="(item, index) in parseGroupList" :key="index" :label="item.parse_group_name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button size="medium" type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
        <span style="float:right">
          <el-button size="medium" @click="batchRecovery" :disabled="isBatchRecoveryBtnDisabled">批量恢复</el-button>
        </span>
      </div>
    </el-form>

    <el-table
      :data="tableData"
      v-loading="querying"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" align="center" width="55"></el-table-column>
      <el-table-column prop="id" label="ID" align="center"></el-table-column>
      <el-table-column prop="lake_name" label="LAKE" align="center"></el-table-column>
      <el-table-column prop="vip" label="vip" align="center"></el-table-column>
      <el-table-column prop="host_group" label="主机组" align="center" width="125"></el-table-column>
      <el-table-column prop="pg_name" label="解析组" align="center"></el-table-column>
      <el-table-column prop="view_name" label="区域" align="center"></el-table-column>
      <el-table-column prop="create_time" label="告警时间" align="center" width="145">
        <template slot-scope='scope'>
          <span>{{ (scope.row.create_time * 1000) | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="recovery_time" label="恢复时间" align="center" width="145">
        <template slot-scope='scope'>
          <span v-if="scope.row.recovery_time > 0">{{ (scope.row.recovery_time * 1000) | dateFormat }}</span>
          <span v-else>0</span>
        </template>
      </el-table-column>
      <el-table-column prop="latest_abnormal_time" label="最近一次业务异常时间" align="center" width="145">
        <template slot-scope='scope'>
          <span v-if="scope.row.latest_abnormal_time > 0">{{ (scope.row.latest_abnormal_time * 1000) | dateFormat }}</span>
          <span v-else>0</span>
        </template>
      </el-table-column>
      <el-table-column prop="operator" label="操作人" align="center"></el-table-column>
      <el-table-column prop="remark" label="备注" align="center"></el-table-column>
      <el-table-column prop="state" label="当前状态" align="center" width="120px">
        <template slot-scope="scope">
          <el-tag slot="reference" v-if="scope.row.state === 0">进行中</el-tag>
          <el-tag slot="reference" v-if="scope.row.state === 1" type="success">人工恢复</el-tag>
          <el-tag slot="reference" v-if="scope.row.state === 2" type="success">自动恢复</el-tag>
          <el-tag slot="reference" v-if="scope.row.state === 3" type="info">取消</el-tag>
          <el-tag slot="reference" v-if="scope.row.state === 4" type="danger">异常</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="180">
        <template slot-scope="scope">
          <div>
            <el-button type="text" @click="handleRecovery(scope.row)" :disabled="scope.row.state !== 0">恢复</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-row style="text-align: center; padding-top: 20px">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.page_size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      ></el-pagination>
    </el-row>

    <el-row>
      <batch-recovery-dialog
        v-if="dialogVisible"
        :multipleSelection="multipleSelection"
        @close="dialogVisible = false"
        @refresh="onSearch"
      ></batch-recovery-dialog>
    </el-row>

  </el-card>
</template>
<script>

import http from "@/api/http.js"
import { mapState } from "vuex";
import dateFormat from "@/utils/dateFormat"
import batchRecoveryDialog from "@/views/business-abnormal/vip-business-abnormal-switch-record/dialog/batchRecoveryDialog.vue"

export default {
  name: "vip-business-abnormal-switch-record",
  components: {
    batchRecoveryDialog
  },
  filters: {
    dateFormat: dateFormat
  },
  props: [],
  data() {
    return {
      querying: false,
      dialogVisible: false,
      tableData: [],
      pagination: {
        total: 0,
        page: 1,
        page_size: 20,
      },
      multipleSelection: [],
      stateList: [
        { label: "进行中", value: 0 },
        { label: "人工恢复", value: 1 },
        { label: "自动恢复", value: 2 },
        { label: "取消", value: 3 },
        { label: "异常", value: 4 }
      ],
      searchForm: {
        lake_id: '',
        host_group: '', // 主机组
        vip: '',
        pg_id: '', // 解析组
        state: 0,
      },
    };
  },
  computed: {
    ...mapState({
      lakeList: (state) => state.baseData.lakeList,
      parseGroupList: (state) => state.baseData.parseGroupList,
    }),
    isBatchRecoveryBtnDisabled() {
      if (!(this.tableData && this.tableData.length > 0)) return false;
      const disabled = !this.tableData.every(item => item.state === 0)
      return disabled;
    },
  },
  created() {},
  watch: {},
  mounted() {
    this.onSearch()
  },
  methods: {
    onSearch() {
      this.pagination.page = 1;
      this.query();
    },
    async query() {
      let params = {
        ...this.searchForm,
        page: this.pagination.page,
        page_size: this.pagination.page_size,
      };
      this.querying = true;
      try {
        await http.get(`/sda/req_abnormal/vip_dispatcher/records`, params).then((res) => {
          this.tableData = res && res.data && res.data.items;
          this.pagination.total = res && res.data && res.data.total;
          this.querying = false;
        });
      } catch (error) {
        this.querying = false;
      } finally {
        this.querying = false;
      }
    },

    handleSelectionChange(val) {
      this.multipleSelection = JSON.parse(JSON.stringify(val));
    },
    // 批量恢复
    async batchRecovery() {
      if (!(this.multipleSelection && this.multipleSelection.length > 0)) {
        this.$message.warning("请先选择数据！")
        return;
      }
      this.dialogVisible = true
    },
    // 恢复
    async handleRecovery(row) {
      this.multipleSelection = [row]
      this.dialogVisible = true
    },
    handleSizeChange(val) {
      this.pagination.page = 1;
      this.pagination.page_size = val;
      this.query();
    },
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.query();
    },
  },
};
</script>
<style scoped lang="scss">
.btn {
  float: right;
}
.ml12 {
  margin-left: 12px;
}
</style>
