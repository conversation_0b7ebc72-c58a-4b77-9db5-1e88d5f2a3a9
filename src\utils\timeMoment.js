import moment from 'moment'

/**
 * 非表格数据日期格式化
 * @param val
 * @param formatString
 * @returns {string}
 */
export function format(val, formatString = 'YYYY-MM-DD HH:mm:ss') {
  if (moment(val).isValid()) {
    return moment(val).format(formatString)
  }
  return '- -'
}

/**
 * 用于表格数据日期格式化
 * @param multi
 * @param formatString
 * @returns {function(*, *, *=)}
 */
export function formatInTable(formatString = 'YYYY-MM-DD', multi = 1) {
  return (row, col, val) => {
    if (moment(val * multi).isValid()) {
      return moment(val * multi).format(formatString)
    }
    return ''
  }
}

/**
 * 用于表格数据日期格式化(UTC)
 * @param multi
 * @param formatString
 * @returns {function(*, *, *=)}
 */
export function formatUTCInTable(formatString = 'YYYY-MM-DD') {
  return (row, col, val) => {
    if (moment(val).isValid()) {
      return moment(new Date(val)).format(formatString)
    }
    return ''
  }
}

export function timestampToTime(timestamp) {
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = ('0' + (date.getMonth() + 1)).slice(-2)
  const day = ('0' + date.getDate()).slice(-2)
  const hours = ('0' + date.getHours()).slice(-2)
  const minutes = ('0' + date.getMinutes()).slice(-2)
  const seconds = ('0' + date.getSeconds()).slice(-2)

  const time = year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds
  return time
}

const durationUnits = [
  {years: '年'},
  {months: '个月'},
  {weeks: '周'},
  {days: '天'},
  {hours: '小时'},
  {minutes: '分钟'},
  {seconds: '秒'}
]

export function formatDuration(end, start) {
  let startMoment = moment(start, 'YYYY-MM-DD HH:mm:SS')
  let endMoment = moment(end, 'YYYY-MM-DD HH:mm:SS')
  let duration = moment.duration(endMoment - startMoment, 'ms')
  let result = []
  for (var i = 0, len = durationUnits.length; i < len; i++) {
    for (var prop in durationUnits[i]) {
      var num = duration._data[prop]
      if (num > 0) {
        result.push(num + durationUnits[i][prop])
      }
    }
  }
  if (result.length > 0) {
    return result.join(' ')
  } else {
    return 0
  }
}

export function utcDateFormat(val, formatString = 'YYYY-MM-DD HH:mm:ss') {
  if (moment(val).isValid()) {
    return moment(new Date(val)).format(formatString)
  }
  return '- -'
}
