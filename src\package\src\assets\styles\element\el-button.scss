@import '../var.scss';

.el-button {
  &--default {
    color: $color-primary;
    border: 1px solid $color-primary;
    &:focus, &:hover {
      border-color: $color-primary;
      background: $color-primary;
      color: $font-color-white;
    }
    &.is-disabled {
      opacity: 0.35;
      color: $color-primary;
      border-color: $color-primary;
      &:hover {
        opacity:0.35;
        border-color: $color-primary;
        background: $color-primary;
        color: $font-color-white;
      }
    }
  }
  &--primary {
    &, &.is-active, &:active {
      background: $color-primary;
      border-color: $color-primary;
      color: $font-color-white;
    }
    &:focus, &:hover {
      opacity:0.8;
      background: $color-primary;
      border-color: $color-primary;
      color: $font-color-white;
    }
    &.is-disabled {
      opacity:0.35;
      background-color: $color-primary;
      border-color: $color-primary;
      &:hover {
        opacity:0.35;
        background-color: $color-primary;
        border-color: $color-primary;
      }
    }
  }
  &--success {
    &, &.is-active, &:active {
      background: $color-assistant-green;
      border-color: $color-assistant-green;
      color: $font-color-white;
    }
    &:focus, &:hover {
      opacity:0.8;
      background: $color-assistant-green;
      border-color: $color-assistant-green;
      color: $font-color-white;
    }
    &.is-disabled {
      opacity:0.35;
      background-color: $color-assistant-green;
      border-color: $color-assistant-green;
      &:hover {
        opacity:0.35;
        background-color: $color-assistant-green;
        border-color: $color-assistant-green;
      }
    }
  }
  &--wanrnig {
    &, &.is-active, &:active {
      background: $color-assistant-yellow;
      border-color: $color-assistant-yellow;
      color: $font-color-white;
    }
    &:focus, &:hover {
      opacity:0.8;
      background: $color-assistant-yellow;
      border-color: $color-assistant-yellow;
      color: $font-color-white;
    }
    &.is-disabled {
      opacity:0.35;
      background-color: $color-assistant-yellow;
      border-color: $color-assistant-yellow;
      &:hover {
        opacity:0.35;
        background-color: $color-assistant-yellow;
        border-color: $color-assistant-yellow;
      }
    }
  }
  &--danger {
    &, &.is-active, &:active {
      background: $color-assistant-red;
      border-color: $color-assistant-red;
      color: $font-color-white;
    }
    &:focus, &:hover {
      opacity:0.8;
      background: $color-assistant-red;
      border-color: $color-assistant-red;
      color: $font-color-white;
    }
    &.is-disabled {
      opacity:0.35;
      background-color: $color-assistant-red;
      border-color: $color-assistant-red;
      &:hover {
        opacity:0.35;
        background-color: $color-assistant-red;
        border-color: $color-assistant-red;
      }
    }
  }
}
