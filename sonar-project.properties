sonar.projectKey={{SONAR_PROJECT_NAME}}
sonar.projectName={{SONAR_PROJECT_NAME}}
# if you want disabled the DTD verification for a proxy problem for example, true by default
sonar.coverage.dtdVerification=false
# JUnit like test report, default value is test.xml
sonar.sources=src
#sonar.tests=test
sonar.language=js
sonar.sourceEncoding=UTF-8
sonar.exclusions=**/node_modules/**,**/*.spec.js,**/test-data/*.js,**/testing/*.js,**/assets/js/*.js
sonar.tests=src
sonar.test.inclusions=**/*.spec.js
sonar.test.exclusions=**/multiselect-dropdown/*.js
