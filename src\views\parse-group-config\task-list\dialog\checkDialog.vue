<template>
  <el-dialog
    append-to-body
    :title="dialogTitle"
    :visible="true"
    :close-on-click-modal="false"
    width="1300px"
    @close="handleCancel"
  >
    <el-row>
      <el-table :data="tableData" v-loading="submiting" border>
        <el-table-column prop="pg_name" label="解析组" align="center" width="200"></el-table-column>
        <el-table-column prop="view_name" label="区域" align="center"></el-table-column>
        <el-table-column prop="v4_peak" label="v4峰值带宽" align="center">
          <template slot-scope="scope">
            <span>{{ parseBwByteString(scope.row.v4_peak) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="v6_peak" label="v6峰值带宽" align="center">
          <template slot-scope="scope">
            <span>{{ parseBwByteString(scope.row.v6_peak) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="diff_detail" label="删除" align="left" width="400">
          <template slot-scope="scope">
            <div v-for="(item, index) in scope.row.diff_detail" :key="index">
              <span class="text-red" v-if="item.op === 1"><span style="margin-left:8px">{{ item.content }}</span></span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="diff_detail" label="新增" align="left" width="400">
          <template slot-scope="scope">
            <div v-for="(item, index) in scope.row.diff_detail" :key="index">
              <span class="text-green" v-if="item.op === 0"><span style="margin-left:8px">{{ item.content }}</span></span>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-row>

    <div slot="footer" class="dialog-footer" v-if="checkDialogType === 'check'">
      <el-button size="medium" type="primary" @click="handleTaskCheck" :loading="submiting" :disabled="submiting">审核通过并下发</el-button>
      <el-button size="medium" type="primary" @click="handleTaskCancel">驳 回</el-button>
      <el-button size="medium" @click="handleCancel">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import http from '@/api/http.js'
import commonMixin from "@/utils/common";

export default {
  components: {},
  mixins: [
    commonMixin
  ],
  props: {
    checkRowData: {
      type: Object,
      default: null
    },
    checkDialogType: {
      type: String,
      default: ""
    },
  },
  data() {
    return {
      submiting: false,
      tableData: [],
    }
  },
  computed: {
    dialogTitle() {
      let data = ""
      if (this.checkDialogType === 'check') {
        data = "审核"
      }
      return data
    },
  },
  mounted() {
    if (this.checkRowData && Object.keys(this.checkRowData).length > 0) {
      this.handleDetailDiff()
    }
  },
  methods: {
    // 审核通过并下发
    async handleTaskCheck() {
      this.submiting = true
      let res = {}
      let successMsg = "审核成功"
      res = await http.get(`/sda/change_cover/task/audit?id=${this.checkRowData.task_id}`)
      if (res && res.code === 100000) {
        this.$message.success(successMsg);
        this.$emit("close")
        this.$emit("refresh")
        this.submiting = false
      } else {
        this.submiting = false
      }
    },
    // 驳回
    async handleTaskCancel() {
      this.submiting = true
      let res = {}
      let successMsg = "驳回成功"
      res = await http.get(`/sda/change_cover/task/cancel?id=${this.checkRowData.task_id}`)
      if (res && res.code === 100000) {
        this.$message.success(successMsg);
        this.$emit("close")
        this.$emit("refresh")
        this.submiting = false
      } else {
        this.submiting = false
      }
    },
    async handleDetailDiff() {
      let detail = (this.checkRowData && this.checkRowData.detail) || []
      let ids = detail.map(item => item.id)
      let params = {
        detail_ids: ids,
      };
      const res = await http.post(`/sda/change_cover/detail/diff`, params)
      let result = (res && res.data) || []
      const data = this.handleTableData(result)
      this.tableData = data
    },
    handleTableData(data) {
      const arrData = []
      for (let i = 0; i < data.length; i++) {
        let diff = data[i].diff
        if (!diff) {
          diff = [{}]
        }
        for (let j = 0; j < diff.length; j++) {
          const info = {
            span_num: j === 0 ? diff.length : 0,
            pg_id: data[i].pg_id,
            pg_name: data[i].pg_name,
            diff_detail: diff[j].diff_detail,
            v4_peak: diff[j].v4_peak,
            v6_peak: diff[j].v6_peak,
            view_id: diff[j].view_id,
            view_name: diff[j].view_name,

          }
          arrData.push(info)
        }
      }
      return arrData;
    },
    handleCancel() {
      this.$emit("close")
      this.$emit("refresh")
    },
  },
}
</script>

<style scoped lang="scss">
.dialog-footer {
  text-align: center;
}
.text-green {
  color: #1ac45d;
}
.text-red {
  color: #df0629;
}
</style>