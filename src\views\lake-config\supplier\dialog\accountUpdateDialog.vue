<template>
  <el-dialog title="账号修改" :visible.sync="dialogVisible" width="400px" append-to-body :close-on-click-modal="false" :close-on-press-escape="false" @close="handleClose">
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="新账号" prop="account">
        <el-input v-model="form.account" placeholder="请输入新账号"></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import http from "../../http";
// import { encryptPassword } from "@/utils/crypto";

export default {
  props: {
    supplierId: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      dialogVisible: true,
      loading: false,
      form: {
        account: '',
      },
      rules: {
        account: [{ required: true, message: '请输入新账号', trigger: 'blur' }],
      }
    };
  },
  methods: {
    handleClose() {
      this.$emit('close');
    },
    async handleSubmit() {
      try {
        await this.$refs.form.validate();
        this.loading = true;
        const params = {
          id: this.supplierId,
          // account: encryptPassword(this.form.account),
          account: this.form.account
          // verify_pwd: this.$store.state.auth.encryptedPassword
        };
        const res = await http.updateSupplierAccount(params);
        if (res.code === 100000) {
          this.$message.success('修改成功');
          this.dialogVisible = false;
          this.$emit('success');
        }
      } catch (error) {
        this.$message.error('修改失败');
      } finally {
        this.loading = false;
      }
    }
  }
};
</script>
