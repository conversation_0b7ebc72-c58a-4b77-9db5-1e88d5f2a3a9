import { mapGetters, mapActions } from 'vuex'
import { decryptPassword } from '@/utils/crypto'
import authVerifyDialog from '@/components/authVerifyDialog.vue'
import authStatus from '@/components/authStatus.vue'

export default {
  components: {
    authVerifyDialog,
    authStatus
  },
  data() {
    return {
      // 认证弹窗显示状态
      authDialogVisible: false,
      // 待执行的操作
      pendingAuthAction: null,
      // 目标数据
      authTargetData: null,
      // 认证类型
      authType: 'default'
    }
  },

  computed: {
    ...mapGetters('auth', ['isVerified'])
  },

  methods: {
    ...mapActions('auth', ['verifyPassword', 'resetAuthState']),

    /**
     * 执行需要认证的操作
     * @param {Object} targetData - 目标数据（可选）
     * @param {Function} action - 需要执行的操作函数
     * @param {String} type - 认证类型（可选）
     */
    executeAuthenticatedOperation(type, action, targetData = null) {
      // 检查是否已认证
      if (this.isVerified) {
        // 已认证，直接执行操作
        if (typeof action === 'function') {
          action(targetData)
        }
      } else {
        // 未认证，保存待执行操作并显示认证弹窗
        this.pendingAuthAction = action
        this.authTargetData = targetData
        this.authType = type
        this.authDialogVisible = true
      }
    },

    /**
     * 处理认证成功
     * @param {Object} result - 认证结果
     */
    handleAuthVerifySuccess(result) {
      try {
        // 关闭认证弹窗
        this.authDialogVisible = false

        // 执行待执行的操作
        if (this.pendingAuthAction && typeof this.pendingAuthAction === 'function') {
          // 如果需要解密数据，传入加密密码
          if (this.authTargetData && this.authTargetData.needDecrypt) {
            // 解密目标数据
            const decryptedData = this.decryptAuthData(this.authTargetData, result.encryptedPassword)
            this.pendingAuthAction(decryptedData)
          } else {
            // 直接执行操作
            this.pendingAuthAction(this.authTargetData)
          }
        }

        // 清理临时数据
        this.clearAuthPendingData()

      } catch (error) {
        console.error('执行认证后操作失败:', error)
        this.$message.error('操作执行失败')
        this.clearAuthPendingData()
      }
    },

    /**
     * 处理认证弹窗关闭
     */
    handleAuthDialogClose() {
      this.authDialogVisible = false
      this.clearAuthPendingData()
    },

    /**
     * 解密认证数据
     * @param {Object} data - 需要解密的数据
     * @param {String} encryptedPassword - 加密密码
     * @returns {Object} 解密后的数据
     */
    decryptAuthData(data, encryptedPassword) {
      try {
        if (!data || !data.encryptedFields) {
          return data
        }

        const decryptedData = { ...data }

        // 解密指定字段
        data.encryptedFields.forEach(field => {
          if (decryptedData[field]) {
            decryptedData[field] = decryptPassword(decryptedData[field], encryptedPassword)
          }
        })

        return decryptedData
      } catch (error) {
        console.error('数据解密失败:', error)
        return data
      }
    },

    /**
     * 清理认证相关的临时数据
     */
    clearAuthPendingData() {
      this.pendingAuthAction = null
      this.authTargetData = null
      this.authType = 'default'
    },

    /**
     * 检查认证状态并执行操作
     * @param {Function} action - 需要执行的操作
     * @param {Object} options - 选项配置
     * @param {Object} options.targetData - 目标数据
     * @param {String} options.type - 认证类型
     * @param {Boolean} options.forceAuth - 是否强制重新认证
     */
    checkAuthAndExecute(action, options = {}) {
      const { targetData = null, type = 'default', forceAuth = false } = options

      if (forceAuth) {
        // 强制重新认证
        this.resetAuthState()
      }

      this.executeAuthenticatedOperation(action, targetData, type)
    },

    /**
     * 快速认证检查（不显示弹窗，仅返回认证状态）
     * @returns {Boolean} 是否已认证
     */
    quickAuthCheck() {
      return this.isVerified
    }
  },

  // 组件销毁时清理数据
  beforeDestroy() {
    this.clearAuthPendingData()
  }
}
