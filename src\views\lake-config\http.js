import { ajax } from "@/api/ajax";
import { formPromise } from "@/utils/fetch";

const prefix = "api/v1"
export default {
  async getSupplierList(params) {
    return await formPromise(ajax.get(`${prefix}/sda/supplier/get`, {
      params
    }))
  },
  async deleteSupplier(id) {
    return await formPromise(ajax.delete(`${prefix}/sda/supplier/delete/${id}`))
  },
  async addSupplier(data) {
    return await formPromise(ajax.post(`${prefix}/sda/supplier/create`, data))
  },
  async updateSupplier(data) {
    return await formPromise(ajax.post(`${prefix}/sda/supplier/update`, data))
  },
  async getIspList() {
    return await formPromise(ajax.get(`${prefix}/sda/supplier/isp`))
  },
  async verifyPassword(verify_pwd) {
    return await formPromise(ajax.post(`${prefix}/sda/auth_gen/verify`, { pwd: verify_pwd }))
  },
  async decryptSupplier(params) {
    return await formPromise(ajax.post(`${prefix}/sda/supplier/decrypt`, params))
  },
  async updateSupplierAccount(params) {
    return await formPromise(ajax.post(`${prefix}/sda/supplier/update_account`, params))
  },
  async resetSupplierPassword(params) {
    return await formPromise(ajax.post(`${prefix}/sda/supplier/reset_pwd`, params))
  },
  async updateSupplierStatus(params) {
    return await formPromise(ajax.post(`${prefix}/sda/supplier/update_status`, params))
  }
}

