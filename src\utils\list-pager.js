export default {
  data() {
    return {
      loading: false,
      dataList: [],
      paging: {
        page: 1,
        page_size: 10,
        total: 0
      }
    }
  },
  mounted() {
    this.query()
  },
  methods: {
    async query() {
      try {
        this.loading = true
        this.queryList && await this.queryList()
        this.loading = false
      } catch (e) {
        this.loading = false
      }
    },
    queryFilter(page_size = 10) {
      typeof page_size !== 'number' && (page_size = 10)
      this.paging.page = 1
      this.paging.page_size = page_size
      this.query()
    },
    setResult(list, total) {
      this.dataList = []
      this.dataList = list
      this.paging.total = total
      if (total > 0 && (list === null || list.length === 0) && this.paging.page > 1) {
        this.paging.page--
        this.query()
      }
    },
    pageChange(val) {
      this.paging.page = val
      this.query()
    },
    pageSizeChange(val) {
      this.paging.page = 1
      this.paging.page_size = val
      this.query()
    }
  }
}
