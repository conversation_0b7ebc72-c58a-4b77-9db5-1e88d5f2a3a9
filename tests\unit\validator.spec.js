/* eslint-disable no-undef */
import { isBiggerThanX, isSmallerThanX, numberRangeRuleGen } from '@/utils/validator';

describe('isBiggerThanX', () => {
  it('should return no error when value is greater than x', () => {
    const rule = {};
    const value = 10;
    const callback = jest.fn();

    isBiggerThanX(5).validator(rule, value, callback);

    expect(callback).toBeCalledTimes(1);
    expect(callback).toBeCalledWith();
  });

  it('should return no error when value is equal to x and equal flag is set', () => {
    const rule = {};
    const value = 5;
    const callback = jest.fn();

    isBiggerThanX(5, true).validator(rule, value, callback);

    expect(callback).toBeCalledTimes(1);
    expect(callback).toBeCalledWith();
  });

  it('should return error when value is less than x and equal flag is set', () => {
    const rule = {};
    const value = 3;
    const callback = jest.fn();

    isBiggerThanX(5, true).validator(rule, value, callback);

    expect(callback).toBeCalledTimes(1);
    expect(callback).toBeCalledWith(new Error('请输入大于等于5的数字'));
  });

  it('should return error when value is less than or equal to x and equal flag is not set', () => {
    const rule = {};
    const value = 5;
    const callback = jest.fn();

    isBiggerThanX(5, false).validator(rule, value, callback);

    expect(callback).toBeCalledTimes(1);
    expect(callback).toBeCalledWith(new Error('请输入大于5的数字'));
  });
});

describe('isSmallerThanX', () => {
  it('should return callback when value is empty or null or undefined', () => {
    const rule = {}
    let value = ''
    const callback = jest.fn()
    isSmallerThanX(10).validator(rule, value, callback)
    expect(callback).toHaveBeenCalled()

    value = null
    isSmallerThanX(10).validator(rule, value, callback)
    expect(callback).toHaveBeenCalled()

    value = undefined
    isSmallerThanX(10).validator(rule, value, callback)
    expect(callback).toHaveBeenCalled()
  })

  it('should return callback when value is smaller than x', () => {
    const rule = {}
    let value = 5
    const callback = jest.fn()
    isSmallerThanX(10).validator(rule, value, callback)
    expect(callback).toHaveBeenCalled()
  })

  it('should return callback when value is equal to x when equal is true', () => {
    const rule = {}
    let value = 10
    const callback = jest.fn()
    isSmallerThanX(10, true).validator(rule, value, callback)
    expect(callback).toHaveBeenCalled()
  })

  it('should return callback with error message when value is greater than x when equal is true', () => {
    const rule = {}
    let value = 15
    const callback = jest.fn()
    isSmallerThanX(10, true).validator(rule, value, callback)
    expect(callback).toHaveBeenCalledWith(new Error('请输入小于等于10的数字'))
  })

  it('should return callback with error message when value is greater than x when equal is false', () => {
    const rule = {}
    let value = 15
    const callback = jest.fn()
    isSmallerThanX(10).validator(rule, value, callback)
    expect(callback).toHaveBeenCalledWith(new Error('请输入小于10的数字'))
  })
})

test('should pass validation if value is null', () => {
  const rule = numberRangeRuleGen(() => true, () => true, 'error message')
  const callback = jest.fn()
  rule.validator({}, null, callback)
  expect(callback).toBeCalledTimes(1)
  expect(callback).toBeCalledWith()
})

test('should pass validation if value is undefined', () => {
  const rule = numberRangeRuleGen(() => true, () => true, 'error message')
  const callback = jest.fn()
  rule.validator({}, undefined, callback)
  expect(callback).toBeCalledTimes(1)
  expect(callback).toBeCalledWith()
})

test('should pass validation if value is empty string', () => {
  const rule = numberRangeRuleGen(() => true, () => true, 'error message')
  const callback = jest.fn()
  rule.validator({}, '', callback)
  expect(callback).toBeCalledTimes(1)
  expect(callback).toBeCalledWith()
})

test('should pass validation if value is a number within the range', () => {
  const rule = numberRangeRuleGen(
    t => t >= 0,
    t => t <= 10,
    'error message'
  )
  const callback = jest.fn()
  rule.validator({}, 5, callback)
  expect(callback).toBeCalledTimes(1)
  expect(callback).toBeCalledWith()
})

test('should fail validation if value is not a number', () => {
  const rule = numberRangeRuleGen(() => true, () => true, 'error message')
  const callback = jest.fn()
  rule.validator({}, 'abc', callback)
  expect(callback).toBeCalledTimes(1)
  expect(callback).toBeCalledWith(new Error('请输入数字'))
})

test('should fail validation if value is outside the range', () => {
  const rule = numberRangeRuleGen(
    t => t >= 0,
    t => t <= 10,
    'error message'
  )
  const callback = jest.fn()
  rule.validator({}, 15, callback)
  expect(callback).toBeCalledTimes(1)
  expect(callback).toBeCalledWith(new Error('error message'))
})
