import EmptyLayout from "@/package/src/components/EmptyLayout";
export default [
  {
    path: '/',
    redirect: { name: 'coverAdjust' }
  },
  {
    path:'/lakeConfig',
    component: EmptyLayout,
    name: "lakeConfig",
    meta: {
      title: "Lake列表",
    },
    children: [
      {
        path: 'lakeList',
        name: 'lakeList',
        component: resolve => require(['@/views/lake-config/lake-list/index.vue'], resolve),
        meta: {
          title: 'Lake列表',
          home: true,
          keepAlive: true
        },
        hidden: false
      },
    ]
  }
]
