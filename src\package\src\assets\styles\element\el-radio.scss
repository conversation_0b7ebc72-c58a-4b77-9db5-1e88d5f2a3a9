@import "../var.scss";
@import "../mixins.scss";

.el-radio {
  @include font-text;

  .el-radio__inner {
    background: transparent !important;

    &::after {
      background: $color-primary;
      width: 8px;
      height: 8px;
    }
  }

  &.is-disabled.is-checked .el-radio__inner {
    background: $border-color !important;

    &::after {
      background: $font-color-white;
      width: 4px;
      height: 4px;
    }
  }

  &:hover {
    .el-radio__label {
      color: $color-primary;
    }
    .el-radio__inner {
      border-color: $color-primary;
    }
  }
}
