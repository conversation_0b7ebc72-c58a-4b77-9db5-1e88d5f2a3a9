<template>
  <el-card>
    <ct-table
      :exportPartInfo="exportPartInfo"
      :exportAllName="exportAllName"
      :tableData="secondTableData"
    ></ct-table>
  </el-card>
</template>
<script>

import listMixin from "@/utils/list-pager";
import hostGroupMixin from "@/views/redundancy-assessment/mixins/hostGroup.mixin";
import ctTable from '@/views/redundancy-assessment/host-group-redundancy/components/ctTable.vue'

export default {
  name: "secondParentConfig",
  mixins: [
    listMixin,
    hostGroupMixin
  ],
  components: {
    ctTable
  },
  props: [],
  data() {
    let excelId = 'parentSecond'
    let excelName = '二层父'
    return {
      form: {},
      secondTableData: [], // 表格数据
      exportPartInfo: {
        excelId: excelId,
        excelName: excelName
      },
      exportAllName: '二层父',
    };
  },
  computed: {},
  filters: {},
  created() {
    this.$eventBus.$on("secondTableData", (data) => {
			this.secondTableData = data
		})
  },
  beforeDestroy() {
    this.$eventBus.$off('secondTableData');
  },
  watch: {},
  methods: {},
};
</script>
<style scoped lang="scss">
</style>
