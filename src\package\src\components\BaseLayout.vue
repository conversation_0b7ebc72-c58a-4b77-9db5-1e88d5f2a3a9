<template>
  <div class="base">
    <!-- TODO: 暂时处理一下登录界面 -->
    <nav-bar class="base--left" v-if="this.$route.path !== '/login'"></nav-bar>
    <div class="base--right">
      <header-bar></header-bar>
      <keep-alive :include="cachedPages" :max="8">
        <router-view v-if="$route.meta.keepAlive"></router-view>
      </keep-alive>
      <router-view v-if="!$route.meta.keepAlive"></router-view>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BaseLayout',
  computed: {
    cachedPages () {
      return this.$store.getters.cachedPages.map(v => v.name)
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../assets/styles/var.scss';
.base {
  display: flex;
  position: fixed;
  width: 100%;
  /*width: calc(100% - 50px); // 50px 是IAM的宽度*/
  height: 100%;

  &--right {
    width: calc(100% - 28px - 24px);
    overflow: auto;
    background: $bg-color;
    padding: 0 12px 0 14px;
    /deep/ .empty-layout.fix-header {
      height: calc(100% - 60px - 104px - 12px);
      overflow: auto;
    }
  }
}
</style>
