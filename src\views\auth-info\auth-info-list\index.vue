<template>
  <el-card>
    <div class="flex-container">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="IP">
          <el-input
            v-model="searchForm.ip"
            placeholder="请输入IP"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="供应商">
          <el-select
            v-model="searchForm.supplier_id"
            placeholder="请选择供应商"
            clearable
          >
            <el-option
              v-for="supplier in supplierList"
              :key="supplier.id"
              :label="`${supplier.supplier_name} (${supplier.supplier_code})`"
              :value="supplier.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="账号">
          <el-input
            v-model="searchForm.account"
            placeholder="请输入账号"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
      <div style="display: flex; align-items: center; flex-wrap: wrap">
        <div>
          <el-button plain @click="handleAdd">新增认证信息</el-button>
          <el-button plain @click="handleAddCustom"
            >新增自定义认证信息</el-button
          >
          <el-button plain @click="handleExportDetail">导出详情</el-button>
          <el-button plain @click="handleExportAuth">导出认证信息</el-button>
          <auth-status />
        </div>
      </div>
    </div>

    <el-table
      :data="tableData"
      v-loading="loading"
      border
      class="table-style"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
        width="55"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="ip"
        label="IP"
        align="center"
        width="140"
      ></el-table-column>
      <el-table-column label="运营商" align="center" width="100" prop="isp">
        <!-- <template slot-scope="scope">
          <span>{{ getIspCode(scope.row) }}</span>
        </template> -->
      </el-table-column>
      <el-table-column
        prop="supplier"
        label="供应商"
        align="center"
        width="150"
      >
        <template slot-scope="scope">
          <span
            >{{ scope.row.supplier_name }} ({{ scope.row.supplier_code }})</span
          >
        </template>
      </el-table-column>
      <el-table-column
        prop="account"
        label="账号"
        align="center"
        width="120"
      ></el-table-column>
      <el-table-column prop="pwd" label="密码" align="center" min-width="220">
        <template slot-scope="scope">
          <span v-if="scope.row.password_visible">{{
            handleDescrypt(scope.row.pwd)
          }}</span>
          <span v-else>******</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="auth_key"
        label="认证信息"
        align="center"
        min-width="220"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.auth_info_visible">{{
            handleDescrypt(scope.row.auth_key)
          }}</span>
          <span v-else>******</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="system_uuid"
        label="system_uuid"
        align="center"
        width="120"
      ></el-table-column>
      <el-table-column
        prop="operator"
        label="操作人"
        align="center"
        width="100"
      ></el-table-column>
      <el-table-column
        prop="create_time_formatted"
        label="创建时间"
        align="center"
        width="160"
      ></el-table-column>
      <el-table-column label="操作" align="center" width="200">
        <template slot-scope="scope">
          <el-button
            v-if="!scope.row.password_visible"
            type="text"
            @click="handleDecrypt(scope.row)"
            >解密</el-button
          >
          <el-button v-else type="text" @click="handleEncrypt(scope.row)"
            >隐藏</el-button
          >
          <el-button type="text" @click="handleDetail(scope.row)"
            >详情</el-button
          >
          <el-button type="text" @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="pagination.page"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pagination.page_size"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pagination.total"
      class="pagination"
    ></el-pagination>

    <auth-info-dialog
      v-if="dialogVisible"
      :is-edit="isEdit"
      :rowData="rowData"
      @close="dialogVisible = false"
      @refresh="getList"
    ></auth-info-dialog>

    <!-- 自定义认证信息弹窗 -->
    <auth-custom-dialog
      v-if="customDialogVisible"
      @close="customDialogVisible = false"
      @refresh="getList"
    ></auth-custom-dialog>

    <!-- 详情弹窗 -->
    <auth-detail-dialog
      v-if="detailDialogVisible"
      :row-data="currentRow"
      @close="detailDialogVisible = false"
      @auth-success="handleDetailAuthSuccess"
    >
    </auth-detail-dialog>
    <!-- 认证弹窗 -->
    <auth-verify-dialog
      :visible="authDialogVisible"
      :auth-type="authType"
      @close="handleAuthDialogClose"
      @verify-success="handleAuthVerifySuccess"
    />
  </el-card>
</template>

<script>
import { removeEmptyProps } from "@/utils/util.js";
import http from "../http.js";
import authInfoDialog from "./dialog/authInfoDialog.vue";
import authDetailDialog from "./dialog/authDetailDialog.vue";
import authCustomDialog from "./dialog/authCustomDialog.vue";
import { decryptPassword } from "@/utils/crypto.js";
import authMixin from "@/mixins/authMixin.js";

export default {
  name: "auth-info-list",
  mixins: [authMixin],
  components: {
    authInfoDialog,
    authDetailDialog,
    authCustomDialog,
  },
  data() {
    return {
      loading: false,
      tableData: [],
      supplierList: [],
      dialogVisible: false,
      customDialogVisible: false,
      isEdit: false,
      rowData: {},

      detailDialogVisible: false,
      currentRow: {},
      searchForm: {
        ip: "",
        supplier_id: "",
        account: "",
      },
      pagination: {
        total: 0,
        page: 1,
        page_size: 20,
      },
      selectedRows: [], // 选中的行数据
    };
  },
  computed: {
    ispList() {
      return this.$store.state.baseData.ispList;
    },
  },
  created() {
    this.getList();
    this.getSupplierList();
  },

  methods: {
    // ==================== 认证状态管理 ====================

    /**
     * 执行解密操作
     * @param {Object} row 行数据
     */
    async performDecrypt(row) {
      // 这里可以调用解密接口验证密码，然后显示解密内容
      // 目前直接在前端标记为已解密状态
      const index = this.tableData.findIndex((item) => item.id === row.id);
      if (index !== -1) {
        this.$set(this.tableData[index], "password_visible", true);
        this.$set(this.tableData[index], "auth_info_visible", true);
        // this.$message.success('解密成功，密码和认证信息已显示')
      }
    },

    /**
     * 执行导出操作
     * @param {Object} exportData 导出数据
     */
    async performExport(exportData) {
      // 使用统一认证系统的加密密码
      const encryptedPassword = this.$store.state.auth.encryptedPassword;
      await this.doExport(encryptedPassword, exportData);
    },

    handleDescrypt(data) {
      return decryptPassword(data);
    },

    async handleEncrypt(row) {
      const index = this.tableData.findIndex((item) => item.id === row.id);
      if (index !== -1) {
        this.$set(this.tableData[index], "password_visible", false);
        this.$set(this.tableData[index], "auth_info_visible", false);
      }
    },

    // ==================== 业务逻辑方法 ====================

    // 获取供应商列表
    async getSupplierList() {
      try {
        const res = await http.getSupplierList({
          page_size: 1000, // 获取所有供应商
        });
        if (res && res.code === 100000) {
          this.supplierList = res.data?.items || [];
        }
      } catch (error) {
        console.error("获取供应商列表失败:", error);
      }
    },
    async getList() {
      this.loading = true;
      try {
        const params = removeEmptyProps({
          page: this.pagination.page,
          page_size: this.pagination.page_size,
          ip: this.searchForm.ip,
          supplier_id: this.searchForm.supplier_id,
          account: this.searchForm.account,
        });

        const res = await http.getAuthInfoList(params);
        if (res && res.code === 100000) {
          // 处理数据，添加显示状态
          this.tableData = (res.data?.items || []).map((item) => ({
            ...item,
            password_visible: false, // 密码是否可见
            auth_info_visible: false, // 认证信息是否可见
            create_time_formatted: this.formatTime(item.create_time),
          }));
          this.pagination.total = res.data?.total || 0;
        }
      } catch (error) {
        console.error("获取认证信息列表失败:", error);
        this.$message.error("获取数据失败");
      } finally {
        this.loading = false;
      }
    },
    handleAdd() {
      this.isEdit = false;
      this.rowData = {};
      this.dialogVisible = true;
    },
    handleAddCustom() {
      this.customDialogVisible = true;
    },
    // 解密显示密码和认证信息
    handleDecrypt(row) {
      this.executeAuthenticatedOperation(
        "decrypt",
        () => this.performDecrypt(row),
        row
      );
    },
    // 查看详情
    handleDetail(row) {
      this.currentRow = row;
      this.detailDialogVisible = true;
    },
    // 表格选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },
    async handleDelete(row) {
      try {
        await this.$confirm("确定删除该认证信息吗？", "提示", {
          type: "warning",
        });
        const res = await http.deleteAuthInfo(row.id);
        if (res && res.code === 100000) {
          this.$message.success("删除成功");
          this.getList();
        }
      } catch (error) {
        console.error(error);
      }
    },
    // 权限认证成功处理

    // 详情弹窗认证成功处理
    handleDetailAuthSuccess(encryptedPassword) {
      // 统一认证系统会自动管理认证状态
    },
    // 导出功能
    async doExport(encryptedPassword, exportData) {
      try {
        const res = await http.exportAuthInfo({
          ids: exportData.ids,
          type: exportData.type,
          pwd: encryptedPassword,
        });

        if (res && res.data) {
          // 处理文件下载
          const blob = new Blob([res.data], {
            type:
              exportData.type === "csv"
                ? "text/csv;charset=utf-8"
                : "text/plain;charset=utf-8",
          });
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement("a");
          link.href = url;

          // 根据导出类型设置文件名
          let fileName = "auth_export";
          if (exportData.exportType === "detail") {
            fileName = "auth_detail_export";
          } else if (exportData.exportType === "auth") {
            fileName = "auth_info_export";
          }

          link.download = `${fileName}.${
            exportData.type === "csv" ? "csv" : "txt"
          }`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);

          const exportTypeName =
            exportData.exportType === "detail" ? "详情" : "认证信息";
          this.$message.success(`${exportTypeName}导出成功`);
        }
      } catch (error) {
        console.error("导出失败:", error);
        this.$message.error("导出失败，请检查网络连接或联系管理员");
      }
    },
    // 导出详情（CSV格式）
    handleExportDetail() {
      if (this.selectedRows.length === 0) {
        this.$message.warning("请先选择要导出的数据");
        return;
      }

      const exportData = {
        ids: this.selectedRows.map((row) => row.id),
        type: "csv",
        exportType: "detail",
      };
      this.executeAuthenticatedOperation(
        "export",
        () => this.performExport(exportData),
        exportData
      );
    },
    // 导出认证信息（TXT格式）
    handleExportAuth() {
      if (this.selectedRows.length === 0) {
        this.$message.warning("请先选择要导出的数据");
        return;
      }

      const exportData = {
        ids: this.selectedRows.map((row) => row.id),
        type: "text",
        exportType: "auth",
      };
      this.executeAuthenticatedOperation(
        "export",
        () => this.performExport(exportData),
        exportData
      );
    },
    // 格式化时间
    formatTime(timestamp) {
      if (!timestamp) return "-";
      const date = new Date(timestamp * 1000);
      return date.toLocaleString("zh-CN");
    },
    handleSizeChange(val) {
      this.pagination.page_size = val;
      this.pagination.page = 1;
      this.getList();
    },
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.getList();
    },
    handleSearch() {
      this.pagination.page = 1;
      this.getList();
    },
    handleReset() {
      this.searchForm = {
        ip: "",
        supplier_id: "",
        account: "",
      };
      this.pagination.page = 1;
      this.getList();
    },
    // 获取运营商代码
    // getIspCode(row) {
    //   let ispCode = '';

    //   // 如果数据中直接包含运营商代码，直接使用
    //   if (row.isp_code) {
    //     ispCode = row.isp_code;
    //   } else if (row.ip && row.supplier_id) {
    //     // 如果没有直接的运营商代码，尝试根据IP和供应商信息匹配
    //     const supplier = this.supplierList.find(s => s.id === row.supplier_id);
    //     if (supplier && supplier.ips_list && Array.isArray(supplier.ips_list)) {
    //       // 检查IP是否在供应商的IP段中
    //       for (const ipInfo of supplier.ips_list) {
    //         if (this.isIpInRange(row.ip, ipInfo.ips)) {
    //           ispCode = ipInfo.isp_code;
    //           break;
    //         }
    //       }
    //     }
    //   }

    //   // 根据运营商代码获取中文名称
    //   if (ispCode) {
    //     const isp = this.ispList.find(item => item.code === ispCode);
    //     return isp ? isp.cn_name : ispCode;
    //   }

    //   return '-';
    // },

    // 检查IP是否在指定的IP段中
    isIpInRange(ip, ipRange) {
      if (!ip || !ipRange) return false;

      // 简单的IP匹配，支持CIDR格式
      try {
        if (ipRange.includes("/")) {
          // CIDR格式
          const [network, prefixLength] = ipRange.split("/");
          const networkParts = network.split(".").map(Number);
          const ipParts = ip.split(".").map(Number);

          // 简单的网络匹配（这里可以使用更精确的算法）
          const prefix = parseInt(prefixLength);
          const bytesToCheck = Math.floor(prefix / 8);
          const bitsToCheck = prefix % 8;

          for (let i = 0; i < bytesToCheck; i++) {
            if (networkParts[i] !== ipParts[i]) {
              return false;
            }
          }

          if (bitsToCheck > 0 && bytesToCheck < 4) {
            const mask = (0xff << (8 - bitsToCheck)) & 0xff;
            if (
              (networkParts[bytesToCheck] & mask) !==
              (ipParts[bytesToCheck] & mask)
            ) {
              return false;
            }
          }

          return true;
        } else {
          // 精确匹配
          return ip === ipRange;
        }
      } catch (error) {
        console.error("IP匹配错误:", error);
        return false;
      }
    },
  },
};
</script>

<style scoped>
.flex-container {
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
}

.table-style {
  margin-bottom: 20px;
}

.pagination {
  text-align: center;
}
</style>
