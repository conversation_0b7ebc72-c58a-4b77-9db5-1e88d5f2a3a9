<template>
  <div>
    <div slot="header" class="container-head">
      <div class="btns">
        <el-button type="primary" icon="el-icon-download" @click="exportPart">部分导出</el-button>
        <el-button type="primary" icon="el-icon-download" @click="exportAll">全量导出</el-button>
        <export-excel-common
          ref='myChild'
          :exportExcelInfo='exportExcelInfo'
          :tableData='selectDataList'
          :exportExcelArry='exportExcelArry'
        ></export-excel-common>
      </div>
    </div>
    <el-table :data="showList" @selection-change="handleSelectionChange" :row-key="row => row.host_group" ref="hostGroupTable" highlight-current-row>
      <el-table-column type="selection" width="55" :reserve-selection="true"></el-table-column>
      <el-table-column label="大区" prop="area_view" width="80" align="center" sortable :sort-method="(a, b) => a.area_view.localeCompare(b.area_view)" fixed="left"></el-table-column>
      <el-table-column label="省份" prop="province" width="80" align="center" sortable :sort-method="(a, b) => a.province.localeCompare(b.province)" fixed="left"></el-table-column>
      <el-table-column label="Lake名称" prop="lake_name" width="105" align="center" sortable :sort-method="(a, b) => a.lake_name.localeCompare(b.lake_name)" fixed="left"></el-table-column>
      <el-table-column label="组名称" prop="host_group" width="180" align="center" sortable :sort-method="(a, b) => a.host_group.localeCompare(b.host_group)" fixed="left"></el-table-column>
      <el-table-column label="资源分组" prop="resource_groups" width="180" align="center" sortable :sort-method="(a, b) => a.resource_groups.localeCompare(b.resource_groups)" fixed="left"></el-table-column>
      <el-table-column label="组层级" prop="hg_level" width="80" align="center" sortable :sort-method="(a, b) => a.hg_level.localeCompare(b.hg_level)" fixed="left">
        <template slot-scope="scope">
          <el-tag slot="reference" v-if="scope.row.hg_level === 1">边缘</el-tag>
          <el-tag slot="reference" v-if="scope.row.hg_level === 2" type="success">一层父</el-tag>
          <el-tag slot="reference" v-if="scope.row.hg_level === 3" type="warning">二层父</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="运营商" prop="isp" width="80" align="center" sortable></el-table-column>
      <el-table-column label="Lake出口带宽" prop="lake_upper_limit_bw" width="120" align="center" sortable></el-table-column>
      <el-table-column label="rs数" prop="rip_count" width="80" align="center" sortable></el-table-column>
      <el-table-column label="正常rs数" prop="healthy_rip_count" width="100" align="center" sortable></el-table-column>
      <el-table-column label="内存" prop="memory" width="80" align="center" sortable></el-table-column>
      <el-table-column label="cpu核数" prop="cpu" width="95" align="center" sortable></el-table-column>
      <el-table-column label="磁盘" prop="disk" width="80" align="center" sortable></el-table-column>
      <el-table-column label="额定单机" prop="rip_rated_bw" width="100" align="center" sortable></el-table-column>
      <el-table-column label="健康值" prop="qfc" width="80" align="center" sortable></el-table-column>
      <el-table-column label="组额定能力" prop="hg_rated_bw" width="110" align="center" sortable></el-table-column>
      <el-table-column label="组动态额定" prop="hg_dyn_rated_bw" width="110" align="center" sortable></el-table-column>
      <el-table-column label="组额定qps" prop="hg_rated_qps" width="110" align="center" sortable></el-table-column>
      <el-table-column label="组业务带宽" prop="hg_cur_bw" width="110" align="center" sortable></el-table-column>
      <el-table-column label="组折算带宽" prop="hg_dis_cur_bw" width="110" align="center" sortable></el-table-column>
      <el-table-column label="峰值qps" prop="hg_peak_qps" width="110" align="center" sortable></el-table-column>
      <el-table-column label="组能力冗余" prop="hg_redu_bw" width="110" align="center" sortable></el-table-column>
      <el-table-column label="组动态能力冗余" prop="hg_dyn_redu_bw" width="130" align="center" sortable></el-table-column>
      <el-table-column label="组冗余qps" prop="hg_redu_qps" width="110" align="center" sortable></el-table-column>
      <el-table-column label="Lake带宽冗余" prop="lake_redu_bw" width="120" align="center" sortable></el-table-column>
      <el-table-column label="是否存在无效字段" prop="has_invalid_field" width="145" align="center" sortable>
        <template slot-scope="scope">
          <el-tag slot="reference" v-if="scope.row.has_invalid_field === false" type="success">否</el-tag>
          <el-tag slot="reference" v-if="scope.row.has_invalid_field === true" type="danger">是</el-tag>
        </template>
      </el-table-column>
      
    </el-table>

    <el-pagination
      layout="total, sizes, prev, pager, next, jumper"
      :total="filterList.length"
      :current-page.sync="page"
      :page-sizes="[10, 50, 100, 500]"
      :page-size="pageSize"
      @size-change="pageSizeChange"
      class="base-pagination"
    ></el-pagination>

  </div>
</template>
<script>

import listMixin from "@/utils/list-pager";
import hostGroupMixin from "@/views/redundancy-assessment/mixins/hostGroup.mixin";
import ExportExcelCommon from '@/helper/exportExcelCommon'

export default {
  name: "edgeConfig",
  filters: {},
  mixins: [
    listMixin,
    hostGroupMixin
  ],
  components: {
    ExportExcelCommon
  },
  props: {
    tableData: {
      type: Array,
      required: true,
      default: () => [],
    },
    exportPartInfo: {
      type: Object,
      default: () => {
        return {}
      },
    },
    exportAllName: {
      type: String,
      default: "",
    }
  },
  data() {
    return {
      exportExcelInfo: {
        excelId: "",
        excelName: ""
      },
      form: {},
      page: 1,
      pageSize: 10,
      selectDataList: [],
      hg_level_map: {
        1: "边缘",
        2: "一层父",
        3: "二层父"
      },
      has_invalid_field_map: {
        true: "是",
        false: "否"
      },
      exportExcelArry: [
        { prop: 'area_view', label: '大区', formatterFlag: false },
        { prop: 'province', label: '省份', formatterFlag: false },
        { prop: 'lake_name', label: 'lake名称', formatterFlag: false },
        { prop: 'host_group', label: '组名称', formatterFlag: false },
        { prop: 'resource_groups', label: '资源分组', formatterFlag: false },
        { prop: 'hg_level', label: '组层级', formatterFlag: false },
        { prop: 'isp', label: '运营商', formatterFlag: false },
        { prop: 'lake_upper_limit_bw', label: 'Lake出口带宽', formatterFlag: false },
        { prop: 'rip_count', label: 'rs数', formatterFlag: false },
        { prop: 'healthy_rip_count', label: '正常rs数', formatterFlag: false },
        { prop: 'memory', label: '内存', formatterFlag: false },
        { prop: 'cpu', label: 'cpu核数', formatterFlag: false },
        { prop: 'disk', label: '磁盘', formatterFlag: false },
        { prop: 'rip_rated_bw', label: '额定单机', formatterFlag: false },
        { prop: 'qfc', label: '健康值', formatterFlag: false },
        { prop: 'hg_rated_bw', label: '组额定能力', formatterFlag: false },
        { prop: 'hg_dyn_rated_bw', label: '组动态额定', formatterFlag: false },
        { prop: 'hg_rated_qps', label: '组额定qps', formatterFlag: false },
        { prop: 'hg_cur_bw', label: '组业务带宽', formatterFlag: false },
        { prop: 'hg_dis_cur_bw', label: '组折算带宽', formatterFlag: false },
        { prop: 'hg_peak_qps', label: '峰值qps', formatterFlag: false },
        { prop: 'hg_redu_bw', label: '组能力冗余', formatterFlag: false },
        { prop: 'hg_dyn_redu_bw', label: '组动态能力冗余', formatterFlag: false },
        { prop: 'hg_redu_qps', label: '组冗余qps', formatterFlag: false },
        { prop: 'lake_redu_bw', label: 'Lake带宽冗余', formatterFlag: false },
        { prop: 'has_invalid_field', label: '是否存在无效字段', formatterFlag: false },
      ],
    };
  },
  computed: {
    filterList() {
      let filterList = (this.tableData && this.tableData.slice()) || [];
      const area_view = this.form.area_view && this.form.area_view.trim();
      const province = this.form.province && this.form.province.trim();
      const lake_name = this.form.lake_name && this.form.lake_name.trim();
      const host_group = this.form.host_group && this.form.host_group.trim();
      const resource_groups = this.form.resource_groups && this.form.resource_groups.trim();
      const hg_level = this.form.hg_level;
      const isp = this.form.isp && this.form.isp.trim();
      const has_invalid_field = this.form.has_invalid_field;
      const lake_upper_limit_bw = this.form.lake_upper_limit_bw && this.form.lake_upper_limit_bw.trim();
      const rip_count = this.form.rip_count && this.form.rip_count.trim();
      const healthy_rip_count = this.form.healthy_rip_count && this.form.healthy_rip_count.trim();
      const memory = this.form.memory && this.form.memory.trim();
      const cpu = this.form.cpu && this.form.cpu.trim();
      const disk = this.form.disk && this.form.disk.trim();
      const rip_rated_bw = this.form.rip_rated_bw && this.form.rip_rated_bw.trim();
      const qfc = this.form.qfc && this.form.qfc.trim();
      const hg_rated_bw = this.form.hg_rated_bw && this.form.hg_rated_bw.trim();
      const hg_dyn_rated_bw = this.form.hg_dyn_rated_bw && this.form.hg_dyn_rated_bw.trim();
      const hg_rated_qps = this.form.hg_rated_qps && this.form.hg_rated_qps.trim();
      const hg_cur_bw = this.form.hg_cur_bw && this.form.hg_cur_bw.trim();
      const hg_dis_cur_bw = this.form.hg_dis_cur_bw && this.form.hg_dis_cur_bw.trim();
      const hg_peak_qps = this.form.hg_peak_qps && this.form.hg_peak_qps.trim();
      const hg_redu_bw = this.form.hg_redu_bw && this.form.hg_redu_bw.trim();
      const hg_dyn_redu_bw = this.form.hg_dyn_redu_bw && this.form.hg_dyn_redu_bw.trim();
      const hg_redu_qps = this.form.hg_redu_qps && this.form.hg_redu_qps.trim();
      const lake_redu_bw = this.form.lake_redu_bw && this.form.lake_redu_bw.trim();
      if (area_view) {
        filterList = filterList.filter(item => item.area_view?.includes(area_view));
      }
      if (province) {
        filterList = filterList.filter(item => item.province?.includes(province));
      }
      if (lake_name) {
        filterList = filterList.filter(item => item.lake_name?.includes(lake_name));
      }
      if (host_group) {
        filterList = filterList.filter(item => item.host_group?.includes(host_group));
      }
      if (resource_groups) {
        filterList = filterList.filter(item => item.resource_groups?.includes(resource_groups));
      }
      if (hg_level) {
        filterList = filterList.filter(item => item.hg_level === hg_level);
      }
      if (isp) {
        filterList = filterList.filter(item => item.isp?.includes(isp));
      }
      if (has_invalid_field !== "" && has_invalid_field !== null && has_invalid_field !== undefined) {
        filterList = filterList.filter(item => item.has_invalid_field === has_invalid_field);
      }
      function f1(val, fieldName) {
        if (val !== 0 && !val) return
        let symbol = val && val.substring(0, 1)
        let data = val && val.substring(1)
        if (symbol === '>') {
          filterList = filterList.filter(item => item[fieldName] > data);
        } else if (symbol === '<') {
          filterList = filterList.filter(item => item[fieldName] < data);
        } else if (symbol === '=') {
          filterList = filterList.filter(item => item[fieldName] = data);
        } else {
          filterList = filterList.filter(item => (item[fieldName] + '') === val);
        }
      }
      // Lake出口带宽
      if (lake_upper_limit_bw) {
        f1(lake_upper_limit_bw, 'lake_upper_limit_bw')
      }
      if (rip_count) {
        f1(rip_count, 'rip_count')
      }
      if (healthy_rip_count) {
        f1(healthy_rip_count, 'healthy_rip_count')
      }
      if (memory) {
        f1(memory, 'memory')
      }
      if (cpu) {
        f1(cpu, 'cpu')
      }
      if (disk) {
        f1(disk, 'disk')
      }
      if (rip_rated_bw) {
        f1(rip_rated_bw, 'rip_rated_bw')
      }
      if (qfc) {
        f1(qfc, 'qfc')
      }
      if (hg_rated_bw) {
        f1(hg_rated_bw, 'hg_rated_bw')
      }
      if (hg_dyn_rated_bw) {
        f1(hg_dyn_rated_bw, 'hg_dyn_rated_bw')
      }
      if (hg_rated_qps) {
        f1(hg_rated_qps, 'hg_rated_qps')
      }
      if (hg_cur_bw) {
        f1(hg_cur_bw, 'hg_cur_bw')
      }
      if (hg_dis_cur_bw) {
        f1(hg_dis_cur_bw, 'hg_dis_cur_bw')
      }
      if (hg_peak_qps) {
        f1(hg_peak_qps, 'hg_peak_qps')
      }
      if (hg_redu_bw) {
        f1(hg_redu_bw, 'hg_redu_bw')
      }
      if (hg_dyn_redu_bw) {
        f1(hg_dyn_redu_bw, 'hg_dyn_redu_bw')
      }
      if (hg_redu_qps) {
        f1(hg_redu_qps, 'hg_redu_qps')
      }
      if (lake_redu_bw) {
        f1(lake_redu_bw, 'lake_redu_bw')
      }

      filterList = filterList
        .map((item, index) => ({
            ...item,
            sortIdx: index, // 用于解决排序不稳定问题，在排序前预存当前顺序，而不是使用排序过程中会变的 index
        }))
        .sort((a, b) => {
          return a.area_view.localeCompare(b.area_view) ||
            a.province.localeCompare(b.province) ||
            a.lake_name.localeCompare(b.lake_name) ||
            a.host_group.localeCompare(b.host_group) ||
            a.resource_groups.localeCompare(b.resource_groups) ||
            a.hg_level.localeCompare(b.hg_level) ||
            a.isp.localeCompare(b.isp)
        })
        .map((item, index) => ({
            ...item,
            index: index + 1, // 增加序号
        }));

      return filterList;
    },
    showList() {
      // 过滤完成后，再分页
      const start = (this.page - 1) * this.pageSize;
      const end = this.page * this.pageSize;
      const showList = this.filterList.slice(start, end);
      return showList;
    }
  },
  created() {
    this.$eventBus.$on("searchFormData", (data) => {
			this.form = data
		})
  },
  beforeDestroy() {
    this.$eventBus.$off('searchFormData');
  },
  watch: {
    filterList: {
      deep: true,
      handler() {
        this.$refs.hostGroupTable && this.$refs.hostGroupTable.clearSelection()
      },
      immediate: true
    }
  },
  methods: {
    handleSelectionChange(val) {
      this.selectDataList = []
      val.forEach(v => {
        this.selectDataList.push({
          area_view: v.area_view,
          province: v.province,
          lake_name: v.lake_name,
          host_group: v.host_group,
          resource_groups: `${"\"" + v.resource_groups + "\"" }`,
          hg_level: this.hg_level_map[v.hg_level],
          isp: v.isp,
          lake_upper_limit_bw: v.lake_upper_limit_bw,
          rip_count: v.rip_count,
          healthy_rip_count: v.healthy_rip_count,
          memory: v.memory,
          cpu: v.cpu,
          disk: v.disk,
          rip_rated_bw: v.rip_rated_bw,
          qfc: v.qfc,
          hg_rated_bw: v.hg_rated_bw,
          hg_dyn_rated_bw: v.hg_dyn_rated_bw,
          hg_rated_qps: v.hg_rated_qps,
          hg_cur_bw: v.hg_cur_bw,
          hg_dis_cur_bw: v.hg_dis_cur_bw,
          hg_peak_qps: v.hg_peak_qps,
          hg_redu_bw: v.hg_redu_bw,
          hg_dyn_redu_bw: v.hg_dyn_redu_bw,
          hg_redu_qps: v.hg_redu_qps,
          lake_redu_bw: v.lake_redu_bw,
          has_invalid_field: this.has_invalid_field_map[v.has_invalid_field],
        })
      })
    },
    pageSizeChange(val) {
      this.page = 1,
      this.pageSize = val
    }
  },
};
</script>
<style scoped lang="scss">
.btns {
  float: right;
  margin-bottom: 20px !important;
}
.base-pagination {
  text-align: center;
  margin-top: 15px !important;
}
</style>
