<template>
  <el-card>
    <!-- 解析组灰度配置 -->
    <!-- 查询条件 -->
    <el-form :model="searchForm" ref="searchForm" label-position="left" inline>
      <el-row>
        <el-form-item label="解析组" prop="parse_group_id">
          <el-select v-model="searchForm.parse_group_id" placeholder="请选择解析组名称" filterable clearable style="width:300px">
            <el-option :value="-1" label="ALL" />
            <el-option v-for="(item, index) in parseGroupList" :key="index" :label="item.parse_group_name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button size="medium" type="primary" @click="onSearch">查询</el-button>
        </el-form-item>

        <span style="float:right">
          <el-button size="medium" type="primary" @click="openFormDialog(null)">新增</el-button>
        </span>
      </el-row>
    </el-form>
    <!-- 列表 -->
    <el-table :data="tableData" v-loading="queryLoading">
      <el-table-column prop="parse_group_id" label="解析组ID" align="center"></el-table-column>
      <el-table-column prop="parse_group_name" label="解析组名称" align="center"></el-table-column>
      <el-table-column prop="enable" label="是否启用" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.enable === 1">是</span>
          <span v-else>否</span>
        </template>
      </el-table-column>
      <el-table-column prop="operator" label="操作人" align="center"></el-table-column>
      <el-table-column prop="update_time" label="更新时间" align="center">
        <template slot-scope='scope'>
          <span>{{ (scope.row.update_time * 1000) | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="180">
        <template slot-scope="scope">
          <div>
            <el-button type="text" @click="openFormDialog(scope.row)">修改</el-button>
            <el-button type="text" @click="handleDelete(scope.row)">删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-row style="text-align: center; padding-top: 20px">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.page_size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      ></el-pagination>
    </el-row>

    <el-row>
      <form-dialog
        v-if="dialogVisible"
        :isEdit="isEdit"
        :rowData="rowData"
        @close="dialogVisible = false"
        @refresh="onSearch"
      ></form-dialog>
    </el-row>

  </el-card>
</template>
<script>

import http from "@/api/http.js"
import { mapState } from "vuex";
import formDialog from "@/views/parse-group-redundancy/grayscale-config/dialog/formDialog.vue"
import dateFormat from "@/utils/dateFormat"

export default {
  name: "lake-list",
  components: {
    formDialog
  },
  filters: {
    dateFormat: dateFormat
  },
  props: [],
  data() {
    return {
      searchForm: {
        parse_group_id: '',
      },
      queryLoading: false,
      tableData: [],
      dialogVisible: false,
      isEdit: false,
      rowData: {},
      pagination: {
        total: 0,
        page: 1,
        page_size: 20,
      },
    };
  },
  computed: {
    ...mapState({
      parseGroupList: (state) => state.baseData.parseGroupList,
    }),
  },
  mounted() {
    this.onSearch()
  },
  methods: {
    openFormDialog(row) {
      this.dialogVisible = true
      this.rowData = structuredClone(row)
      if (row) {
        this.isEdit = true;
      } else {
        this.isEdit = false;
      }
    },
    onSearch() {
      this.pagination.page = 1;
      this.query();
    },
    /**
     * 查询
     */
    async query() {
      let params = {
        ...this.searchForm,
        page: this.pagination.page,
        page_size: this.pagination.page_size,
      };
      this.queryLoading = true;
      try {
        await http.get(`/sda/parse_group/redundancy`, params).then((res) => {
          this.tableData = res && res.data && res.data.items;
          this.pagination.total = res && res.data && res.data.total;
          this.queryLoading = false;
        });
      } catch (error) {
        this.queryLoading = false;
      } finally {
        this.queryLoading = false;
      }
    },
    /**
     * 删除
     */
    async handleDelete(row) {
      await this.$confirm("确定删除该条数据吗？", "提示", {
        type: "warning",
      })
      let params = {
        operator: window.localStorage.getItem('userInfo'),
      }
      const res = await http.delete(`/sda/parse_group/redundancy/${row.id}`, params)
      if (res && res.code === 100000) {
        let msg = (res && res.message) || "删除成功"
        this.$message.success(msg);
        this.onSearch()
      }
    },
    handleSizeChange(val) {
      this.pagination.page = 1;
      this.pagination.page_size = val;
      this.query();
    },
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.query();
    },
  },
};
</script>
<style scoped lang="scss">
</style>
