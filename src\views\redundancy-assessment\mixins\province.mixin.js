import http from '@/api/http.js'
import timeFormat from '@/utils/timeFormat'
import { download } from "@/utils/downloadCsv";

export default {
  filters: {
    timeFormat: timeFormat
  },
  components: {
    download
  },
  data() {
    return {};
  },
  methods: {
    handleSearch() {
      this.paging.page = 1;
      this.search();
    },
    search() {
      if (this.queryForm.template_ids && this.queryForm.template_ids.length === 0) {
        this.$message.warning("资源池不能为空")
        return;
      }
      if (this.timeRange === null) {
        this.$message.warning("请先选择时间")
        return;
      }
      const currentTime = new Date().getTime(); // 返回当前时间的毫秒表示
      let enddate = this.timeRange ? Number(new Date(this.timeRange[1]).getTime()) : null
      if (enddate > currentTime) {
        this.$message.warning("结束时间不能超过当前时间")
        return;
      }
      if (this.queryForm.include_future_lake === '') {
        this.$message.warning("请选择考虑上下架节点")
        return;
      }
      if (this.queryForm.lake_weight === '') {
        this.$message.warning("请输入Lake带宽比例")
        return;
      }
      if (this.queryForm.lake_weight && typeof this.queryForm.lake_weight !== 'number') {
        this.$message.warning("Lake带宽比例只能输入数字")
        return;
      }
      if (this.queryForm.lake_weight < 0 || this.queryForm.lake_weight > 100) {
        this.$message.warning("Lake带宽比例取值范围：0 ~ 100")
        return;
      }
      if (this.queryForm.host_group_weight === '') {
        this.$message.warning("请输入组能力比例")
        return;
      }
      if (this.queryForm.host_group_weight && typeof this.queryForm.host_group_weight !== 'number') {
        this.$message.warning("组能力比例只能输入数字")
        return;
      }
      if (this.queryForm.host_group_weight < 0 || this.queryForm.host_group_weight > 100) {
        this.$message.warning("组能力比例取值范围：0 ~ 100")
        return;
      }
      if (this.queryForm.include_biz_table === '') {
        this.$message.warning("请选择是否考虑商机表")
        return;
      }
      this.loading = true
      this.$eventBus.$emit("loadingProvinceData", true);
      let vm = this
      let startTime = this.timeRange ? Number(new Date(this.timeRange[0]).getTime().toString().substr(0, 10)) : ""
      let endTime = this.timeRange ? Number(new Date(this.timeRange[1]).getTime().toString().substr(0, 10)) : ""
      let params = {
        ...this.queryForm,
        start_time: startTime,
        end_time: endTime,
        page: vm.paging.page,
        page_size: vm.paging.page_size
      }
      http.post(`/sda/redundancy/province`, params)
      .then(response => {
        if (response && response.code === 100000) {
          this.loading = false
          this.$eventBus.$emit("loadingProvinceData", false);
          let mobileTableData = response && response.data && response.data.China_Mobile
          let unicomTableData = response && response.data && response.data.China_Unicom
          let telecomTableData = response && response.data && response.data.China_Telecom
          this.$eventBus.$emit("mobileTableData", mobileTableData);
          this.$eventBus.$emit("unicomTableData", unicomTableData);
          this.$eventBus.$emit("telecomTableData", telecomTableData);
        }
      })
      .finally(() => {
        this.loading = false
        this.$eventBus.$emit("loadingProvinceData", false);
      })
    },
    exportPart() {
      let timestamp = new Date().getTime()
      let time = timeFormat(timestamp)
      let downloadTime = time.replace(/[-:]/g, '')
      this.exportExcelInfo.excelName = this.exportPartInfo.excelName+'-' + downloadTime + '.xlsx'
      this.exportExcelInfo.excelId = this.exportPartInfo.excelId
      // 需要延时调导出方法，为了等待数据初始化到列表中
      setTimeout(() => {
        this.$refs.myChild.exportExcel()
      }, 100)
    },
    exportAll() {
      let str = `大区,省份,额定带宽,额定能力,额定动态能力,带宽冗余,能力冗余,动态能力冗余,当前峰值,上架带宽,上架能力,下架带宽,下架能力,带宽需求,能力需求,最终带宽冗余,最终能力冗余,最终动态能力冗余,是否存在无效字段\n`
      this.filterList.forEach((item => {
        str += item.area_view + ",";
        str += item.province + ",";
        str += item.rated_bw + ",";
        str += item.rated_cap + ",";
        str += item.dyn_rated_cap + ",";
        str += item.redu_bw + ",";
        str += item.redu_cap + ",";
        str += item.redu_dyn_cap + ",";
        str += item.cur_peak_bw + ",";
        str += item.add_bw + ",";
        str += item.add_cap + ",";
        str += item.del_bw + ",";
        str += item.del_cap + ",";
        str += item.need_bw + ",";
        str += item.need_cap + ",";
        str += item.final_redu_bw + ",";
        str += item.final_redu_cap + ",";
        str += item.final_dyn_redu_cap + ",";
        str += this.has_invalid_field_map[item.has_invalid_field] + ",\n";
      }))
      let timestamp = new Date().getTime()
      let time = timeFormat(timestamp)
      let downloadTime = time.replace(/[-:]/g, '')
      let name = this.exportAllName + '-' + downloadTime
      download(name, str)
    },
  }
}
