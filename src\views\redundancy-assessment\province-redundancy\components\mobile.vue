<template>
  <el-card>
    <ct-table
      :exportPartInfo="exportPartInfo"
      :exportAllName="exportAllName"
      :tableData="mobileTableData"
    ></ct-table>

  </el-card>
</template>
<script>

import listMixin from "@/utils/list-pager";
import provinceMixin from "@/views/redundancy-assessment/mixins/province.mixin";
import ctTable from '@/views/redundancy-assessment/province-redundancy/components/ctTable.vue'

export default {
  name: "mobile",
  mixins: [
    listMixin,
    provinceMixin
  ],
  components: {
    ctTable
  },
  props: [],
  data() {
    let excelId = 'mobile'
    let excelName = '中国移动'
    return {
      form: {},
      mobileTableData: [], // 表格数据
      exportPartInfo: {
        excelId: excelId,
        excelName: excelName
      },
      exportAllName: '中国移动',
    };
  },
  computed: {},
  filters: {},
  created() {
    this.$eventBus.$on("mobileTableData", (data) => {
			this.mobileTableData = data
		})
  },
  beforeDestroy() {
    this.$eventBus.$off('mobileTableData');
  },
  watch: {},
  methods: {},
};
</script>
<style scoped lang="scss">
</style>
