<template>
  <el-card>
    <ct-table
      :exportPartInfo="exportPartInfo"
      :exportAllName="exportAllName"
      :tableData="firstTableData"
    ></ct-table>
  </el-card>
</template>
<script>

import listMixin from "@/utils/list-pager";
import hostGroupMixin from "@/views/redundancy-assessment/mixins/hostGroup.mixin";
import ctTable from '@/views/redundancy-assessment/host-group-redundancy/components/ctTable.vue'

export default {
  name: "firstParentConfig",
  mixins: [
    listMixin,
    hostGroupMixin
  ],
  components: {
    ctTable
  },
  props: [],
  data() {
    let excelId = 'parentFirst'
    let excelName = '一层父'
    return {
      form: {},
      firstTableData: [], // 表格数据
      exportPartInfo: {
        excelId: excelId,
        excelName: excelName
      },
      exportAllName: '一层父',
    };
  },
  computed: {},
  filters: {},
  created() {
    this.$eventBus.$on("firstTableData", (data) => {
			this.firstTableData = data
		})
  },
  beforeDestroy() {
    this.$eventBus.$off('firstTableData');
  },
  watch: {},
  methods: {},
};
</script>
<style scoped lang="scss">
</style>
