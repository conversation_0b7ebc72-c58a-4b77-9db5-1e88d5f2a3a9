apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    run: sda-ui
  name: sda-ui
  namespace: {{NAMESPACE}}
spec:
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      run: sda-ui
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        run: sda-ui
    spec:
      dnsConfig:
        options:
        - name: single-request-reopen
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: {{NODE_LABEL_KEY}}
                operator: In
                values:
                - "{{NODE_LABEL_VAL}}"
      containers:
      - image: {{imageUrl}}
        imagePullPolicy: IfNotPresent
        name: sda-ui
        volumeMounts:
        - mountPath: /usr/share/nginx/html/static/serverConfig.js
          name: sda-ui-config
          subPath: serverConfig.js
      volumes:
        - name: sda-ui-config
          configMap:
            name: sda-ui-config
            items:
              - key: serverConfig.js
                path: serverConfig.js
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
