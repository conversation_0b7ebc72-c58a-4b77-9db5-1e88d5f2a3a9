const GROUP_TYPE_MAIN = 1
const GROUP_TYPE_BAK = 2

export default {
  methods: {
    getCoverDetailParams(configData, areaConfigMap) {
      let params = {
        ...configData
      }
      params.parse_area_covers = []
      for (let key in areaConfigMap) {
        let item = areaConfigMap[key]
        item.groups && item.groups.forEach((group) => {
          let config = JSON.parse(JSON.stringify(item))
          delete config.bakGroups
          handleConfigItem(config, group)
          params.parse_area_covers.push(config)
        })
        item.bakGroups && item.bakGroups.forEach((group) => {
          let config = JSON.parse(JSON.stringify(item))
          delete config.bakGroups
          handleConfigItem(config, group, 'bak')
          params.parse_area_covers.push(config)
        })
      }
      return params

      function handleConfigItem(config, group, type) {
        delete config['activeNames']
        delete config['areaNode']
        config.host_group = JSON.parse(JSON.stringify(group))
        delete config['groups']
        delete config['bakActiveNames']
        if (type === 'bak') {
          config.type = GROUP_TYPE_BAK
        } else {
          config.type = GROUP_TYPE_MAIN
        }
      }
    }
  }
}
