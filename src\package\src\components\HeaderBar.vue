<template>
  <div class="header">
    <scrollpane class="header--tag" :tagNum="tagNum">
      <div class="header--tag--body">
        <router-link
          v-for="(item, index) in routeList"
          tag="div"
          :class="[
            'header--tag--body--item',
            { 'header--tag--body--item__active': active === index },
          ]"
          :key="index"
          :to="{ name: item.name, query: item.query }"
        >
          {{ item.title }}
          <span
            class="el-icon-close"
            @click.prevent.stop="closeSelectedTag(index)"
          ></span>
        </router-link>
      </div>
    </scrollpane>
    <div class="header--bread">
      <img src="../assets/images/bread.svg" />
      <div v-for="(item, index) in $route.matched" :key="index">
        <span :class="{ title__last: index === $route.matched.length - 1 }">{{
          item.meta.title
        }}</span>
        <span v-if="index !== $route.matched.length - 1">&gt;</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "HeaderBar",
  data() {
    return {
      active: 0,
      tagNum: 1,
      routeList: [],
    };
  },
  computed: {
    isHome() {
      // 是否是主页
      return this.$route.meta.home;
    },
  },
  watch: {
    $route: {
      deep: true,
      handler() {
        this.addRouteTag();
      },
    },
    routeList: {
      deep: true,
      handler() {
        this.$store.dispatch("setCachedPages", this.routeList);
        this.$nextTick(() => {
          this.tagNum = this.routeList.length;
        });
      },
    },
  },
  methods: {
    // 添加路由标签
    addRouteTag() {
      let name = this.$route.name;
      let title = this.$route.meta.title;
      let path = this.$route.path;
      let query = this.$route.query;
      let params = this.$route.params;
      let currentPath =
        this.routeList[this.active] && this.routeList[this.active].path;
      let pathList = this.routeList.map((v) => v.path);
      let indexOf = pathList.indexOf(path);
      // 当路由已存在routeList中
      if (indexOf > -1) {
        if (path !== currentPath) {
          // 路由不是当前页面
          this.active = indexOf;
          this.$router.push({ name, title, path, params, query });
        }
        return;
      }
      title && this.routeList.push({ name, title, path, params, query });
      indexOf === -1 && (this.active = this.routeList.length - 1); // active变为最后一个，即刚添加的
    },
    // 关闭标签
    closeSelectedTag(index) {
      let len = this.routeList.length;
      // 只存在一个标签且是主页，无需操作
      if (len === 1 && this.isHome) {
        return;
      }
      // 必须先删除，否则this.$router.push会影响index
      this.routeList.splice(index, 1);
      if (len === 1) {
        // 只存在一个标签但非主页，跳回主页
        this.$router.push("/");
      } else {
        // 存在多个标签
        this.active > index && this.active--;
        if (this.active === index) {
          // active < index时无需处理
          this.active === len - 1 &&
            this.$router.push({ ...this.routeList[--this.active] });
          this.active < len - 1 &&
            this.$router.push({ ...this.routeList[this.active] });
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../assets/styles/var.scss";
@import "../assets/styles/mixins.scss";
$header-height: 56px;
$header-item-height: 40px;
$header-border-color: #efefef;

.header {
  height: 104px;
  color: $font-color-primary2;
  font-size: $font-size-large;
  // padding: 0 14px;

  &--tag {
    height: $header-height;
    border-bottom: 1px solid $split-color;
    &--body {
      height: $header-height;
      display: flex;
      align-items: flex-end;

      &--item {
        height: $header-item-height;
        line-height: $header-item-height;
        background-color: $bg-color-tab;
        border: 1px solid $split-color;
        border-radius: $border-radius-primary;
        box-shadow: 2px -2px 4px 0 rgba(219, 219, 219, 0.62);
        margin: 0 6px 1px 0;
        padding: 0 20px;
        @include center;
        cursor: pointer;

        &__active {
          color: $font-color-important;
          background-color: $bg-color-module;
        }

        span {
          color: #929db1;
          padding: 1px;
          margin-left: 20px;

          &:hover {
            border-radius: 50%;
            color: $font-color-white;
            background-color: #929db1;
          }
        }
      }
    }
  }

  &--bread {
    height: 48px;
    padding-left: 12px;
    display: flex;
    align-items: center;
    font-size: $font-size-large;
    color: $font-color-primary;

    > img {
      width: 12px;
      height: 15px;
      margin-right: 10px;
    }

    > div {
      display: flex;
      align-items: center;

      > span {
        margin-right: 6px;
      }
      .title__last {
        color: $font-color-primary2;
      }
    }
  }
}
</style>
