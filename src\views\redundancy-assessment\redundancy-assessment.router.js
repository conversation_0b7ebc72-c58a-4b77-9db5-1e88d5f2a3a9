// import hostGroupRedundancyAssessment from "@/views/redundancy-assessment/host-group-redundancy/index.vue"
// import provinceRedundancyAssessment from "@/views/redundancy-assessment/province-redundancy/index.vue"
import EmptyLayout from "@/package/src/components/EmptyLayout";
export default [
  {
    path: '/',
    redirect: { name: 'hostGroupRedundancyAssessment' }
  },
  {
    path:'/redundancyAssessment',
    component: EmptyLayout,
    name: "redundancyAssessment",
    meta: {
      title: "资源冗余",
    },
    children: [
      {
        path: 'hostGroupRedundancyAssessment',
        name: 'hostGroupRedundancyAssessment',
        // component: hostGroupRedundancyAssessment,
        component: resolve => require(['@/views/redundancy-assessment/host-group-redundancy/index.vue'], resolve),
        meta: {
          title: '主机组冗余',
          home: true, // 首页
          keepAlive: true
        },
        hidden: false
      },
      {
        path: 'provinceRedundancyAssessment',
        name: 'provinceRedundancyAssessment',
        // component: provinceRedundancyAssessment,
        component: resolve => require(['@/views/redundancy-assessment/province-redundancy/index.vue'], resolve),
        meta: {
          title: '省份冗余',
          keepAlive: true
        },
        hidden: false
      },
    ]
  }
]