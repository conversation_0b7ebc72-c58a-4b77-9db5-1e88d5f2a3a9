import EmptyLayout from "@/package/src/components/EmptyLayout";
import Config from "@/views/parse-group-config/cover-adjust/config.vue"

export default [
  {
    path:'/parseGroupRedundancy',
    component: EmptyLayout,
    name: "parseGroupRedundancy",
    meta: {
      title: "解析组系数",
    },
    children: [
      {
        path: 'grayscaleConfig',
        name: 'grayscaleConfig',
        component: resolve => require(['@/views/parse-group-redundancy/grayscale-config/index.vue'], resolve),
        meta: {
          title: '解析组灰度配置',
          home: true,
          keepAlive: true
        },
        hidden: false
      },
      {
        path: 'changeRecord',
        name: 'changeRecord',
        component: resolve => require(['@/views/parse-group-redundancy/change-record/index.vue'], resolve),
        meta: {
          title: '解析组系数变更记录',
          home: true,
          keepAlive: true
        },
        hidden: false
      },
    ]
  }
]