<template>
  <el-card>
    <div slot="header" class="clearfix">
      <span>省份冗余</span>
      <div class="btns">
        <span class="text-style">
          <span v-if="updateTime === 0">最近没有上传文件</span>
          <span v-if="updateTime > 0">最新上传文件时间为：{{ updateTime * 1000 | dateFormat }}</span>
        </span>
        <el-button type="primary" icon="el-icon-plus" @click="addConfigFileUpload">上传商机表</el-button>
      </div>
    </div>
    <!-- 查询条件 -->
    <search-bar></search-bar>

    <el-tabs v-model="activeTab" type="card">
      <el-tab-pane v-for="item in getChildMenu" :key="item.prop" :label="item.label" :name="item.prop"></el-tab-pane>
    </el-tabs>

    <el-scrollbar class="domain-edit-content" v-loading="loading">
      <el-col :span="24" ref="orderform">
        <component
          v-for="item in getChildMenu"
          :key="item.prop"
          :is="item.prop"
          v-show="activeTab === item.prop"
          :ref="item.prop"
        />
      </el-col>
    </el-scrollbar>

    <configFileAdd
      v-if="isUploadFile"
      @close="isUploadFile = false"
      @refresh="onSearch"
      :isShow="addConfigFile"
      :isUploadFile="isUploadFile"
    ></configFileAdd>

  </el-card>
</template>
<script>

import listMixin from "@/utils/list-pager";
import copyIpMixin from "@/utils/mixins/copyIp.mixin";
import ExportExcelCommon from '@/helper/exportExcelCommon'
import { download } from "@/utils/downloadCsv";
import mobile from "@/views/redundancy-assessment/province-redundancy/components/mobile.vue";
import unicom from "@/views/redundancy-assessment/province-redundancy/components/unicom.vue";
import telecom from "@/views/redundancy-assessment/province-redundancy/components/telecom.vue";
import searchBar from "@/views/redundancy-assessment/province-redundancy/components/searchBar.vue";
import provinceMixin from "@/views/redundancy-assessment/mixins/province.mixin";
import configFileAdd from '@/views/redundancy-assessment/province-redundancy/components/configFileAdd.vue'
import http from '@/api/http.js'
import dateFormat from '@/utils/dateFormat'

export default {
  name: "province-redundancy",
  filters: {
    dateFormat: dateFormat
  },
  mixins: [listMixin, copyIpMixin, provinceMixin],
  components: {
    ExportExcelCommon,
    download,
    mobile,
    unicom,
    telecom,
    searchBar,
    configFileAdd
  },
  props: [],
  data() {
    return {
      activeTab: "telecom",
      updateTime: 0,
      addConfigFile: false,
      loading: false,
      isUploadFile: false,
      getChildMenu: [
        { label: "中国电信", prop: "telecom"},
        { label: "中国联通", prop: "unicom"},
        { label: "中国移动", prop: "mobile"}
      ],
    };
  },
  computed: {},
  created() {
    this.$eventBus.$on("loadingProvinceData", (data) => {
			this.loading = data
		})
  },
  mounted() {
    this.getUpdateTime();
  },
  watch: {},
  methods: {
    addConfigFileUpload(data, title) {
      this.isUploadFile = true
      this.addConfigFile = !this.addConfigFile
    },
    onSearch() {
      this.isUploadFile = false
      this.getUpdateTime();
    },
    getUpdateTime() {
      http.get(`/sda/redundancy/business`)
      .then(response => {
        if (response && response.update_time) {
          this.updateTime = response.update_time
        } else {
          this.updateTime = 0
        }
      })
    },
  },
};
</script>
<style scoped lang="scss">
.btns {
  float: right;
}
.text-style {
  font-size: 13px;
  font-weight: 500;
  margin-right: 12px;
}
</style>
