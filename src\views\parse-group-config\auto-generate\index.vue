<template>
  <el-card>
    <!-- 待办任务-自动生成 -->
    <!-- 查询条件 -->
    <el-form :model="searchForm" ref="searchForm" label-position="left" inline>
      <el-row>
        <el-form-item label="" prop="pg_id">
          <el-select v-model="searchForm.pg_id" placeholder="请选择解析组名称" filterable clearable style="width:300px">
            <el-option v-for="(item, index) in parseGroupList" :key="index" :label="item.pg_name" :value="item.pg_id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="">
          <el-input v-model="searchForm.id" clearable placeholder="请输入子任务id"></el-input>
        </el-form-item>

        <el-form-item>
          <el-button size="medium" type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
        <span style="float:right">
          <el-button size="medium" @click="openTaskDialog('preview')">任务预览</el-button>
          <el-button size="medium" type="primary" @click="handleDelete()">批量删除</el-button>
          <el-button size="medium" type="primary" @click="openTaskDialog('submit')">任务提交</el-button>
        </span>
      </el-row>
    </el-form>

    <el-table
      :data="tableData"
      ref="tableData"
      v-loading="querying"
      @selection-change="handleSelectionChange"
      @row-click="rowClick"
      border
    >
      <el-table-column type="selection" align="center" width="55"></el-table-column>
      <el-table-column prop="id" label="子任务ID" align="center" key="sub_task_id"></el-table-column>
      <el-table-column prop="parse_group_name" label="解析组名称" align="center" key="parse_group_name"></el-table-column>
      <el-table-column prop="editor_name" label="提交人" align="center" key="editor_name"></el-table-column>
      <el-table-column label="操作" align="center" width="180" key="operate">
        <template slot-scope="scope">
          <div>
            <span>
              <el-button type="text" @click="handlePreview(scope.row)">预览</el-button>
              <el-button type="text" @click="showConfig(scope.row)">配置</el-button>
              <el-button type="text" @click="handleDelete(scope.row)">删除</el-button>
            </span>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-row style="text-align: center; padding-top: 20px">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.page_size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      ></el-pagination>
    </el-row>

    <el-row>
      <!-- 任务预览/任务提交 -->
      <task-diff-dialog
        v-if="taskDiffDialogVisible"
        :is-edit="true"
        :rowData="rowData"
        :dialogType="dialogType"
        :multipleSelection="multipleSelection"
        @close="taskDiffDialogVisible = false"
        @refresh="onSearch"
      ></task-diff-dialog>
    </el-row>

  </el-card>
</template>
<script>

import http from "@/api/http.js"
import taskDiffDialog from "@/views/parse-group-config/task-list/dialog/taskDiffDialog.vue"
import dateFormat from "@/utils/dateFormat"

export default {
  name: "auto-generate",
  components: {
    taskDiffDialog,
  },
  filters: {
    dateFormat: dateFormat
  },
  props: [],
  data() {
    return {
      querying: false,
      tableData: [],
      rowData: {},
      dialogType: '',
      taskDiffDialogVisible: false,
      multipleSelection: [],
      parseGroupList: [],
      pagination: {
        total: 0,
        page: 1,
        page_size: 20,
      },
      searchForm: {
        task_name: '',
        id: '',
        pg_id: '',
      },
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {
    this.onSearch()
    this.getParseGroupList()
  },
  methods: {
    // 任务提交/任务预览
    openTaskDialog(type) {
      if (!(this.multipleSelection && this.multipleSelection.length > 0)) {
        this.$message.warning("请先选择数据！")
        return;
      }
      this.dialogType = type
      this.taskDiffDialogVisible = true
    },
    // 点击：预览
    handlePreview(row) {
      this.multipleSelection = [row]
      this.taskDiffDialogVisible = true
    },
    handleSelectionChange(val) {
      this.multipleSelection = JSON.parse(JSON.stringify(val));
    },
    rowClick(row) {
      this.$refs.tableData.toggleRowSelection(row)
    },
    showConfig(row) {
      this.$router.push({name: 'parse-config', query: { id: row.parse_group_id, subTaskId: row.id, from: "autoGenerate" }})
    },
    async handleDelete(row) {
      let delIds = []

      // 1. 判断是单项删除还是批量删除
      if (row && row.id) {
        delIds = [row.id]
      } else if (this.multipleSelection && this.multipleSelection.length > 0) {
        delIds = this.multipleSelection.map(item => item.id)
      } else {
        this.$message.warning("请先选择数据！")
        return
      }

      // 2. 弹出确认框
      await this.$confirm("确定删除数据吗？", "提示", {
        type: "warning",
      })

      // 3. 调用删除接口
      const params = { ids: delIds }
      const res = await http.post(`/sda/change_cover/detail/delete`, params)
      if (res && res.code === 100000) {
        let msg = (res && res.message) || "删除成功"
        this.$message.success(msg);
        this.onSearch()
      }
    },
    onSearch() {
      this.pagination.page = 1;
      this.query();
    },
    async query() {
      this.querying = true;
      let params = {
        auto: 1,
        id: this.searchForm.id,
        pg_id: this.searchForm.pg_id,
        task_id: 0,
        page: this.pagination.page,
        page_size: this.pagination.page_size,
      };
      try {
        let res = await http.get(`/sda/change_cover/detail/list`, params)
        this.tableData = res && res.data && res.data.items;
        this.pagination.total = res && res.data && res.data.total;
        this.querying = false;
      } catch (error) {
        this.querying = false;
      } finally {
        this.querying = false;
      }
    },
    handleSizeChange(val) {
      this.pagination.page = 1;
      this.pagination.page_size = val;
      this.query();
    },
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.query();
    },
    getParseGroupList() {
      http.get(`/sda/parse_group/get`).then(res => {
        this.parseGroupList = (res && res.data && res.data.items) || []
      })
    },
  },
};
</script>
<style scoped lang="scss">
</style>
