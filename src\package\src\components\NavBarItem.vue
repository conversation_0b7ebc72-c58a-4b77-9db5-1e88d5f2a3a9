<!--
 * @Description: 菜单栏子组件
 * @Author: l<PERSON><PERSON><PERSON>@chinatelecom.cn
 * @Date: 2020-06-06 11:10:00
 * @LastEditTime: 2020-06-06 11:10:00
 * @LastEditors: <EMAIL>
 * @Usage：<nav-bar-item :menu="menu" :index="index"><nav-bar-item>
 -->
<template>
  <div>
    <!-- 无子菜单 -->
    <el-menu-item
      v-if="showMenu(menu) && !hasChildren(menu)"
      :index="route"
      :class="[
        { subitem1: level === 1 },
        { subitem2: level === 2 },
        { subitem3: level === 3 },
      ]"
    >
      <template slot="title">
        <div v-show="isCollapse">
          <i v-if="level === 1" :class="`iconfont ${menu.meta.icon}`"></i>
          <span>{{ menu.meta.title }}</span>
        </div>
      </template>
      <i v-if="level === 1" :class="`iconfont ${menu.meta.icon}`"></i>
      <span>{{ !isCollapse || level !== 1 ? menu.meta.title : "" }}</span>
    </el-menu-item>

    <!-- 有子菜单 -->
    <el-submenu
      v-if="showMenu(menu) && hasChildren(menu)"
      :class="[
        { submenu1: level === 1 },
        { submenu2: level === 2 },
        { submenu3: level === 3 },
      ]"
      :index="route"
    >
      <template slot="title">
        <i v-if="level === 1" :class="`iconfont ${menu.meta.icon}`"></i>
        <span>{{ !isCollapse || level !== 1 ? menu.meta.title : "" }}</span>
      </template>
      <slot></slot>
    </el-submenu>
  </div>
</template>

<script>
export default {
  name: "NavBarItem",
  props: ["menu", "route", "level", "isCollapse"],
  methods: {
    showMenu(menu) {
      // hidden未定义默认true
      return (
        (menu.hidden === false || menu.hidden === undefined) &&
        menu.meta &&
        menu.meta.title
      );
    },
    hasChildren(menu) {
      return menu.children && menu.children.length > 0;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../assets/styles/var.scss";
@mixin menu($bg, $color, $hoverBg: "", $hoverColor: "", $activeColor: "") {
  :global(.el-menu) {
    background-color: $bg;
    :global(.el-menu-item) {
      background-color: $bg;
      color: $color;
      &:hover {
        background-color: $bg-color-nav__hover;
      }
    }
    :global(.el-menu-item.is-active) {
      color: $font-color-important;
    }
    :global(.el-submenu__title) {
      color: $color;
      &:hover {
        background-color: $bg-color-nav__hover;
      }
    }
    :global(.el-submenu__title.is-actice) {
      color: $font-color-important;
    }
  }
}

i:before {
  color: $color-primary;
  margin-right: 10px;
  font-size: 14px;
}

:global(.el-menu-item.subitem1) {
  // background-color: $bg-color-nav2;
  color: $font-color-white;
  // text-indent: 10px;
  &:hover {
    background-color: $bg-color-nav__hover;
  }
  &:hover {
    background-color: $bg-color-nav__hover;
  }
  &:focus {
    background-color: $bg-color-nav1;
  }
}

:global(.el-menu-item.is-active.subitem1) {
  color: $font-color-important;
}

:global(.el-menu-item.subitem2) {
  background-color: $bg-color-nav2;
  color: $font-color-white;
  text-indent: 10px;
  &:hover {
    background-color: $bg-color-nav__hover;
  }
}

:global(.el-menu-item.is-active.subitem2) {
  color: $font-color-important;
}

:global(.el-submenu.submenu2) {
  // 一级菜单
  background-color: $bg-color-nav2;
  color: $font-color-white;
  text-indent: 10px;
  @include menu($bg-color-nav3, $font-color-white);
  :global(.el-submenu__title) {
    color: $font-color-white;
    text-indent: 10px;
    &:hover {
      background-color: $bg-color-nav__hover;
    }
  }
}
</style>
