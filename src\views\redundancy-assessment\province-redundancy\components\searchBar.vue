<template>
  <div class="container-head">
    <el-form inline label-width="108px" :show-message="false">
      <el-form-item label="资源池" prop="template_ids" label-width="55px">
        <el-select
          v-model="queryForm.template_ids"
          placeholder="资源池"
          filterable
          style="width: 200px;"
          multiple
        >
          <el-option 
            v-for="item in resource_pool_list"
            :key="item.template_id"
            :label="item.template_name" 
            :value="item.template_id + ''" 
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="时间" prop="id" label-width="55px">
        <el-date-picker
          v-model="timeRange"
          type="datetimerange"
          range-separator="-"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="timestamp"
          :default-time="['00:00:00', '23:59:59']"
          :picker-options="pickerOptions"
          style="width: 340px;"
        />
      </el-form-item>
      <el-form-item label="考虑上下架节点" prop="include_future_lake" label-width="108px">
        <el-select v-model="queryForm.include_future_lake" filterable placeholder="考虑上下架节点" clearable style="width: 68px;">
          <el-option v-for="item in include_future_lake_list" :label="item.label" :value="item.value" :key="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="Lake带宽比例" prop="lake_weight" label-width="108px">
        <el-input v-model.number="queryForm.lake_weight" placeholder="Lake带宽比例" style="width:68px"></el-input>
      </el-form-item>
      <el-form-item label="组能力比例" prop="host_group_weight" label-width="90px">
        <el-input v-model.number="queryForm.host_group_weight" placeholder="组能力比例" style="width:68px"></el-input>
      </el-form-item>
      <el-form-item label="是否考虑商机表" prop="include_biz_table" label-width="108px">
        <el-select v-model="queryForm.include_biz_table" filterable placeholder="是否考虑商机表" clearable style="width: 68px;">
          <el-option v-for="item in include_biz_table_list" :label="item.label" :value="item.value" :key="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="medium" @click="handleSearch" :loading="loading" :disabled="loading">查询</el-button>
        <el-button size="medium" @click="resetForm" :loading="loading" :disabled="loading">重置</el-button>
      </el-form-item>
      <!-- 前端自己过滤的查询条件 -->
      <el-collapse v-model="activeNames" ref="collapse" class="collapse-style">
        <el-collapse-item name="1" :title="collapseTitle">
          <div>
            <el-form-item label="大区" prop="area_view" label-width="108px">
              <el-input v-model="searchForm.area_view" placeholder="请输入大区" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="省份" prop="province" label-width="108px">
              <el-input v-model="searchForm.province" placeholder="请输入省份" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="额定带宽" prop="rated_bw" label-width="108px">
              <el-input v-model="searchForm.rated_bw" placeholder="请输入额定带宽" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="额定能力" prop="rated_cap" label-width="108px">
              <el-input v-model="searchForm.rated_cap" placeholder="请输入额定能力" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="额定动态能力" prop="dyn_rated_cap" label-width="108px">
              <el-input v-model="searchForm.dyn_rated_cap" placeholder="请输入额定动态能力" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="带宽冗余" prop="redu_bw" label-width="108px">
              <el-input v-model="searchForm.redu_bw" placeholder="请输入带宽冗余" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="能力冗余" prop="redu_cap" label-width="108px">
              <el-input v-model="searchForm.redu_cap" placeholder="请输入能力冗余" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="动态能力冗余" prop="redu_dyn_cap" label-width="108px">
              <el-input v-model="searchForm.redu_dyn_cap" placeholder="请输入动态能力冗余" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="当前峰值" prop="cur_peak_bw" label-width="108px">
              <el-input v-model="searchForm.cur_peak_bw" placeholder="请输入当前峰值" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="上架带宽" prop="add_bw" label-width="108px">
              <el-input v-model="searchForm.add_bw" placeholder="请输入上架带宽" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="上架能力" prop="add_cap" label-width="108px">
              <el-input v-model="searchForm.add_cap" placeholder="请输入上架能力" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="下架带宽" prop="del_bw" label-width="108px">
              <el-input v-model="searchForm.del_bw" placeholder="请输入下架带宽" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="下架能力" prop="del_cap" label-width="108px">
              <el-input v-model="searchForm.del_cap" placeholder="请输入下架能力" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="带宽需求" prop="need_bw" label-width="108px">
              <el-input v-model="searchForm.need_bw" placeholder="请输入带宽需求" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="能力需求" prop="need_cap" label-width="108px">
              <el-input v-model="searchForm.need_cap" placeholder="请输入能力需求" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="最终带宽冗余" prop="final_redu_bw" label-width="108px">
              <el-input v-model="searchForm.final_redu_bw" placeholder="请输入最终带宽冗余" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="最终能力冗余" prop="final_redu_cap" label-width="108px">
              <el-input v-model="searchForm.final_redu_cap" placeholder="请输入最终能力冗余" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="最终动态能力冗余" prop="final_dyn_redu_cap" label-width="108px">
              <el-input v-model="searchForm.final_dyn_redu_cap" placeholder="请输入最终动态能力冗余" class="input-style"></el-input>
            </el-form-item>
            <el-form-item label="是否存在无效字段" prop="has_invalid_field" label-width="108px">
              <el-select v-model="searchForm.has_invalid_field" clearable  class="input-style" placeholder="是否存在无效字段">
                <el-option :value="true" label="是"></el-option>
                <el-option :value="false" label="否"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label-width="420px">
              <span slot="label" class="tip-wrap">{{ tip }}</span>
            </el-form-item>
          </div>
        </el-collapse-item>
      </el-collapse>

    </el-form>

  </div>
</template>
<script>

import listMixin from "@/utils/list-pager";
import httpApi from '@/api/http.js'
import copyIpMixin from "@/utils/mixins/copyIp.mixin";
import ExportExcelCommon from '@/helper/exportExcelCommon'
import { download } from "@/utils/downloadCsv";
import mobile from "@/views/redundancy-assessment/province-redundancy/components/mobile.vue";
import telecom from "@/views/redundancy-assessment/province-redundancy/components/telecom.vue";
import unicom from "@/views/redundancy-assessment/province-redundancy/components/unicom.vue";
import provinceMixin from "@/views/redundancy-assessment/mixins/province.mixin";
import { getAm0 as get0 } from "@/utils/time";
import { shortcuts_arr} from "@/utils/pickerOption.js"

// 获取近7天的时间戳
function get7d() {
  const today = get0(new Date());
  let current = new Date()
  let timeStamp = current.getTime()
  return [new Date(+today - 6 * 24 * 60 * 60 * 1000), new Date(timeStamp - 10 * 60 * 1000)];
  // return [new Date(new Date(timeStamp) - 1 * 24 * 60 * 60 * 1000), new Date(timeStamp)];
}

export default {
  name: "provinceSearchBar",
  mixins: [listMixin, copyIpMixin, provinceMixin],
  components: {
    ExportExcelCommon,
    download,
    mobile,
    telecom,
    unicom,
    shortcuts_arr
  },
  props: [],
  data() {
    return {
      loading: false,
      tip: "提示：列表结果为数字的，查询支持 >、< 或者 =，只填数字表示等于该值",
      activeNames: [],
      timeRange: get7d(),
      resource_pool_list: [],
      include_future_lake_list: [
        { label: "否", value: false },
        { label: "是", value: true }
      ],
      include_biz_table_list: [
        { label: "否", value: false },
        { label: "是", value: true }
      ],
      queryForm: {
        template_ids: ['59'], // 资源池
        lake_weight: 85, // Lake带宽比例
        host_group_weight: 100, // 组能力比例
        include_future_lake: false, // 是否包含上下架Lake
        include_biz_table: false
      },
      searchForm: {
        area_view: "",
        province: "",
        rated_bw: "",
        rated_cap: "",
        dyn_rated_cap: "",
        redu_bw: "",
        redu_cap: "",
        redu_dyn_cap: "",
        cur_peak_bw: "",
        add_bw: "",
        add_cap: "",
        del_bw: "",
        del_cap: "",
        need_bw: "",
        need_cap: "",
        final_redu_bw: "",
        final_redu_cap: "",
        final_dyn_redu_cap: "",
      },
      paging: {
        total: 0,
        page: 1,
        page_size: 10
      },
      selectDataList: [],
      maxDayBeforeNow: 30,
      pickerOptions: {
        shortcuts: shortcuts_arr,
        // 设置禁用时间
        disabledDate: (time) => {
          // 获取今天0点数据
          const today0 = get0(new Date());
          // 禁用大于今天时间23：59：59
          if (+time > +new Date(+today0 + 24 * 60 * 60 * 1000 - 1)) return true;
          // 禁用小于设置的最早期限的，由于今天会算1天，需要倒推 x-1 天
          if (+time < +today0 - (this.maxDayBeforeNow- 1) * 24 * 60 * 60 * 1000) return true;
          return false;
        },
        onPick: ({ minDate }) => {
          this.minDate = +minDate;
        },
        reset: () => {
          this.minDate = null;
        },
      },
    };
  },
  watch: {
    searchForm: {
      deep: true,
      handler(val) {
        this.$eventBus.$emit("provinceFormData", this.searchForm);
      },
      immediate: true
    }
  },
  computed: {
    collapseTitle() {
      return this.activeNames.includes("1") ? "收起其它查询条件" : "展开其它查询条件";
    },
  },
  filters: {},
  created() {
    this.$eventBus.$on("refresh", () => {
			this.handleSearch()
		})
  },
  mounted() {
    this.getResourcePool()
    // this.handleSearch()
  },
  beforeDestroy() {
    this.$eventBus.$off('refresh');
  },
  methods: {
    getResourcePool() {
      httpApi.get(`/rap/resource_pool`)
        .then((res) => {
          this.resource_pool_list = res
        })
    },
    resetForm() {
      this.queryForm = this.$options.data().queryForm;
      this.searchForm = this.$options.data().searchForm;
      this.timeRange = get7d()
    },
  },
};
</script>
<style scoped lang="scss">
.collapse-style {
  text-align: left;
  /deep/ {
    .el-collapse-item__header {
      border: 0;
      background-color: #f7f8fa;
      margin-bottom: 20px;
      padding-left: 12px;
      height: 25px;
    }
  }
}
.tip-wrap {
  font-size: 12px;
  color: #a8adaf;
}
.input-style {
  width: 165px;
}
</style>
