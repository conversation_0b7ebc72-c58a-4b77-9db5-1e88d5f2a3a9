<template>
  <el-card>
    <div slot="header" class="clearfix">
      <span>磁盘告警配置</span>
    </div>
    <!-- 查询条件 -->
    <el-form :model="searchForm" ref="searchForm" label-position="left" inline>
      <el-row>
        <el-form-item label="LAKE名称">
          <el-select v-model="searchForm.lake_id" placeholder="请选择LAKE名称" style="width:220px" filterable clearable>
            <el-option :value="-1" label="ALL" />
            <el-option v-for="(item, index) in lakeList" :key="index" :label="item.bk_inst_name" :value="item.bk_inst_id"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button size="medium" type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
        <span style="float:right">
          <el-button size="medium" @click="handleAdd">新增</el-button>
        </span>
      </el-row>
    </el-form>

    <el-table :data="tableData" v-loading="querying">
      <el-table-column prop="lake_name" label="LAKE" align="center" width="100"></el-table-column>
      <el-table-column prop="iowait_limit" label="Iowait异常阈值" align="center" width="110"></el-table-column>
      <el-table-column prop="abnormal_times" label="持续异常次数" align="center" width="100"></el-table-column>
      <el-table-column prop="abnormal_period" label="持续异常周期（s）" align="center" width="145"></el-table-column>
      <el-table-column prop="normal_period" label="消警时长（s）" align="center" width="100"></el-table-column>
      <el-table-column prop="frequent_switch_times" label="频繁切换次数" align="center" width="120"></el-table-column>
      <el-table-column prop="state" label="状态" align="center" width="60">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.state === 0" type="success">启用</el-tag>
          <el-tag v-if="scope.row.state === 1" type="danger">禁用</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="adopt_req_status" label="参考域名5xx个数" align="center" width="120">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.adopt_req_status === 0" type="success">是</el-tag>
          <el-tag v-if="scope.row.adopt_req_status === 1" type="danger">否</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="abnormal_count" label="域名5xx个数" align="center" width="100"></el-table-column>
      <el-table-column prop="create_time" label="创建时间" align="center" width="145">
        <template slot-scope='scope'>
          <span>{{ (scope.row.create_time * 1000) | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="update_time" label="更新时间" align="center" width="145">
        <template slot-scope='scope'>
          <span>{{ (scope.row.update_time * 1000) | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="operator" label="操作人" align="center"></el-table-column>
      <el-table-column label="操作" align="center" width="130">
        <template slot-scope="scope">
          <div>
            <el-button type="text" @click="handleEdit(scope.row)">修改</el-button>
            <el-button type="text" @click="handleDelete(scope.row)">删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-row style="text-align: center; padding-top: 20px">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.page_size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      ></el-pagination>
    </el-row>

    <el-row>
      <!-- 新增 -->
      <disk-alarm-dialog
        v-if="addDialog"
        @close="addDialog = false"
        @refresh="onSearch"
      ></disk-alarm-dialog>
      <!-- 修改 -->
      <disk-alarm-dialog
        v-if="editDialog"
        :is-edit="true"
        :rowData="rowData"
        @close="editDialog = false"
        @refresh="onSearch"
      ></disk-alarm-dialog>
    </el-row>

  </el-card>
</template>
<script>

import http from "@/api/http.js"
import diskAlarmDialog from "@/views/disk-alarm-config/disk-alarm-list/dialog/diskAlarmDialog.vue"
import { mapState } from "vuex";
import dateFormat from "@/utils/dateFormat"

export default {
  name: "disk-alarm-list",
  components: {
    diskAlarmDialog,
  },
  filters: {
    dateFormat: dateFormat
  },
  props: [],
  data() {
    return {
      querying: false,
      tableData: [],
      rowData: {},
      addDialog: false,
      editDialog: false,
      pagination: {
        total: 0,
        page: 1,
        page_size: 20,
      },
      searchForm: {
        lake_id: '',
      },
    };
  },
  computed: {
    ...mapState({
      lakeList: (state) => state.baseData.lakeList,
    }),
  },
  watch: {},
  created() {},
  mounted() {
    this.onSearch()
  },
  methods: {
    handleAdd() {
      this.addDialog = true
    },
    handleEdit(row) {
      this.editDialog = true
      this.rowData = structuredClone(row)
    },
    async handleDelete(row) {
      await this.$confirm("确定删除该条数据吗？", "提示", {
        type: "warning",
      })
      let params = {
        operator: window.localStorage.getItem('userInfo'),
      };
      const res = await http.delete(`/sda/disks/iowaits/${row.id}`, params)
      if (res && res.code === 100000) {
        let msg = (res && res.message) || "删除成功"
        this.$message.success(msg);
        this.onSearch()
      }
    },
    onSearch() {
      this.pagination.page = 1;
      this.query();
    },
    async query() {
      let params = {
        lake_id: this.searchForm.lake_id,
        page: this.pagination.page,
        page_size: this.pagination.page_size,
      };
      this.querying = true;
      try {
        await http.get(`/sda/disks/iowaits`, params).then((res) => {
          this.tableData = res && res.data && res.data.items;
          this.pagination.total = res && res.data && res.data.total;
          this.querying = false;
        });
      } catch (error) {
        this.querying = false;
      } finally {
        this.querying = false;
      }
    },
    handleSizeChange(val) {
      this.pagination.page = 1;
      this.pagination.page_size = val;
      this.query();
    },
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.query();
    },
  },
};
</script>
<style scoped lang="scss">
.btn {
  float: right;
}
</style>
