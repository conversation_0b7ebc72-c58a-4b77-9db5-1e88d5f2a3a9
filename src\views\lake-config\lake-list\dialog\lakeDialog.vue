<template>
  <el-dialog
    append-to-body
    :title="title"
    :visible="true"
    :close-on-click-modal="false"
    width="800px"
    @close="handleCancel"
  >
    <el-row>
      <el-form :inline="true" ref="dialogForm" :model="dialogForm" :rules="rules" label-width="150px" label-position="right">
        <el-row>
          <el-form-item label="lake" prop="lake_id">
            <el-select v-model="dialogForm.lake_id" placeholder="请选择lake" style="width:420px" :disabled="isEdit" filterable clearable>
              <el-option v-for="(item, index) in lakeList" :key="index" :label="item.bk_inst_name" :value="item.bk_inst_id"></el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="业务状态" prop="biz_status">
            <el-select v-model="dialogForm.biz_status" placeholder="请选择业务状态" style="width:420px">
              <el-option :label="'可用'" :value="0"></el-option>
              <el-option :label="'不可用'" :value="1"></el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="view" prop="view_id">
            <el-select v-model="dialogForm.view_id" placeholder="请选择view" style="width:420px" filterable clearable>
              <el-option v-for="(item, index) in viewList" :key="index" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="供应商" prop="supplier_id">
            <el-select v-model="dialogForm.supplier_id" placeholder="请选择供应商" style="width:420px" filterable clearable>
              <el-option v-for="(item, index) in supplierList" :label="`${item.supplier_name} (${item.supplier_code})`" :key="index" :value="item.id">
                <span style="float: left">{{ item.supplier_name }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ item.supplier_code }}</span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row>
          <el-col :span="10">
            <el-form-item label="v4比例" prop="v4_ratio" class="ct-form-style">
              <el-input v-model.number="dialogForm.v4_ratio" placeholder="请输入v4比例" class="w-120"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="v6比例" prop="v6_ratio" class="ct-form-style" label-width="100px">
              <el-input v-model.number="dialogForm.v6_ratio" placeholder="请输入v6比例" class="w-120"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- v4、v6个数 -->
        <el-row>
          <el-col :span="10">
            <el-form-item label="v4个数" prop="v4_count" class="ct-form-style">
              <el-input v-model.number="dialogForm.v4_count" placeholder="请输入v4个数" class="w-120"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="v6个数" prop="v6_count" class="ct-form-style" label-width="100px">
              <el-input v-model.number="dialogForm.v6_count" placeholder="请输入v6个数" class="w-120"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item label="自动生成覆盖变更方案" prop="cover_gen_switch">
            <el-switch
              v-model.number="dialogForm.cover_gen_switch"
              :inactive-value="0"
              :active-value="1"
            ></el-switch>
          </el-form-item>
        </el-row>
      </el-form>
    </el-row>

    <div slot="footer" class="dialog-footer">
      <el-button size="medium" @click="handleCancel">取 消</el-button>
      <el-button size="medium" type="primary" @click="handleSubmit" :loading="submiting" :disabled="submiting">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import http from '@/api/http.js'
import { mapState } from "vuex";

export default {
  components: {},
  props: {
    isEdit: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: null
    },
  },
  data() {
    return {
      submiting: false,
      supplierList: [], // 供应商列表
      dialogForm: {
        lake_id: "", // lake
        view_id: "",
        v4_ratio: "",
        v6_ratio: "",
        cover_gen_switch: 0,
        v4_count: "",
        v6_count: "",
        supplier_id: "", // 供应商ID
        biz_status: 0,
      },
      rules: {
        lake_id: [
          { required: true, message: "请选择lake", trigger: "change"}
        ],
        view_id: [
          { required: true, message: "请选择view", trigger: "change"}
        ],
        v4_ratio: [
          { required: true, message: "请输入v4比例", trigger: "blur"},
          { trigger: "blur", pattern: /^[0-9]*$/, message: "请输入[0, 100]的整数" },
          {
            validator: (rule, value, callback) => {
              const v4_ratio = parseInt(this.dialogForm.v4_ratio)
              const v6_ratio = parseInt(this.dialogForm.v6_ratio)
              const sum = v4_ratio + v6_ratio
              if (sum <= 0) {
                callback(new Error("v4比例和v6比例之和要大于0"));
              } else if (value > 100) {
                callback(new Error("请输入[0, 100]的整数"));
              } else callback();
            },
            trigger: ["blur", "change"],
          },
        ],
        v6_ratio: [
          { required: true, message: "请输入v6比例", trigger: "blur"},
          { trigger: "blur", pattern: /^[0-9]*$/, message: "请输入[0, 100]的整数" },
          {
            validator: (rule, value, callback) => {
              const v4_ratio = parseInt(this.dialogForm.v4_ratio)
              const v6_ratio = parseInt(this.dialogForm.v6_ratio)
              const sum = v4_ratio + v6_ratio
              if (sum <= 0) {
                callback(new Error("v4比例和v6比例之和要大于0"));
              } else if (value > 100) {
                callback(new Error("请输入[0, 100]的整数"));
              } else callback();
            },
            trigger: ["blur", "change"],
          },
        ],
        v4_count: [
          { required: true, message: "请输入", trigger: "blur"},
          { trigger: "blur", pattern: /^[0-9]*$/, message: "请输入大于等于0的整数" },
          { required: true, validator: this.commonValid, trigger: "blur" },
        ],
        v6_count: [
          { required: true, message: "请输入", trigger: "blur"},
          { trigger: "blur", pattern: /^[0-9]*$/, message: "请输入大于等于0的整数" },
          { required: true, validator: this.commonValid, trigger: "blur" },
        ],
      },
    }
  },
  computed: {
    title() {
      return this.isEdit ? "修改" : "新增";
    },
    ...mapState({
      lakeList: (state) => state.baseData.lakeList,
      viewList: (state) => state.baseData.viewList,
    }),
  },
  mounted() {
    if (this.isEdit) {
      this.handleInitData()
    }
    this.getSupplierList()
  },
  methods: {
    async getSupplierList() {
      try {
        const res = await http.get('/sda/supplier/get', {
          page_size: 10000
        })
        if (res && res.code === 100000) {
          this.supplierList = res.data.items || []
        }
      } catch (error) {
        console.error(error)
      }
    },
    async handleSubmit() {
      try {
        await new Promise((resolve, reject) => {
          this.$refs.dialogForm.validate(valid => {
            valid ? resolve() : reject('表单项有错误，请检查')
          })
        })
      } catch (err) {
        this.$message({
          showClose: true,
          message: err.message || err,
          type: 'error'
        })
        return false
      }
      this.submiting = true
      let view = this.viewList.find(item => {
        return item.id === this.dialogForm.view_id
      })
      let params = {
        ...this.dialogForm,
        v4_ratio: parseInt(this.dialogForm.v4_ratio),
        v6_ratio: parseInt(this.dialogForm.v6_ratio),
        view_name: (view && view.name) || "",
        cover_gen_switch: parseInt(this.dialogForm.cover_gen_switch),
        supplier_id: this.dialogForm.supplier_id || 0,
        biz_status: parseInt(this.dialogForm.biz_status)
      }
      let res = {}
      let successMsg = "修改成功"
      res = await http.post(`/sda/lake_view/update`, params)
      if (res && res.code === 100000) {
        this.$message.success(successMsg);
        this.$emit("close")
        this.$emit("refresh")
        this.submiting = false
      } else {
        this.submiting = false
      }
    },
    handleCancel() {
      this.$emit("close")
      this.$emit("refresh")
    },
    handleInitData() {
      const row = structuredClone(this.rowData)
      this.dialogForm.lake_id = row.lake_id
      this.dialogForm.view_id = row.view_id
      this.dialogForm.v4_ratio = row.v4_ratio
      this.dialogForm.v6_ratio = row.v6_ratio
      this.dialogForm.cover_gen_switch = row.cover_gen_switch
      this.dialogForm.v4_count = row.v4_count
      this.dialogForm.v6_count = row.v6_count
      this.dialogForm.supplier_id = row.supplier_id === 0 ? "" : row.supplier_id
      this.dialogForm.biz_status = row.biz_status || 0
    },
    async commonValid(rule, value, callback) {
      const v4_count = parseInt(this.dialogForm.v4_count)
      const v6_count = parseInt(this.dialogForm.v6_count)
      const sum = v4_count + v6_count
      const data = parseInt(value)
      if (data < 0) {
        callback(new Error("请输入大于等于0的整数"));
      } else if (sum <= 0) {
        callback(new Error("v4个数和v6个数之和要大于0"));
      } else callback();
      return callback();
    },
  },
}
</script>

<style scoped lang="scss">
/deep/ {
  .ct-form-style {
    .el-form-item__error {
      margin-left: 0 !important;
      width: 160px !important;
    }
  }
}
.w-120 {
  width: 120px;
}
</style>
