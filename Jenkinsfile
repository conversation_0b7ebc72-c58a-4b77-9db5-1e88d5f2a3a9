@Library('cdn-devops') _

def RELEASE_BUILD
String BUILD_RESULT = ""


pipeline {
    agent {
        label 'jnlp-slave'
    }
	options {
		buildDiscarder(logRotator(numToKeepStr: '10'))
		disableConcurrentBuilds()
		skipDefaultCheckout()
		timeout(time: 30, unit: 'MINUTES')
		gitLabConnection('gitlab')
	}
    environment {
//        DEV_BRANCH="release.*"
//        QA_BRANCH="develop"
        DEV_BRANCH="develop"
        QA_BRANCH="release.*"
        QA_TAG = "v\\d+\\.\\d+.\\d+(-rc\\d+)?" // v1.0.0-rc1
        TPL = "others"
        DEV_NAMESPACE = "cdngslb-dev"
        QA_NAMESPACE = "cdngslb-test"
        IMAGE_CREDENTIALS = "credential-harbor"
        DEPLOYMENT = "sda-ui"
        IMAGE_REPOSITORY = "harbor.ctyuncdn.cn/test/sda-ui"
    }

    stages {
        stage('Checkout') {
            when {
                expression { BRANCH_NAME ==~ env.DEV_BRANCH || BRANCH_NAME ==~ env.QA_BRANCH || BRANCH_NAME ==~ env.QA_TAG }
            }
            steps {
                script {
                    container('tools') {
                        // checkout code
                        retry(2) { scmVars = checkout scm }
                        RELEASE_BUILD = scmVars.GIT_COMMIT
                        BUILD_RESULT = devops.updateBuildTasks(BUILD_RESULT,"Checkout OK...√")
                        echo 'begin checkout...'
                        echo sh(returnStdout: true, script: "env")
                    }
                }
            }
        }
        stage('node_mpdules') {
            when {
                expression { BRANCH_NAME ==~ env.DEV_BRANCH || BRANCH_NAME ==~ env.QA_BRANCH || BRANCH_NAME ==~ env.QA_TAG }
            }
            steps {
                script {
                    container('tools') {
                        sh """
                        cd src;
                        rm -rf node_modules/;
                        npm cache clean --force;
                        if [ ! -d "node_modules" ];then npm install -verbose --unsafe-perm=true --allow-root;fi
                        """
                    }
                }
            }
        }
        stage('npm-build') {
            when {
                expression { BRANCH_NAME ==~ env.DEV_BRANCH || BRANCH_NAME ==~ env.QA_BRANCH || BRANCH_NAME ==~ env.QA_TAG }
            }
            steps {
                script {
                    container('tools') {
                        retry(2) {
                            sh """
                            cd src/;
                            npm run build"""
                        }
                        BUILD_RESULT = devops.updateBuildTasks(BUILD_RESULT,"npm-build OK...√")
                    }
                }
            }
        }
        stage('CI'){
            failFast true
            parallel {
                stage('Unit Test') {
                    when {
                        expression { BRANCH_NAME ==~ env.DEV_BRANCH}
                    }
                    steps {
                        script {
                            container('tools') {
                                echo 'skip unit test'
                            }
                        }
                    }
                }
                stage('Code Scan') {
                    when {
                        expression { BRANCH_NAME ==~ env.DEV_BRANCH }
                    }
                    steps {
                        script {
                            container('tools') {
                                echo 'skip'
                            }
                        }
                    }
                }
            }
        }
        stage('Build-Image') {
            when {
                expression { BRANCH_NAME ==~ env.DEV_BRANCH || BRANCH_NAME ==~ env.QA_BRANCH || BRANCH_NAME ==~ env.QA_TAG }
            }
            steps {
                script {
                    container('tools') {
                        devops.dockerBuild(
                            "Dockerfile", //Dockerfile
                            ".", // build context
                            IMAGE_REPOSITORY, // repo address
                            RELEASE_BUILD, // tag
                            IMAGE_CREDENTIALS, // credentials for pushing
                        ).start().push()
                    }
                }
            }
        }
        stage('Deploy') {
            when {
                expression { BRANCH_NAME ==~ env.DEV_BRANCH || BRANCH_NAME ==~ env.QA_BRANCH || BRANCH_NAME ==~ env.QA_TAG }
            }
            steps {
                script {
                    container('tools') {
                        dep = devops.deploy(
                            "deploy", //k8s files dir
                            "deploy/deployment.yaml",
                            RELEASE_BUILD,
                            true
                        )
                        dep.start()
                    }
                }
            }
        }

    }
    post {
        success {
            script {
                container('tools') {
                    devops.notificationSuccess(DEPLOYMENT, "流水线完成了", RELEASE_BUILD, "dingTalk")
                }
            }
        }
        failure {
            script {
                container('tools') {
                    devops.notificationFailed(DEPLOYMENT, "流水线失败了", RELEASE_BUILD, "dingTalk")
                }
            }
        }
    }
}
