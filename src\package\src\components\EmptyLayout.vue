<!-- 空组件，用于承载父层路由 -->
<template>
  <div class="empty-layout" :class="{'fix-header': fixHeader}">
    <keep-alive :include="cachedPages" :max="8">
      <router-view v-if="$route.meta.keepAlive"></router-view>
    </keep-alive>
    <router-view v-if="!$route.meta.keepAlive"></router-view>
  </div>
</template>

<script>
export default {
  name: 'EmptyLayout',
  computed: {
    cachedPages () {
      return this.$store.getters.cachedPages.map(v => v.name)
    },
    fixHeader() {
      return this.$route.meta && this.$route.meta.fixHeader
    }
  }
}
</script>