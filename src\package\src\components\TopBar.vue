<template>
  <div class="top">
    <div class="top--link">
      <div v-for="item in linkList" :key="item.name" @click="toOtherWeb(item)" class="top--link--item">
        <img :src="item.iconPath" />
        <span>{{ item.name }}</span>
      </div>
    </div>
    <div class="top--user">
      <img :src="userPhoto" />
      <span>{{ username }}</span>
      <span class="top--user--division">|</span>
      <span class="iconfont iconzhuanchu" @click="logout"></span>
    </div>
  </div>
</template>

<script>
// import ServerConfig from '../../../../static/serverConfig.json'
import Cookies from 'js-cookie'
export default {
  name: 'TopBar',
  data () {
    return {
      linkList: [
        { name: 'YUN JIRA', iconPath: require('../assets/images/icon-yun-jira.png'), link: window.api.jumpURL2 },
        { name: '自动化调度', iconPath: require('../assets/images/icon-auto-dispatch.png'), link: window.api.jumpURL3 },
        // { name: '配置管理', iconPath: require('../assets/images/icon-config.png'), link: '/configManage/' }
      ]
    }
  },
  computed: {
    username () {
      return localStorage.getItem('userInfo')
    },
    userPhoto () {
      return localStorage.getItem('userPhoto')
    }
  },
  methods: {
    toOtherWeb (item) {
      window.open(item.link, '_blank')
    },
    logout () {
      Cookies.remove('sidebarStatus')
      localStorage.clear()
      window.location.href = '/v1/auth/logout'
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../assets/styles/var.scss';
@import '../assets/styles/mixins.scss';
$top-height: 60px;

.top {
  height: $top-height;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 14px;
  color: $font-color-primary;
  font-size: $font-size-large;
  background-color: $bg-color-module;

  &--link {
    display: flex;
    align-items: center;

    &--item {
      @include center;
      cursor: pointer;

      > span {
        margin: 0 30px 0 10px;
      }

      > img {
        width: 28px;
        height: 28px;
      }
    }
  }

  &--user {
    display: flex;
    align-items: center;
    color: #929DB1;

    > img {
      width: 28px;
      height: 28px;
      margin-right: 8px;
      border-radius: 50%;
    }

    &--division {
      margin: 0 8px;
    }

    .iconzhuanchu {
      color: $font-color-important;
      cursor: pointer;
    }
  }
}
</style>
