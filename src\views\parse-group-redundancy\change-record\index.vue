<template>
  <el-card>
    <!-- 解析组系数变更记录 -->
    <!-- 查询条件 -->
    <el-form :model="searchForm" ref="searchForm" label-position="left" inline>
      <el-row>
        <el-form-item label="解析组" prop="parse_group_id">
          <el-select v-model="searchForm.parse_group_id" placeholder="请选择解析组名称" filterable clearable style="width:300px">
            <el-option :value="-1" label="ALL" />
            <el-option v-for="(item, index) in parseGroupList" :key="index" :label="item.parse_group_name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="状态" prop="state">
          <el-select v-model="searchForm.state" placeholder="请选择状态" filterable clearable style="width:140px">
            <el-option v-for="(item, index) in stateList" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button size="medium" type="primary" @click="onSearch">查询</el-button>
        </el-form-item>

        <span style="float: right">
          <el-button size="medium" type="primary" @click="handleBatch">批量重提</el-button>
        </span>
      </el-row>
    </el-form>
    <!-- 列表 -->
    <el-table :data="tableData" v-loading="queryLoading" @selection-change="handleSelectionChange">
      <el-table-column type="selection" align="center" width="55"></el-table-column>
      <el-table-column prop="parse_group_id" label="解析组ID" align="center"></el-table-column>
      <el-table-column prop="parse_group_name" label="解析组名称" align="center"></el-table-column>
      <el-table-column prop="cur_redundancy" label="当前DCP冗余系数" align="center"></el-table-column>
      <el-table-column prop="redundancy" label="新冗余系数" align="center"></el-table-column>
      <el-table-column prop="state" label="状态" align="center">
        <template slot-scope="scope">
          <el-tag slot="reference" v-if="scope.row.state === 0" type="info">待生效</el-tag>
          <el-tag slot="reference" v-if="scope.row.state === 1" type="success">已生效</el-tag>
          <el-tag slot="reference" v-if="scope.row.state === 2" type="danger">校验失败</el-tag>
          <el-tag slot="reference" v-if="scope.row.state === 3" type="danger">执行失败</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="create_date" label="更新日期" align="center">
        <template slot-scope='scope'>
          <span>{{ scope.row.create_date }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="operator" label="操作人" align="center"></el-table-column>
      <el-table-column prop="update_time" label="更新时间" align="center">
        <template slot-scope='scope'>
          <span>{{ (scope.row.update_time * 1000) | dateFormat }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-row style="text-align: center; padding-top: 20px">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.page_size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      ></el-pagination>
    </el-row>

  </el-card>
</template>
<script>

import http from "@/api/http.js"
import { mapState } from "vuex";
import dateFormat from "@/utils/dateFormat"

export default {
  name: "lake-list",
  components: {},
  filters: {
    dateFormat: dateFormat
  },
  props: [],
  data() {
    return {
      searchForm: {
        parse_group_id: '',
        state: '',
      },
      queryLoading: false,
      tableData: [],
      multipleSelection: [],
      stateList: [
        { label: "待生效", value: 0 },
        { label: "已生效", value: 1 },
        { label: "校验失败", value: 2 },
        { label: "执行失败", value: 3 }
      ],
      pagination: {
        total: 0,
        page: 1,
        page_size: 20,
      },
    };
  },
  computed: {
    ...mapState({
      parseGroupList: (state) => state.baseData.parseGroupList,
    }),
  },
  mounted() {
    this.onSearch()
  },
  methods: {
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    /**
     * 批量重提
     */
    async handleBatch() {
      if (!(this.multipleSelection && this.multipleSelection.length > 0)) {
        this.$message.warning("请先选择数据！")
        return;
      }
      const length = this.multipleSelection && this.multipleSelection.length
      await this.$confirm(`已选中${length}条数据，确定批量重提选中的数据吗？`, "提示", {
        type: "warning",
      });
      // 获取id
      const ids = this.multipleSelection && this.multipleSelection.map(item => item.id)
      let params = {
        ids: ids,
        operator: window.localStorage.getItem('userInfo'),
      };
      const res = await http.post(`/sda/parse_group/redundancy/records`, params)
      if (res && res.code === 100000) {
        let msg = (res && res.message) || "手动恢复成功"
        this.$message.success(msg);
        this.onSearch()
      }
    },
    onSearch() {
      this.pagination.page = 1;
      this.query();
    },
    async query() {
      let params = {
        ...this.searchForm,

        page: this.pagination.page,
        page_size: this.pagination.page_size,
      };
      this.queryLoading = true;
      try {
        await http.get(`/sda/parse_group/redundancy/records`, params).then((res) => {
          this.tableData = res && res.data && res.data.items;
          this.pagination.total = res && res.data && res.data.total;
          this.queryLoading = false;
        });
      } catch (error) {
        this.queryLoading = false;
      } finally {
        this.queryLoading = false;
      }
    },
    handleSizeChange(val) {
      this.pagination.page = 1;
      this.pagination.page_size = val;
      this.query();
    },
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.query();
    },
  },
};
</script>
<style scoped lang="scss">
.btn {
  float: right;
}
</style>
