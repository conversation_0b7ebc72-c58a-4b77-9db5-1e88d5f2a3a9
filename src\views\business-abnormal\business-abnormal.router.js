import EmptyLayout from "@/package/src/components/EmptyLayout";
export default [
  {
    path:'/businessAbnormal',
    component: EmptyLayout,
    name: "businessAbnormal",
    meta: {
      title: "业务异常切换",
    },
    children: [
      {
        path: 'lakeParsegroupConfig',
        name: 'lakeParsegroupConfig',
        component: resolve => require(['@/views/business-abnormal/lake-parsegroup-config/index.vue'], resolve),
        meta: {
          title: 'LAKE解析组配置',
          home: true,
          keepAlive: true
        },
        hidden: false
      },
      {
        path: 'businessAbnormalSwitchRecord',
        name: 'businessAbnormalSwitchRecord',
        component: resolve => require(['@/views/business-abnormal/business-abnormal-switch-record/index.vue'], resolve),
        meta: {
          title: '业务异常切换记录',
          home: true,
          keepAlive: true
        },
        hidden: false
      },
      {
        path: 'vipBusinessAbnormalSwitchRecord',
        name: 'vipBusinessAbnormalSwitchRecord',
        component: resolve => require(['@/views/business-abnormal/vip-business-abnormal-switch-record/index.vue'], resolve),
        meta: {
          title: 'vip业务异常切换记录',
          home: true,
          keepAlive: true
        },
        hidden: false
      },
    ]
  }
]