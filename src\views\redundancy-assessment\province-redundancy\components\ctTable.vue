<template>
  <div>
    <div slot="header" class="container-head">
      <div class="btns">
        <el-button type="primary" icon="el-icon-download" @click="exportPart">部分导出</el-button>
        <el-button type="primary" icon="el-icon-download" @click="exportAll">全量导出</el-button>
        <export-excel-common
          ref='myChild'
          :exportExcelInfo='exportExcelInfo'
          :tableData='selectDataList'
          :exportExcelArry='exportExcelArry'
        ></export-excel-common>
      </div>
    </div>

    <el-table :data="showList" @selection-change="handleSelectionChange" :row-key="row => row.province" ref="provinceTable" highlight-current-row>
      <el-table-column type="selection" width="55" :reserve-selection="true"></el-table-column>
      <el-table-column label="大区" prop="area_view" width="80" align="center" sortable :sort-method="(a, b) => a.area_view.localeCompare(b.area_view)"></el-table-column>
      <el-table-column label="省份" prop="province" width="80" align="center" sortable :sort-method="(a, b) => a.province.localeCompare(b.province)"></el-table-column>
      <el-table-column label="额定带宽" prop="rated_bw" width="105" align="center" sortable></el-table-column>
      <el-table-column label="额定能力" prop="rated_cap" width="120" align="center" sortable></el-table-column>
      <el-table-column label="额定动态能力" prop="dyn_rated_cap" width="120" align="center" sortable></el-table-column>
      <el-table-column label="带宽冗余" prop="redu_bw" width="100" align="center" sortable></el-table-column>
      <el-table-column label="能力冗余" prop="redu_cap" width="100" align="center" sortable></el-table-column>
      <el-table-column label="动态能力冗余" prop="redu_dyn_cap" width="120" align="center" sortable></el-table-column>
      <el-table-column label="当前峰值" prop="cur_peak_bw" width="105" align="center" sortable></el-table-column>
      <el-table-column label="上架带宽" prop="add_bw" width="105" align="center" sortable></el-table-column>
      <el-table-column label="上架能力" prop="add_cap" width="105" align="center" sortable></el-table-column>
      <el-table-column label="下架带宽" prop="del_bw" width="105" align="center" sortable></el-table-column>
      <el-table-column label="下架能力" prop="del_cap" width="105" align="center" sortable></el-table-column>
      <el-table-column label="带宽需求" prop="need_bw" width="105" align="center" sortable></el-table-column>
      <el-table-column label="能力需求" prop="need_cap" width="105" align="center" sortable></el-table-column>
      <el-table-column label="最终带宽冗余" prop="final_redu_bw" width="125" align="center" sortable></el-table-column>
      <el-table-column label="最终能力冗余" prop="final_redu_cap" width="125" align="center" sortable></el-table-column>
      <el-table-column label="最终动态能力冗余" prop="final_dyn_redu_cap" width="145" align="center" sortable></el-table-column>
      <el-table-column label="是否存在无效字段" prop="has_invalid_field" width="145" align="center" sortable>
        <template slot-scope="scope">
          <el-tag slot="reference" v-if="scope.row.has_invalid_field === false" type="success">否</el-tag>
          <el-tag slot="reference" v-if="scope.row.has_invalid_field === true" type="danger">是</el-tag>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      layout="total, sizes, prev, pager, next, jumper"
      :total="filterList.length"
      :current-page.sync="page"
      :page-sizes="[10, 50, 100, 500]"
      :page-size="pageSize"
      @size-change="pageSizeChange"
      class="base-pagination"
    ></el-pagination>

  </div>
</template>
<script>

import listMixin from "@/utils/list-pager";
import provinceMixin from "@/views/redundancy-assessment/mixins/province.mixin";
import ExportExcelCommon from '@/helper/exportExcelCommon'

export default {
  name: "edgeConfig",
  filters: {},
  mixins: [
    listMixin,
    provinceMixin
  ],
  components: {
    ExportExcelCommon
  },
  props: {
    tableData: {
      type: Array,
      required: true,
      default: () => [],
    },
    exportPartInfo: {
      type: Object,
      default: () => {
        return {}
      },
    },
    exportAllName: {
      type: String,
      default: "",
    }
  },
  data() {
    return {
      exportExcelInfo: {
        excelId: "",
        excelName: ""
      },
      form: {},
      page: 1,
      pageSize: 10,
      selectDataList: [],
      has_invalid_field_map: {
        true: "是",
        false: "否"
      },
      exportExcelArry: [
        { prop: 'area_view', label: '大区', formatterFlag: false },
        { prop: 'province', label: '省份', formatterFlag: false },
        { prop: 'rated_bw', label: '额定带宽', formatterFlag: false },
        { prop: 'rated_cap', label: '额定能力', formatterFlag: false },
        { prop: 'dyn_rated_cap', label: '额定动态能力', formatterFlag: false },
        { prop: 'redu_bw', label: '带宽冗余', formatterFlag: false },
        { prop: 'redu_cap', label: '能力冗余', formatterFlag: false },
        { prop: 'redu_dyn_cap', label: '动态能力冗余', formatterFlag: false },
        { prop: 'cur_peak_bw', label: '当前峰值', formatterFlag: false },
        { prop: 'add_bw', label: '上架带宽', formatterFlag: false },
        { prop: 'add_cap', label: '上架能力', formatterFlag: false },
        { prop: 'del_bw', label: '下架带宽', formatterFlag: false },
        { prop: 'del_cap', label: '下架能力', formatterFlag: false },
        { prop: 'need_bw', label: '带宽需求', formatterFlag: false },
        { prop: 'need_cap', label: '能力需求', formatterFlag: false },
        { prop: 'final_redu_bw', label: '最终带宽冗余', formatterFlag: false },
        { prop: 'final_redu_cap', label: '最终能力冗余', formatterFlag: false },
        { prop: 'final_dyn_redu_cap', label: '最终动态能力冗余', formatterFlag: false },
        { prop: 'has_invalid_field', label: '是否存在无效字段', formatterFlag: false },
      ],
    };
  },
  computed: {
    filterList() {
      let filterList = (this.tableData && this.tableData.slice()) || [];
      const area_view = this.form.area_view && this.form.area_view.trim();
      const province = this.form.province && this.form.province.trim();
      const rated_bw = this.form.rated_bw && this.form.rated_bw.trim();
      const rated_cap = this.form.rated_cap && this.form.rated_cap.trim();
      const dyn_rated_cap = this.form.dyn_rated_cap && this.form.dyn_rated_cap.trim();
      const redu_bw = this.form.redu_bw && this.form.redu_bw.trim();
      const redu_cap = this.form.redu_cap && this.form.redu_cap.trim();
      const redu_dyn_cap = this.form.redu_dyn_cap && this.form.redu_dyn_cap.trim();
      const cur_peak_bw = this.form.cur_peak_bw && this.form.cur_peak_bw.trim();
      const add_bw = this.form.add_bw && this.form.add_bw.trim();
      const add_cap = this.form.add_cap && this.form.add_cap.trim();
      const del_bw = this.form.del_bw && this.form.del_bw.trim();
      const del_cap = this.form.del_cap && this.form.del_cap.trim();
      const need_bw = this.form.need_bw && this.form.need_bw.trim();
      const need_cap = this.form.need_cap && this.form.need_cap.trim();
      const final_redu_bw = this.form.final_redu_bw && this.form.final_redu_bw.trim();
      const final_redu_cap = this.form.final_redu_cap && this.form.final_redu_cap.trim();
      const final_dyn_redu_cap = this.form.final_dyn_redu_cap && this.form.final_dyn_redu_cap.trim();
      const has_invalid_field = this.form.has_invalid_field;
      if (area_view) {
        filterList = filterList.filter(item => item.area_view?.includes(area_view));
      }
      if (province) {
        filterList = filterList.filter(item => item.province?.includes(province));
      }
      if (has_invalid_field !== "" && has_invalid_field !== null && has_invalid_field !== undefined) {
        filterList = filterList.filter(item => item.has_invalid_field === has_invalid_field);
      }
      function f1(val, fieldName) {
        if (val !== 0 && !val) return
        let symbol = val && val.substring(0, 1)
        let data = val && val.substring(1)
        if (symbol === '>') {
          filterList = filterList.filter(item => item[fieldName] > data);
        } else if (symbol === '<') {
          filterList = filterList.filter(item => item[fieldName] < data);
        } else if (symbol === '=') {
          filterList = filterList.filter(item => item[fieldName] = data);
        } else {
          filterList = filterList.filter(item => (item[fieldName] + '') === val);
        }
      }
      // Lake出口带宽
      if (rated_bw) {
        f1(rated_bw, 'rated_bw')
      }
      if (rated_cap) {
        f1(rated_cap, 'rated_cap')
      }
      if (dyn_rated_cap) {
        f1(dyn_rated_cap, 'dyn_rated_cap')
      }
      if (redu_bw) {
        f1(redu_bw, 'redu_bw')
      }
      if (redu_cap) {
        f1(redu_cap, 'redu_cap')
      }
      if (redu_dyn_cap) {
        f1(redu_dyn_cap, 'redu_dyn_cap')
      }
      if (cur_peak_bw) {
        f1(cur_peak_bw, 'cur_peak_bw')
      }
      if (add_bw) {
        f1(add_bw, 'add_bw')
      }
      if (add_cap) {
        f1(add_cap, 'add_cap')
      }
      if (del_bw) {
        f1(del_bw, 'del_bw')
      }
      if (del_cap) {
        f1(del_cap, 'del_cap')
      }
      if (need_bw) {
        f1(need_bw, 'need_bw')
      }
      if (need_cap) {
        f1(need_cap, 'need_cap')
      }
      if (final_redu_bw) {
        f1(final_redu_bw, 'final_redu_bw')
      }
      if (final_redu_cap) {
        f1(final_redu_cap, 'final_redu_cap')
      }
      if (final_dyn_redu_cap) {
        f1(final_dyn_redu_cap, 'final_dyn_redu_cap')
      }

      filterList = filterList && filterList
        .map((item, index) => ({
            ...item,
            sortIdx: index, // 用于解决排序不稳定问题，在排序前预存当前顺序，而不是使用排序过程中会变的 index
        }))
        .sort((a, b) => {
          return a.area_view.localeCompare(b.area_view) || a.province.localeCompare(b.province)
        })
        .map((item, index) => ({
            ...item,
            index: index + 1, // 增加序号
        }));

      return filterList;
    },
    showList() {
      // 过滤完成后，再分页
      const start = (this.page - 1) * this.pageSize;
      const end = this.page * this.pageSize;
      const showList = this.filterList && this.filterList.slice(start, end);
      return showList;
    }
  },
  created() {
    this.$eventBus.$on("provinceFormData", (data) => {
			this.form = data
		})
  },
  beforeDestroy() {
    this.$eventBus.$off('provinceFormData');
  },
  watch: {
    filterList: {
      deep: true,
      handler() {
        this.$refs.provinceTable && this.$refs.provinceTable.clearSelection()
      },
      immediate: true
    }
  },
  methods: {
    handleSelectionChange(val) {
      this.selectDataList = []
      val.forEach(v => {
        this.selectDataList.push({
          area_view: v.area_view,
          province: v.province,
          rated_bw: v.rated_bw,
          rated_cap: v.rated_cap,
          dyn_rated_cap: v.dyn_rated_cap,
          redu_bw: v.redu_bw,
          redu_cap: v.redu_cap,
          redu_dyn_cap: v.redu_dyn_cap,
          cur_peak_bw: v.cur_peak_bw,
          add_bw: v.add_bw,
          add_cap: v.add_cap,
          del_bw: v.del_bw,
          del_cap: v.del_cap,
          need_bw: v.need_bw,
          need_cap: v.need_cap,
          final_redu_bw: v.final_redu_bw,
          final_redu_cap: v.final_redu_cap,
          final_dyn_redu_cap: v.final_dyn_redu_cap,
          has_invalid_field: this.has_invalid_field_map[v.has_invalid_field],
        })
      })
    },
    pageSizeChange(val) {
      this.page = 1,
      this.pageSize = val
    }
  },
};
</script>
<style scoped lang="scss">
.btns {
  float: right;
  margin-bottom: 20px !important;
}
.base-pagination {
  text-align: center;
  margin-top: 15px !important;
}
</style>
