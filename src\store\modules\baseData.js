import { getLakeList, getIpList, getParseGroupList, getViewList, getAllDomainList, getAccessList, getHostGroupList, getNodeList, getAreaList } from '@/utils/fetch'
import { ajax } from "@/api/ajax"
import { formPromise } from "@/utils/fetch"

const baseData = {
  state: {
    lakeList: [],
    fullLakeList: [],
    ipList: [],
    parseGroupList: [],
    viewList: [],
    parge_group_loading: false,
    domainAllList: [],
    accessList: [],
    hostGroupList: [],
    nodeList: [],
    ispList: [],
    areaList: [], // 区域数据列表（大区、省份、城市）
  },
  mutations: {
    SET_LAKE_LIST: (state, list) => {
      state.lakeList = list
    },
    SET_FULL_LAKE_LIST: (state, list) => {
      state.fullLakeList = list
    },
    SET_IP_LIST: (state, list) => {
      state.ipList = list
    },
    SET_PARSE_GROUP_LIST: (state, list) => {
      state.parseGroupList = list
    },
    SET_PARSE_GROUP_LOADING: (state, loading) => {
      state.parge_group_loading = loading
    },
    SET_VIEW_LIST: (state, list) => {
      state.viewList = list
    },
    SET_ALL_DOMAIN_LIST: (state, list) => {
      state.domainAllList = list
    },
    SET_ACCESS_LIST: (state, list) => {
      state.accessList = list
    },
    SET_HOST_GROUP_LIST: (state, list) => {
      state.hostGroupList = list
    },
    SET_NODE_LIST: (state, list) => {
      state.nodeList = list
    },
    SET_ISP_LIST: (state, list) => {
      state.ispList = list
    },
    SET_AREA_LIST: (state, list) => {
      state.areaList = list
    },
  },
  actions: {
    async setLakeList({ commit }) {
      let query = {
        lake_type: 4
      }
      const list = await getLakeList(query)
      commit('SET_LAKE_LIST', list)
    },
    async setIpList({ commit }) {
      const list = await getIpList()
      commit('SET_IP_LIST', list)
    },
    async setParseGroupList({ commit }) {
      let loading = true
      const list = await getParseGroupList()
      loading = false
      commit('SET_PARSE_GROUP_LOADING', loading)
      if (list === "no found") return
      commit('SET_PARSE_GROUP_LIST', list)
    },
    async setViewList({ commit }) {
      const list = await getViewList()
      commit('SET_VIEW_LIST', list)
    },
    async setAllDomainList({ commit }) {
      const list = await getAllDomainList()
      commit('SET_ALL_DOMAIN_LIST', list)
    },
    async setAccessList({ commit }) {
      const list = await getAccessList()
      commit('SET_ACCESS_LIST', list)
    },
    async setHostGroupList({ commit }) {
      const res = await getHostGroupList({ lake_type: 4 })
      commit('SET_HOST_GROUP_LIST', res && res.data || [])
    },
    async setNodeList({ commit }) {
      const res = await getNodeList()
      commit('SET_NODE_LIST', res && res.data || [])
    },
    async setFullLakeList({ commit }) {
      const res = await getLakeList({})
      commit('SET_FULL_LAKE_LIST', res || [])
    },
    async setIspList({ commit }) {
      const res = await formPromise(ajax.get('api/v1/sda/supplier/isp'))
      commit('SET_ISP_LIST', res && res.data || [])
    },
    async setAreaList({ commit }) {
      const list = await getAreaList()
      commit('SET_AREA_LIST', list)
    }
  }
}

export default baseData
