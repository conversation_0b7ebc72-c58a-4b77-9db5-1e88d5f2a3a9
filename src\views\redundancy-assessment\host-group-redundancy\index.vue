<template>
  <el-card>
    <div slot="header" class="clearfix">
      <span>主机组冗余</span>
    </div>
    <!-- 查询条件 -->
    <search-bar></search-bar>

    <el-tabs v-model="activeTab" type="card">
      <el-tab-pane v-for="item in getChildMenu" :key="item.prop" :label="item.label" :name="item.prop"></el-tab-pane>
    </el-tabs>

    <el-scrollbar class="domain-edit-content" v-loading="loading">
      <el-col :span="24" ref="orderform">
        <component
          v-for="item in getChildMenu"
          :key="item.prop"
          :is="item.prop"
          v-show="activeTab === item.prop"
          :ref="item.prop"
        />
      </el-col>
    </el-scrollbar>

  </el-card>
</template>
<script>

import listMixin from "@/utils/list-pager";
import copyIpMixin from "@/utils/mixins/copyIp.mixin";
import ExportExcelCommon from '@/helper/exportExcelCommon'
import { download } from "@/utils/downloadCsv";
import edgeConfig from "@/views/redundancy-assessment/host-group-redundancy/components/edgeConfig.vue";
import firstParentConfig from "@/views/redundancy-assessment/host-group-redundancy/components/firstParentConfig.vue";
import secondParentConfig from "@/views/redundancy-assessment/host-group-redundancy/components/secondParentConfig.vue";
import searchBar from "@/views/redundancy-assessment/host-group-redundancy/components/searchBar.vue";
import hostGroupMixin from "@/views/redundancy-assessment/mixins/hostGroup.mixin";

export default {
  name: "host-group-redundancy",
  mixins: [listMixin, copyIpMixin, hostGroupMixin],
  components: {
    ExportExcelCommon,
    download,
    edgeConfig,
    firstParentConfig,
    secondParentConfig,
    searchBar
  },
  props: [],
  data() {
    return {
      loading: false,
      activeTab: "edgeConfig",
      getChildMenu: [
        { label: "边缘", prop: "edgeConfig"},
        { label: "一层父", prop: "firstParentConfig"},
        { label: "二层父", prop: "secondParentConfig"}
      ],
    };
  },
  computed: {},
  created() {
    this.$eventBus.$on("loadingData", (data) => {
			this.loading = data
		})
  },
  watch: {},
  methods: {},
};
</script>
<style scoped lang="scss">
</style>
