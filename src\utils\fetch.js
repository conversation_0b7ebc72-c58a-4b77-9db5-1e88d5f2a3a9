// 公共接口
import { ajax } from "@/api/ajax";
import { Message } from "element-ui";

export const formPromise = (form, errMsg = "") => {
  return form
    .then((response) => {
      return response.data;
    })
    .catch((err) => {
      console.log(err);
      Message({
        showClose: true,
        message: errMsg || err.message || err.data,
        type: "warning",
      });
    });
};
// 获取lake
const prefix = "api/v1"
export const getLakeList = async (query) => {
  const res = await formPromise(
    ajax.get(`${prefix}/sda/basic_data/lakes`, {
      params: query
    })
  );
  let data = (res && res.data) || []
  return data;
};
// 获取ip
export const getIpList = async () => {
  let query = {
		lake_type: 4
  }
  const res = await formPromise(
    ajax.get(`${prefix}/sda/basic_data/hosts`, {
      params: query
    })
  );
  let data = (res && res.data) || []
  return data;
};
// 获取解析组
export const getParseGroupList = async () => {
  const res = await formPromise(
    ajax.get(`${prefix}/sda/basic_data/parse_groups`)
  );
  let data = (res && res.data) || []
  return data;
};
// 获取区域
export const getViewList = async () => {
  const res = await formPromise(
    ajax.get(`${prefix}/sda/basic_data/views`)
  );
  let data = (res && res.data) || []
  return data;
};
// 获取全量域名
export const getAllDomainList = async () => {
  const res = await formPromise(
    ajax.get(`${prefix}/scc/api/v3/domain/list`)
  );
  let data = (res && res.data && res.data.items) || []
  return data;
};
// 获取接入层
export const getAccessList = async () => {
  const res = await formPromise(
    ajax.get(`${prefix}/dcp/mix/access_all`)
  );
  let data = (res && res.data) || []
  return data;
};
// 主机组信息查询接口
export const getHostGroupList = async (params) => {
  return await formPromise(ajax.get(`${prefix}/sda/basic_data/host_groups`, {
    params
  }))
}

// 节点信息查询接口
export const getNodeList = async (params) => {
  return await formPromise(ajax.get(`${prefix}/sda/basic_data/nodes`, {
    params
  }))
}

// 获取区域数据（大区、省份、城市）
export const getAreaList = async () => {
  const res = await formPromise(
    ajax.get(`${prefix}/sda/axe_area`)
  );
  let data = (res && res.data) || []
  return data;
};
