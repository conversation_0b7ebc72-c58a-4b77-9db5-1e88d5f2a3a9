import EmptyLayout from "@/package/src/components/EmptyLayout";
export default [
  {
    path: '/',
    redirect: { name: 'diskAlarmList' }
  },
  {
    path:'/diskAlarmConfig',
    component: EmptyLayout,
    name: "diskAlarmConfig",
    meta: {
      title: "磁盘告警配置",
    },
    children: [
      {
        path: 'diskAlarmList',
        name: 'disk<PERSON>larm<PERSON>ist',
        component: resolve => require(['@/views/disk-alarm-config/disk-alarm-list/index.vue'], resolve),
        meta: {
          title: '磁盘告警配置',
          home: true,
          keepAlive: true
        },
        hidden: false
      },
      {
        path: 'diskAlarmRecord',
        name: 'diskAlarmRecord',
        component: resolve => require(['@/views/disk-alarm-config/disk-alarm-record/index.vue'], resolve),
        meta: {
          title: '磁盘告警记录',
          home: true,
          keepAlive: true
        },
        hidden: false
      },
      {
        path: 'diskAbnormalDetail',
        name: 'diskAbnormalDetail',
        component: resolve => require(['@/views/disk-alarm-config/disk-abnormal-detail/index.vue'], resolve),
        meta: {
          title: '磁盘异常详情',
          home: true,
          keepAlive: true
        },
        hidden: false
      },
      {
        path: 'domainLimitConfig',
        name: 'domainLimitConfig',
        component: resolve => require(['@/views/disk-alarm-config/domain-limit-config/index.vue'], resolve),
        meta: {
          title: '域名限制配置',
          home: true,
          keepAlive: true
        },
        hidden: false
      },
      {
        path: 'businessAbnormalRecord',
        name: 'businessAbnormalRecord',
        component: resolve => require(['@/views/disk-alarm-config/business-abnormal-record/index.vue'], resolve),
        meta: {
          title: '业务异常记录',
          home: true,
          keepAlive: true
        },
        hidden: false
      },
    ]
  }
]