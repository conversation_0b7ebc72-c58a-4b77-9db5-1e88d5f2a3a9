@import "../var.scss";

$step-line-spacing: 8px;
$step-icon-size: 28px;
$line-weight: 2px;
$line-offset: ($step-icon-size - $line-weight) / 2;

$margin-left: $step-icon-size + $step-line-spacing;
$margin-right: calc(100% - #{$margin-left + $step-line-spacing});

.el-step.is-horizontal {
  .el-step__line {
    margin-left: $margin-left;
    width: $margin-right;
    height: $line-weight;
    top: $line-offset;
  }
}

.el-step.is-vertical {
  .el-step__line {
    margin-top: $margin-left;
    height: $margin-right;
    width: $line-weight;
    left: $line-offset;
  }
}

.el-step {
  .el-step__head {
    .el-step__icon {
      width: $step-icon-size;
      height: $step-icon-size;

      color: $font-color-primary2;
      border: 1px solid $border-color;
    }

    &.is-finish {
      .el-step__line {
        background-color: $color-primary;
      }

      .el-step__icon {
        color: $color-primary;
        border-color: $color-primary;
      }

      &+.el-step__main {
        .el-step__title {
          color: $color-primary;
        }
      }
    }

    &.is-process {
      .el-step__icon {
        color: $font-color-white;
        background-color: $color-primary;
        border-color: $color-primary;
      }

      &+.el-step__main {
        .el-step__title {
          color: $font-color-primary;
        }
      }
    }
  }

  .el-step__main {
    .el-step__description {
      &.is-process, &.is-finish, &.is-wait {
        color: $font-color-secondary
      }
    }

    .el-step__title {
      font-size: $font-size-larger !important;
      color: $font-color-primary2;
    }

    &.is-finish {
      .el-step__main {
        .el-step__title {
          color: $color-primary;
        }
      }
    }

    &.is-process {
      .el-step__main {
        .el-step__title {
          color: $font-color-primary;
        }
      }
    }
  }
}
