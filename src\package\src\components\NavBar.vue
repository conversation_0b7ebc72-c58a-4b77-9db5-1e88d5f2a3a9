<!--
 * @Description: 菜单栏组件-通过路由（hidden、meta、children）生成菜单
 * @Author: l<PERSON><PERSON><PERSON>@chinatelecom.cn
 * @Date: 2020-06-06 11:10:00
 * @LastEditTime: 2020-06-06 11:10:00
 * @LastEditors: liz<PERSON><PERSON>@chinatelecom.cn
 * @Usage：<nav-bar><nav-bar>
 -->
<template>
  <div :class="['menu', { menu__collapse: isCollapse }]">
    <div class="menu--bg"></div>
    <div class="menu--body">
      <!-- 头部标题 -->
      <header class="iconfont iconyunwei">{{ isCollapse ? "" : title }}</header>
      <!-- 菜单 -->
      <transition name="fade">
        <el-menu
          unique-opened
          router
          :default-active="activeIndex"
          :collapse="isCollapse"
        >
          <nav-bar-item
            v-for="(menu1, index1) in menuList"
            :key="index1"
            :menu="menu1"
            :route="menu1.path"
            :level="1"
            :isCollapse="isCollapse"
          >
            <nav-bar-item
              v-for="(menu2, index2) in menu1.children"
              :key="index2"
              :menu="menu2"
              :route="`${menu1.path}/${menu2.path}`"
              :level="2"
              :isCollapse="isCollapse"
            >
              <nav-bar-item
                v-for="(menu3, index3) in menu2.children"
                :key="index3"
                :menu="menu3"
                :route="`${menu1.path}/${menu2.path}/${menu3.path}`"
                :level="3"
                :isCollapse="isCollapse"
              ></nav-bar-item>
            </nav-bar-item>
          </nav-bar-item>
        </el-menu>
      </transition>
      <!-- 底部折叠/展开按钮 -->
      <footer>
        <span
          :class="[
            'iconfont',
            { iconzhankai: isCollapse },
            { iconshouqi: !isCollapse },
          ]"
          @click="isCollapse = !isCollapse"
        ></span>
      </footer>
    </div>
  </div>
</template>

<script>
import services from '@/api/services'

export default {
  name: "NavBar",
  data() {
    return {
      title: "调度数据分析平台",
      activeIndex: "",
      isCollapse: false,
      sortMenuList: [
        // 控制菜单顺序
        "资源冗余",
        "调度数据配置",
        "磁盘告警配置",
        "解析组配置",
        "Lake列表",
        "业务异常切换",
        "监控告警阈值配置",
        "解析组系数",
        "资源能力配置",
        "带宽数据源配置",
        "主机标签规划",
        "认证信息",
        "LVS上报信息",
        "小带宽设备验收",
        "小带宽故障资源列表",
      ],
    };
  },
  computed: {
    menuList() {
      let list = [];
      this.sortMenuList.map((item) => {
        list = list.concat(
          this.$router.options.routes.filter((route) => {
            if (route.meta && route.meta.title) {
              return route.meta.title === item;
            }
          })
        );
      });
      console.log("menu", list);
      return list;
    },
  },
  watch: {
    $route: {
      deep: true,
      handler() {
        this.activeIndex = this.$route.path;
      },
    },
  },
  async created() {
    try {
      let data
      if (process.env.NODE_ENV != 'development') {
        data = await services.getAuth()
      } else {
        data = { data: { privileges: '' } }
        let privileges = localStorage.getItem('privileges')
        data.data.privileges = privileges
      }
      console.log('当前用户权限:', data.data.privileges)
      if (data.data.privileges) {
        localStorage.setItem('privileges', data.data.privileges)
      }
    } catch (data) {
      console.log('privileges 接口调用出错：', data.message)
    }
  },
};
</script>

<style lang="scss" scoped>
@import "../assets/styles/var.scss";
@import "../assets/styles/mixins.scss";
$menu-width: 200px;
$menu-min-height: 720px;
$menu-header-height: 60px;
$menu-footer-height: 60px;
$menu-width__collapse: 58px;
$transition-time: 0.3s;

@mixin nav-width($width, $time: $transition-time) {
  width: $width;
  transition: width $time;
  -moz-transition: width $time; /* Firefox 4 */
  -webkit-transition: width $time; /* Safari 和 Chrome */
  -o-transition: width $time; /* Opera */
}

.menu,
.menu .menu--body,
.menu header,
.menu footer {
  @include nav-width($menu-width);
}

.menu {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background-color: $bg-color-nav1;
  font-family: PingFangSC-Regular;
  &--bg {
    @include nav-width($menu-width);
    width: calc(200px); // 50px是IAM的宽度
    background-color: $bg-color-nav1;
    position: fixed;
    /*top: 50px;*/
    top: 0;
    left: 0;
    bottom: 0;
    z-index: 1;
  }

  &--body {
    width: $menu-width;
    z-index: 10;
    overflow: auto;
    margin-bottom: 60px;

    header {
      @include center;
      height: $menu-header-height;
      color: $font-color-important;
      font-size: $font-size-larger;
      background-color: $bg-color-nav3;

      &:before {
        font-size: 22px;
        position: relative; // 不用margin是为了右边的文字位置不动
        right: 14px;
      }
    }

    .el-menu {
      background-color: $bg-color-nav1;
      border-right: none;
      margin-bottom: 50px;
      :global(.el-submenu__title) {
        color: #d6d6d8;
        &:hover {
          background-color: $bg-color-nav__hover;
        }
      }
    }

    footer {
      @include center;
      position: fixed;
      background-color: $bg-color-nav1;
      height: 50px;
      bottom: 0;
      border-top: 1px solid #6d717c;
      color: $font-color-white;
      z-index: 11;

      span {
        cursor: pointer;
        &:before {
          font-size: 16px;
        }
      }
    }
  }

  &__collapse,
  &__collapse .menu--body,
  &__collapse .el-menu,
  &__collapse header,
  &__collapse footer {
    @include nav-width($menu-width__collapse);
  }

  &__collapse .menu--bg {
    @include nav-width(58px);
    // @include nav-width(calc(50px + 58px)); // 50px是IAM的宽度
  }
  &__collapse header {
    &:before {
      right: 0;
    }
  }
  &__collapse .el-menu {
    :global(.el-submenu__icon-arrow) {
      display: none;
    }
  }
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity $transition-time;
}
.fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */ {
  opacity: 0;
}
</style>
