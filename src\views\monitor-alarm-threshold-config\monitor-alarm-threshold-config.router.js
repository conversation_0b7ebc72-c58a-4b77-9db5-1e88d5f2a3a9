import EmptyLayout from "@/package/src/components/EmptyLayout";
export default [
  {
    path: '/',
    redirect: { name: 'coverAdjust' }
  },
  {
    path:'/monitorAlarmThresholdConfig',
    component: EmptyLayout,
    name: "monitorAlarmThresholdConfig",
    meta: {
      title: "监控告警阈值配置",
    },
    children: [
      {
        path: 'monitorAlarmThresholdConfigList',
        name: 'monitorAlarmThresholdConfigList',
        component: resolve => require(['@/views/monitor-alarm-threshold-config/list/index.vue'], resolve),
        meta: {
          title: '监控告警阈值配置',
          home: true,
          keepAlive: true
        },
        hidden: false
      },
    ]
  }
]