import EmptyLayout from "@/package/src/components/EmptyLayout";
export default [
  {
    path: '/',
    redirect: { name: 'special<PERSON><PERSON>' }
  },
  {
    path:'/scheduleDataConfig',
    component: EmptyLayout,
    name: "scheduleDataConfig",
    meta: {
      title: "调度数据配置",
    },
    children: [
      {
        path: 'specialArea',
        name: 'specialA<PERSON>',
        component: resolve => require(['@/views/schedule-data-config/special-area/index.vue'], resolve),
        meta: {
          title: '特殊区域带宽配置',
          home: true,
          keepAlive: true
        },
        hidden: false
      },
      {
        path: 'mixBandwidth',
        name: 'mixBandwidth',
        component: resolve => require(['@/views/schedule-data-config/mix-bandwidth/index.vue'], resolve),
        meta: {
          title: '融合带宽灰度',
          home: true,
          keepAlive: true
        },
        hidden: false
      },
    ]
  }
]