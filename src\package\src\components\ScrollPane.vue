<template>
  <div class="scroll" ref="scroll" @wheel.prevent="handleScroll">
    <div class="scroll--wrapper" ref="scrollWrapper" :style="{ left: left + 'px' }">
      <slot></slot>
    </div>
  </div>
</template>

<script>

export default {
  name: 'scrollpane',
  props: {
    tagNum: {
      required: false
    }
  },
  data() {
    return {
      left: 0,
      offset: 0
    }
  },
  watch: {
    left () {
      this.left > 0 && (this.left = 0)
      this.offset < 0 && this.left < this.offset && (this.left = this.offset)
    },
    'tagNum' () { // 标签数量变化时
      let scrollWidth = this.$refs.scroll.offsetWidth // 外层宽度
      let scrollWrapperWidth = this.$refs.scrollWrapper.offsetWidth // 内层宽阔
      let offset = scrollWidth - scrollWrapperWidth
      if (offset < 0) { // 需要滚动
        this.left = offset
      } else { // 无需滚动
        this.left = 0
      }
    }
  },
  methods: {
    handleScroll(e) {
      let direction = e.wheelDelta || -e.deltaY // Windows：正direction-鼠标滑轮向上（页面向上或左滚
      let scrollWidth = this.$refs.scroll.offsetWidth // 外层宽度
      let scrollWrapperWidth = this.$refs.scrollWrapper.offsetWidth // 内层宽阔
      let offset = scrollWidth - scrollWrapperWidth
      this.offset = offset
      if (offset >= 0) return

      this.left += direction > 0 ? 50 : -50
    }
  }
}
</script>

<style lang="scss" scoped>
.scroll {
  width: 100%;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
  &--wrapper {
    position: absolute;
  }
}
</style>
