import Vue from 'vue'
import Router from 'vue-router'
Vue.use(Router)

let routes = []
let routesContext = require.context('@/views/', true, /\.router\.js$/)
routesContext.keys().forEach(item => {
  // 兼容 import export 和 require module.export 两种规范
  let route = routesContext(item).default || routesContext(item)
  routes = routes.concat(route)
})

const originalRouterPushFn = Router.prototype.push
Router.prototype.push = function (...args) {
  return originalRouterPushFn.call(this, ...args)?.catch(err => {
    if (err.name !== 'NavigationDuplicated') {
      throw err
    }
  })
}

// routes.push({
//   path: '*',
//   redirect: '/conf-list'
// })

export default new Router({ routes })
