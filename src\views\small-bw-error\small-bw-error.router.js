import EmptyLayout from "@/package/src/components/EmptyLayout";
export default [
  {
    path: '/',
    redirect: { name: 'smallBwError' }
  },
  {
    path:'/smallBwError',
    component: EmptyLayout,
    name: "smallBwError",
    meta: {
      title: "小带宽故障资源列表",
    },
    children: [
      {
        path: 'smallBwError',
        name: 'smallBwError',
        component: resolve => require(['@/views/small-bw-error/bw-error/index.vue'], resolve),
        meta: {
          title: '小带宽故障资源列表',
          home: true,
          keepAlive: true
        },
        hidden: false
      },
    ]
  }
]
