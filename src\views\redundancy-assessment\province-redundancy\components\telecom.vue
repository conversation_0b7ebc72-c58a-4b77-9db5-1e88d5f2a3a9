<template>
  <el-card>
    <ct-table
      :exportPartInfo="exportPartInfo"
      :exportAllName="exportAllName"
      :tableData="telecomTableData"
    ></ct-table>

  </el-card>
</template>
<script>

import listMixin from "@/utils/list-pager";
import provinceMixin from "@/views/redundancy-assessment/mixins/province.mixin";
import ctTable from '@/views/redundancy-assessment/province-redundancy/components/ctTable.vue'

export default {
  name: "telecom",
  mixins: [
    listMixin,
    provinceMixin
  ],
  components: {
    ctTable
  },
  props: [],
  data() {
    let excelId = 'telecom'
    let excelName = '中国电信'
    return {
      form: {},
      telecomTableData: [], // 表格数据
      exportPartInfo: {
        excelId: excelId,
        excelName: excelName
      },
      exportAllName: '中国电信',
    };
  },
  computed: {
  },
  filters: {},
  created() {
    this.$eventBus.$on("telecomTableData", (data) => {
			this.telecomTableData = data
		})
  },
  beforeDestroy() {
    this.$eventBus.$off('telecomTableData');
  },
  watch: {},
  methods: {},
};
</script>
<style scoped lang="scss">
</style>
