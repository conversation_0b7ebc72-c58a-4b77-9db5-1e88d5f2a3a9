import * as date from '@/utils/timeMoment'
 
export default {
  data: () => {
    return {}
  },
  methods: {
    dateFormatInTable: date.formatInTable('YYYY-MM-DD HH:mm:ss', 1000),
    dateFormatHHmmInTable: date.formatInTable('YYYY-MM-DD HH:mm', 1000),
    dateFormatUTCInTable: date.formatUTCInTable('YYYY-MM-DD HH:mm:ss', 1000),
    parseBwByteString(bwString) {
      let flowVlueBytes = parseInt(bwString)
      // 如果小于1Kb.则显示为bit
      if (flowVlueBytes === 0 || isNaN(flowVlueBytes)) {
        return '0'
      } else if (flowVlueBytes < 1000) {
        return Math.round(flowVlueBytes) + ' b'
      } else if (flowVlueBytes / 1000 < 1000) {
        return Math.round(flowVlueBytes / 1000) + ' K'
      } else if (flowVlueBytes / 1000 / 1000 < 1000) {
        return Math.round(flowVlueBytes / 1000 / 1000) + ' M'
      } else if ((flowVlueBytes / 1000 / 1000 / 1000 < 1000)) {
        return (flowVlueBytes / 1000 / 1000 / 1000).toFixed(1) + ' G'
      } else {
        return (flowVlueBytes / 1000 / 1000 / 1000 / 1000).toFixed(1) + ' T'
      }
    },
    parseBwByteToM(bwString) {
      let flowVlueBytes = parseInt(bwString)
      if (flowVlueBytes === 0 || isNaN(flowVlueBytes)) {
        return '0'
      } else {
        return Math.floor(flowVlueBytes / 1000 / 1000)
      }
    },
  },
  filters: {}
}
 