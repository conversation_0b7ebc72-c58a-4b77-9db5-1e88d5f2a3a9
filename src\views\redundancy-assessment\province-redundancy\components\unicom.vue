<template>
  <el-card>
    <ct-table
      :exportPartInfo="exportPartInfo"
      :exportAllName="exportAllName"
      :tableData="unicomTableData"
    ></ct-table>

  </el-card>
</template>
<script>

import listMixin from "@/utils/list-pager";
import provinceMixin from "@/views/redundancy-assessment/mixins/province.mixin";
import ctTable from '@/views/redundancy-assessment/province-redundancy/components/ctTable.vue'

export default {
  name: "unicom",
  mixins: [
    listMixin,
    provinceMixin
  ],
  components: {
    ctTable
  },
  props: [],
  data() {
    let excelId = 'unicom'
    let excelName = '中国联通'
    return {
      form: {},
      unicomTableData: [], // 表格数据
      exportPartInfo: {
        excelId: excelId,
        excelName: excelName
      },
      exportAllName: '中国联通',
    };
  },
  computed: {
  },
  filters: {},
  created() {
    this.$eventBus.$on("unicomTableData", (data) => {
			this.unicomTableData = data
		})
  },
  beforeDestroy() {
    this.$eventBus.$off('unicomTableData');
  },
  watch: {},
  methods: {},
};
</script>
<style scoped lang="scss">
</style>
