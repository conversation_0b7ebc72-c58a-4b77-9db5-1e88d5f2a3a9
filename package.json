{"name": "sda-ui", "version": "1.0.0", "private": true, "description": "", "author": "", "scripts": {"dev": "webpack-dev-server --inline --progress --config build/webpack.dev.conf.js", "build": "node build/build.js", "test:unit": "vue-cli-service test:unit"}, "dependencies": {"@babel/runtime-corejs3": "^7.23.2", "animate": "^1.0.0", "animate.css": "^3.7.0", "axios": "^0.18.0", "babel-helper-vue-jsx-merge-props": "^2.0.3", "copy-to-clipboard": "^3.3.3", "crypto-js": "^4.2.0", "css-loader": "^0.28.11", "dayjs": "^1.11.10", "dompurify": "^3.1.6", "element-ui": "^2.15.6", "file-saver": "^2.0.5", "lite-virtual-list": "^0.1.7", "lodash-es": "^4.17.21", "moment": "^2.30.1", "spark-md5": "^3.0.2", "url-search-params-polyfill": "^8.1.0", "vue": "^2.6.10", "vue-infinite-scroll": "^2.0.2", "vue-router": "^3.1.6", "vuex": "^3.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.0.0", "@babel/plugin-proposal-class-properties": "^7.0.0", "@babel/plugin-proposal-decorators": "^7.0.0", "@babel/plugin-proposal-export-namespace-from": "^7.0.0", "@babel/plugin-proposal-function-sent": "^7.0.0", "@babel/plugin-proposal-json-strings": "^7.0.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6", "@babel/plugin-proposal-numeric-separator": "^7.0.0", "@babel/plugin-proposal-object-rest-spread": "^7.0.0", "@babel/plugin-proposal-optional-chaining": "7.18.9", "@babel/plugin-proposal-throw-expressions": "^7.0.0", "@babel/plugin-syntax-dynamic-import": "^7.0.0", "@babel/plugin-syntax-import-meta": "^7.0.0", "@babel/plugin-syntax-jsx": "^7.0.0", "@babel/plugin-transform-runtime": "^7.24.7", "@babel/preset-env": "^7.24.8", "@babel/register": "^7.0.0", "@typescript-eslint/eslint-plugin": "^2.13.0", "@typescript-eslint/parser": "^2.13.0", "@vue/cli-plugin-unit-jest": "^5.0.8", "@vue/test-utils": "^1.1.3", "@vue/vue2-jest": "^27.0.0-alpha.2", "animate": "^1.0.0", "autoprefixer": "^7.1.2", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-jest": "^27.0.6", "babel-loader": "^8.0.0", "babel-plugin-istanbul": "^4.1.1", "babel-plugin-transform-vue-jsx": "^3.7.0", "compression-webpack-plugin": "^1.1.12", "copy-webpack-plugin": "^4.6.0", "cross-env": "^5.0.1", "cross-spawn": "^5.0.1", "eslint": "^6.8.0", "eslint-plugin-vue": "^6.1.1", "extract-text-webpack-plugin": "^3.0.0", "file-loader": "^1.1.4", "friendly-errors-webpack-plugin": "^1.6.1", "generate-asset-webpack-plugin": "^0.3.0", "html-webpack-plugin": "^2.30.1", "inject-loader": "^3.0.0", "jest": "^27.0.5", "jquery": "^3.3.1", "js-cookie": "^2.2.0", "node-notifier": "^5.4.0", "node-sass": "^4.13.1", "optimize-css-assets-webpack-plugin": "^3.2.0", "ora": "^1.2.0", "portfinder": "^1.0.20", "postcss-import": "^11.0.0", "postcss-loader": "^2.0.8", "postcss-url": "^7.2.1", "qs": "^6.7.0", "rimraf": "^2.6.3", "sass-loader": "^7.1.0", "script-loader": "^0.7.2", "selenium-server": "^3.141.59", "semver": "^5.7.0", "shelljs": "^0.7.6", "uglifyjs-webpack-plugin": "^1.1.1", "url-loader": "^0.5.8", "vue-codemirror": "^4.0.6", "vue-element-extends": "^1.2.19", "vue-json-viewer": "^2.2.16", "vue-loader": "^13.3.0", "vue-style-loader": "^3.0.1", "vue-template-compiler": "^2.6.10", "webpack": "^3.6.0", "webpack-bundle-analyzer": "^2.9.0", "webpack-dev-server": "^2.11.5", "webpack-merge": "^4.2.1"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "volta": {"node": "12.22.12"}}