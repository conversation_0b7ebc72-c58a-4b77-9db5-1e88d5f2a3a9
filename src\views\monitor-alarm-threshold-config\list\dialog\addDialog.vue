<template>
  <el-dialog
    append-to-body
    :title="title"
    :visible="true"
    :close-on-click-modal="false"
    width="1000px"
    @close="handleCancel"
  >
    <el-row>
      <el-form :inline="true" inline-message ref="dialogForm" :model="dialogForm" :rules="rules" label-width="300px" label-position="left" class="form-wrapper" :disabled="isView">
        <el-row>
          <el-col :span="12">
            <el-form-item label="域名" prop="domain" :inline-message="false" label-width="60px">
              <el-select
                v-model="dialogForm.domain"
                placeholder="待配置域名"
                clearable
                filterable
                remote
                :remote-method="getDomainList"
                :loading="domainLoading"
                style="width:300px"
                :disabled="isEdit"
              >
                <el-option v-for="(item, index) in domainList" :key="index" :label="item.domain" :value="item.domain">
                  <span style="float:left; text-overflow: ellipsis;overflow: hidden;">{{ item.domain }}</span>
                  <span style="float:right; font-size: 12px;color: #b4b4b4;">&emsp;{{item.account_id }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="持续异常时间" prop="persistentAbnormal" label-width="100px" :inline-message="false">
              <el-input v-model.number="dialogForm.persistentAbnormal" placeholder="" style="width:200px" clearable :disabled="isEdit"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row class="flex-wrapper mt16">
          <el-col :span="4">
            <el-form-item label="首帧时间"></el-form-item>
          </el-col>
          <el-col :span="20" class="bg-wrapper">
            <div>
              <el-form-item label="首帧记录数（ms）" prop="firstFrameCount">
                <el-input v-model.number="dialogForm.firstFrameCount" placeholder="首帧记录数（ms）" class="input-select-wrapper" clearable></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="首帧时长阈值（ms）" prop="firstFrameTimeCostThre">
                <el-input v-model.number="dialogForm.firstFrameTimeCostThre" placeholder="首帧时长阈值（ms）" class="input-select-wrapper" clearable></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="首帧突增比例阈值（%）" prop="firstFrameIncreasePercentThre">
                <el-input v-model.number="dialogForm.firstFrameIncreasePercentThre" placeholder="首帧突增比例阈值（%）" class="input-select-wrapper" clearable></el-input>
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <el-row class="flex-wrapper">
          <el-col :span="4">
            <el-form-item label-width="120px" label="渲染百秒卡顿时长"></el-form-item>
          </el-col>
          <el-col :span="20" class="bg-wrapper">
            <div>
              <el-form-item label="渲染百秒卡顿时长记录数" prop="videoRenderLagTimeCostCount">
                <el-input v-model.number="dialogForm.videoRenderLagTimeCostCount" placeholder="渲染百秒卡顿时长记录数（ms）" class="input-select-wrapper" clearable></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="渲染百秒卡顿时长阈值（ms）" prop="videoRenderLagTimeCostThre">
                <el-input v-model.number="dialogForm.videoRenderLagTimeCostThre" placeholder="渲染百秒卡顿时长阈值（ms）" class="input-select-wrapper" clearable></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="渲染百秒卡顿时长突增比例阈值（%）" prop="videoRenderLagTimeCostIncreasePercentThre">
                <el-input v-model.number="dialogForm.videoRenderLagTimeCostIncreasePercentThre" placeholder="渲染百秒卡顿时长突增比例阈值（%）" class="input-select-wrapper" clearable></el-input>
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <el-row class="flex-wrapper">
          <el-col :span="4">
            <el-form-item label-width="120px" label="渲染百秒卡顿次数"></el-form-item>
          </el-col>
          <el-col :span="20" class="bg-wrapper">
            <div>
              <el-form-item label="渲染百秒卡顿次数记录数" prop="videoRenderLagCount">
                <el-input v-model.number="dialogForm.videoRenderLagCount" placeholder="渲染百秒卡顿次数记录数（ms）" class="input-select-wrapper" clearable></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="渲染百秒卡顿次数阈值" prop="videoRenderLagCountThre">
                <el-input v-model.number="dialogForm.videoRenderLagCountThre" placeholder="渲染百秒卡顿次数阈值" class="input-select-wrapper" clearable></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="渲染百秒卡顿次数突增比例阈值（%）" prop="videoRenderLagCountIncreasePercentThre">
                <el-input v-model.number="dialogForm.videoRenderLagCountIncreasePercentThre" placeholder="渲染百秒卡顿次数突增比例阈值（%）" class="input-select-wrapper" clearable></el-input>
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <el-row class="flex-wrapper">
          <el-col :span="4">
            <el-form-item label-width="120px" label="review拉流成功率"></el-form-item>
          </el-col>
          <el-col :span="20" class="bg-wrapper">
            <div>
              <el-form-item label="review拉流记录数" prop="newSumPlayCount">
                <el-input v-model.number="dialogForm.newSumPlayCount" placeholder="review拉流记录数" class="input-select-wrapper" clearable></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="review拉流成功率阈值（ms）" prop="newSumPlaySuccessRateThre">
                <el-input v-model.number="dialogForm.newSumPlaySuccessRateThre" placeholder="review拉流成功率阈值（ms）" class="input-select-wrapper" clearable></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="review拉流成功率突降比例阈值（%）" prop="newSumPlaySuccessRateDecreasePercentThre">
                <el-input v-model.number="dialogForm.newSumPlaySuccessRateDecreasePercentThre" placeholder="review拉流成功率突降比例阈值（%）" class="input-select-wrapper" clearable></el-input>
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <el-row class="flex-wrapper">
          <el-col :span="4">
            <el-form-item label-width="120px" label="命中首包时间"></el-form-item>
          </el-col>
          <el-col :span="20" class="bg-wrapper">
            <div>
              <el-form-item label="首包记录数" prop="firstPackageCount">
                <el-input v-model.number="dialogForm.firstPackageCount" placeholder="首包记录数" class="input-select-wrapper" clearable></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="首包时间阈值（ms）" prop="firstPackageTimeCostThre">
                <el-input v-model.number="dialogForm.firstPackageTimeCostThre" placeholder="首包时间阈值（ms）" class="input-select-wrapper" clearable></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="首包时间突增比例阈值（%）" prop="firstPackageIncreasePercentThre">
                <el-input v-model.number="dialogForm.firstPackageIncreasePercentThre" placeholder="首包时间突增比例阈值（%）" class="input-select-wrapper" clearable></el-input>
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <el-row class="flex-wrapper">
          <el-col :span="4">
            <el-form-item label-width="120px" label="指定省份运营商收到302请求数比例"></el-form-item>
          </el-col>
          <el-col :span="20" class="bg-wrapper">
            <div>
              <el-form-item label="响应的302请求数" prop="response302QueryCount">
                <el-input v-model.number="dialogForm.response302QueryCount" placeholder="响应的302请求数" class="input-select-wrapper" clearable></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="指定省份运营商收到302请求数比例阈值（%）" prop="ispProvince302QueryPercentThre">
                <el-input v-model.number="dialogForm.ispProvince302QueryPercentThre" placeholder="指定省份运营商收到302请求数比例阈值（%）" class="input-select-wrapper" clearable></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="指定省份运营商收到302请求数比例突降阈值（%）" prop="ispProvince302QueryDecreasePercentThre">
                <el-input v-model.number="dialogForm.ispProvince302QueryDecreasePercentThre" placeholder="指定省份运营商收到302请求数比例突降阈值（%）" class="input-select-wrapper" clearable></el-input>
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        
      </el-form>
    </el-row>

    <div slot="footer" class="dialog-footer">
      <el-button size="medium" @click="handleCancel">取 消</el-button>
      <el-button v-if="!isView" size="medium" type="primary" @click="handleSubmit" :loading="submiting" :disabled="submiting || isView">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import http from '@/api/http.js'
import validateMixin from "@/views/monitor-alarm-threshold-config/mixins/validate.mixin.js";

export default {
  components: {},
  mixins: [
    validateMixin
  ],
  props: {
    isEdit: {
      type: Boolean,
      default: false
    },
    isView: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: null
    },
  },
  data() {
    return {
      submiting: false,
      domainLoading: false,
      domainList: [],
      dialogForm: {
        domain: "", // 域名
        persistentAbnormal: 3, // 持续异常时间
        firstFrameCount: 30, // 首帧记录数
        firstFrameTimeCostThre: 600, // 首帧时长阈值
        firstFrameIncreasePercentThre: 20, // 首帧突增比例阈值
        videoRenderLagTimeCostCount: 30, // 渲染百秒卡顿时长记录数
        videoRenderLagTimeCostThre: 4000, // 渲染百秒卡顿时长阈值
        videoRenderLagTimeCostIncreasePercentThre: 20, // 渲染百秒卡顿时长突增比例阈值
        videoRenderLagCount: 30, // 渲染百秒卡顿次数记录数
        videoRenderLagCountThre: 10, // 渲染百秒卡顿次数阈值
        videoRenderLagCountIncreasePercentThre: 20, // 渲染百秒卡顿次数突增比例阈值
        newSumPlayCount: 30, // review拉流记录数
        newSumPlaySuccessRateThre: 94, // review拉流成功率阈值
        newSumPlaySuccessRateDecreasePercentThre: 10, // review拉流成功率突降比例阈值
        firstPackageCount: 30, // 首包记录数
        firstPackageTimeCostThre: 20, // 首包时间阈值
        firstPackageIncreasePercentThre: 20, // 首包时间突增比例阈值
        response302QueryCount: 30, // 响应的302请求数
        ispProvince302QueryPercentThre: 80, // 指定省份运营商收到302请求数比例阈值
        ispProvince302QueryDecreasePercentThre: 20, // 指定省份运营商收到302请求数比例突降阈值
      },
      rules: {},
    }
  },
  computed: {
    title() {
      return this.isEdit ? "修改" : this.isView ? "查看" : "新增";
    },
  },
  mounted() {
    if (this.isEdit || this.isView) {
      this.handleInitData()
    }
  },
  methods: {
    async handleSubmit() {
      try {
        await new Promise((resolve, reject) => {
          this.$refs.dialogForm.validate(valid => {
            valid ? resolve() : reject('表单项有错误，请检查')
          })
        })
      } catch (err) {
        this.$message({
          showClose: true,
          message: err.message || err,
          type: 'error'
        })
        return false
      }
      this.submiting = true
      let domainObj = this.domainList.find(item => {
        return item.domain === this.dialogForm.domain
      })
      let params = {
        ...this.dialogForm,
        accid: domainObj && domainObj.account_id,
      }
      let res = {}
      let successMsg = "添加成功"
      if (this.isEdit) {
        // 修改
        res = await http.patch(`/sda/monitor/domains/thresholdConf/${this.rowData.id}`, params)
        successMsg = "修改成功"
      } else {
        // 新增
        res = await http.post(`/sda/monitor/domains/thresholdConf/create`, params)
      }
      if (res && res.code === 100000) {
        this.$message.success(successMsg);
        this.$emit("close")
        this.$emit("refresh")
        this.submiting = false
      } else {
        this.submiting = false
      }
    },
    handleCancel() {
      this.$emit("close")
      this.$emit("refresh")
    },
    handleInitData() {
      const row = structuredClone(this.rowData)
      this.dialogForm.domain = row.domain
      this.dialogForm.persistentAbnormal = row.persistentAbnormal
      this.dialogForm.firstFrameCount = row.firstFrameCount
      this.dialogForm.firstFrameTimeCostThre = row.firstFrameTimeCostThre
      this.dialogForm.firstFrameIncreasePercentThre = row.firstFrameIncreasePercentThre
      this.dialogForm.videoRenderLagTimeCostCount = row.videoRenderLagTimeCostCount
      this.dialogForm.videoRenderLagTimeCostThre = row.videoRenderLagTimeCostThre
      this.dialogForm.videoRenderLagTimeCostIncreasePercentThre = row.videoRenderLagTimeCostIncreasePercentThre
      this.dialogForm.videoRenderLagCount = row.videoRenderLagCount
      this.dialogForm.videoRenderLagCountThre = row.videoRenderLagCountThre
      this.dialogForm.videoRenderLagCountIncreasePercentThre = row.videoRenderLagCountIncreasePercentThre
      this.dialogForm.newSumPlayCount = row.newSumPlayCount
      this.dialogForm.newSumPlaySuccessRateThre = row.newSumPlaySuccessRateThre
      this.dialogForm.newSumPlaySuccessRateDecreasePercentThre = row.newSumPlaySuccessRateDecreasePercentThre
      this.dialogForm.firstPackageCount = row.firstPackageCount
      this.dialogForm.firstPackageTimeCostThre = row.firstPackageTimeCostThre
      this.dialogForm.firstPackageIncreasePercentThre = row.firstPackageIncreasePercentThre
      this.dialogForm.response302QueryCount = row.response302QueryCount
      this.dialogForm.ispProvince302QueryPercentThre = row.ispProvince302QueryPercentThre
      this.dialogForm.ispProvince302QueryDecreasePercentThre = row.ispProvince302QueryDecreasePercentThre
    },
    // 获取域名
    getDomainList(domain) {
      if (domain === '' || domain === null || domain === undefined) {
        this.domainList = []
        return
      }
      this.domainLoading = true
      http.get(`/scc/api/v3/domain/list?domain=${domain}`).then((res) => {
        this.domainList = (res && res.data && res.data.items) || []
      }).finally(() => {
        this.domainLoading = false
      })
    },
  },
}
</script>

<style scoped lang="scss">
.form-wrapper {
  .el-form-item--small.el-form-item {
    margin-bottom: 4px !important;
  }
}
.flex-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  margin: 8px 0;
}
.mt16 {
  margin-top: 16px !important;
}
.input-select-wrapper {
  width: 240px;
}
.bg-wrapper {
  border-style: solid;
  border-width: 2px;
  border-color: #f7f8fa;
  background-color: #f7f8fa;
  padding: 0.375rem 0.375rem 0.175rem 0.375rem;
}
</style>