@import "../var";
@import "../mixins";

$slider-button-size: 13px;
$slider-bar-size: 3px;

.el-slider {
  &[aria-orientation="horizontal"] {
    .el-slider__runway, .el-slider__runway .el-slider__bar {
      height: $slider-bar-size;
    }
    .el-slider__runway .el-slider__stop {
      margin-top: -1px;
    }
  }

  &[aria-orientation="vertical"] {
    .el-slider__runway, .el-slider__runway .el-slider__bar {
      width: $slider-bar-size;
    }
    .el-slider__runway .el-slider__stop {
      margin-left: -1px;
    }
  }

  .el-slider__runway {
    .el-slider__stop {
      width: $slider-bar-size;
      height: $slider-bar-size;
      border: 1px solid $bg-color-slider;
    }

    .el-slider__button-wrapper {
      width: #{36px - ($slider-bar-size - 1) * 2};
      height: #{36px - ($slider-bar-size - 1) * 2};

      .el-slider__button {
        width: $slider-button-size;
        height: $slider-button-size;

        border: none;
        background-color: $color-primary;

        &:hover {
          width: $slider-button-size;
          height: $slider-button-size;
        }
      }
    }

    .el-slider__marks-text {
      @include font-text-1;
    }
  }
}
