import {
  ajax
} from '@/api/ajax'
import {
  Message
} from 'element-ui'

export const formPromise = (form) => {
  return form.then(function (response) {
    return response.data
  }).catch((err) => {
    Message({
      showClose: true,
      message: (err && err.data) || (err && err.response && err.response.data && err.response.data.msg) || "系统异常，请联系管理员！",
      type: 'warning'
    })
  })
}

export default {
  async get(url, params) {
    let currentUrl = `/api/v1` + url
    let reData = await formPromise(ajax.get(currentUrl, { params }))
    return reData
  },
  async post(url, params) {
    let currentUrl = `/api/v1` + url
    let reData = await formPromise(ajax.post(currentUrl, params))
    return reData
  },
  async patch(url, params) {
    let currentUrl = `/api/v1` + url
    let reData = await formPromise(ajax.patch(currentUrl, params))
    return reData
  },
  async delete(url, params) {
    let currentUrl = `/api/v1` + url
    let reData = await formPromise(ajax.delete(currentUrl, { params }))
    return reData
  },
}