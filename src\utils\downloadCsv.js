import { Message } from "element-ui";
const ua = window.navigator.userAgent;
const isIe = ua.indexOf("MSIE ") > -1 || !!ua.match(/Trident.*rv\\:11\./) || ua.indexOf("Edge") > -1;
const canUseBlob = typeof Blob !== "undefined";

export function download (fileName = "表格数据", fileContent = "", fileType = "xlsx") {
  if (isIe) {
    if (canUseBlob && window.navigator.msSaveOrOpenBlob) {
      fileContent = ["\uFEFF" + fileContent];
      const blob = new Blob(fileContent, {
        type: `text/${fileType}`,
      });
      window.navigator.msSaveOrOpenBlob(blob, `${fileName}.${fileType}`);
    } else {
      Message.warning("该浏览器不支持下载");
    }
  } else {
    const link = document.createElement("a");
    if (canUseBlob) {
      link.href = URL.createObjectURL(new Blob(["\uFEFF" + fileContent]));
    } else {
      link.href = `data:text/xlsx;charset=utf-8,${fileContent}`;
    }
    link.download = `${fileName}.${fileType}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};