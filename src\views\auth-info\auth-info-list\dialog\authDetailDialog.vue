<template>
  <el-dialog
    title="详情展示"
    :visible.sync="dialogVisible"
    width="600px"
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div v-loading="loading">
      <div
        style="
          font-size: 16px;
          display: flex;
          flex-direction: column;
          gap: 10px;
          padding-left: 50px;
        "
      >
        <div v-for="(v, key) in detailData" :key="key" class="detail-item">
          <template v-if="key !== 'extra'"
            >-{{ key }}<template v-if="!v.only_key">=<template v-if="['k', 'J'].includes(key)"
              ><template v-if="!showFullInfo">********</template
              ><template v-else>{{ handlerDescrypt(v.value) }}</template></template
            ><template v-else>{{ v.value }}</template></template></template
          >
        </div>
      </div>

      <div style="margin-top: 20px; text-align: center">
        <el-button
          v-if="!showFullInfo"
          type="primary"
          @click="handleShowFullInfo"
          >显示完整信息</el-button
        >
        <el-button v-else type="success" disabled>已显示完整信息</el-button>
      </div>
    </div>

    <auth-verify-dialog
      :visible.sync="authDialogVisible"
      :auth-type="authType"
      @close="handleAuthDialogClose"
      @verify-success="handleAuthVerifySuccess"
    />
  </el-dialog>
</template>

<script>
import { decryptPassword } from "@/utils/crypto";
import http from "../../http";
import authMixin from "@/mixins/authMixin";

export default {
  name: "authDetailDialog",
  components: {},
  mixins: [authMixin],
  props: {
    rowData: {
      type: Object,
      default: () => ({}),
    },

  },
  data() {
    return {
      dialogVisible: true,
      loading: false,
      showFullInfo: false,
      detailData: {},
    };
  },
  created() {
    this.getDetailData();
  },
  methods: {
    handlerDescrypt(data) {
      return decryptPassword(data);
    },
    async getDetailData() {
      this.loading = true;
      try {
        const res = await http.getAuthInfoDetail(this.rowData.id);
        if (res && res.code === 100000) {
          this.detailData = res.data.reduce((acc, item) => {
            acc[item.key] = { value: item.value, only_key: item.only_key };
            return acc;
          }, {});
        }
      } catch (error) {
        console.error("获取详情失败:", error);
        this.$message.error("获取详情失败");
      } finally {
        this.loading = false;
      }
    },
    handleShowFullInfo() {
      // 使用 authMixin 中的方法进行认证操作
      this.executeAuthenticatedOperation('showFullInfo', this.performShowFullInfo);
    },
    performShowFullInfo() {
      this.showFullInfo = true;
    },

    formatTime(timestamp) {
      if (!timestamp) return "-";
      const date = new Date(timestamp * 1000);
      return date.toLocaleString("zh-CN");
    },
    formatConfigInfo(configInfo) {
      if (!configInfo) return "";

      // 如果未显示完整信息，则隐藏 -k 和 -J 字段
      if (!this.showFullInfo) {
        return configInfo
          .replace(/-k\s+\S+/g, "-k ******")
          .replace(/-J\s+\S+/g, "-J ******");
      }

      return configInfo;
    },
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>

<style scoped>
.el-descriptions {
  margin-bottom: 20px;
}

pre {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  font-family: "Courier New", monospace;
  font-size: 12px;
  line-height: 1.5;
}

.detail-item {
  white-space: nowrap;
}
</style>
