import EmptyLayout from "@/package/src/components/EmptyLayout";

export default [
  {
    path: '/resourceCapacityConfig',
    component: EmptyLayout,
    name: "resourceCapacityConfig",
    meta: {
      title: "资源能力配置",
    },
    children: [
      {
        path: 'lakeCapacityConfig',
        name: 'lakeCapacityConfig',
        component: resolve => require(['@/views/resource-capacity-config/lake-capacity-config/index.vue'], resolve),
        meta: {
          title: 'Lake单机能力配置',
          keepAlive: true
        },
        hidden: false
      },
      {
        path: 'hostGroupCapacityConfig',
        name: 'hostGroupCapacityConfig',
        component: resolve => require(['@/views/resource-capacity-config/host-group-capacity-config/index.vue'], resolve),
        meta: {
          title: '主机组单机能力配置',
          keepAlive: true
        },
        hidden: false
      }
    ]
  }
] 
