<template>
  <el-card>
    <div slot="header" class="clearfix">
      <span>磁盘告警记录</span>
    </div>
    <!-- 查询条件 -->
    <el-form :model="searchForm" ref="searchForm" label-position="left" inline>
      <el-row>
        <el-form-item label="LAKE名称">
          <el-select v-model="searchForm.lake_id" placeholder="请选择LAKE名称" style="width:220px" filterable clearable @change="lakeChange">
            <el-option :value="-1" label="ALL" />
            <el-option v-for="(item, index) in lakeList" :key="index" :label="item.bk_inst_name" :value="item.bk_inst_id"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="IP">
          <el-select v-model="searchForm.ip" placeholder="请选择IP" filterable clearable style="width:220px">
            <el-option v-for="(item, index) in ipList" :key="index" :label="item.ip" :value="item.ip">
              <span style="float:left; text-overflow: ellipsis;overflow: hidden;">{{ item.hostgroup_name }}</span>
              <span style="float:right; font-size: 12px;color: #b4b4b4;">&emsp;{{item.ip }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button size="medium" type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
        <span style="float:right">
          <el-button size="medium" @click="handleRecover">手动恢复</el-button>
        </span>
      </el-row>
    </el-form>

    <el-table ref="multipleTable" :data="tableData" v-loading="querying" @selection-change="handleSelectionChange">
      <el-table-column type="selection" align="center" width="55"></el-table-column>
      <el-table-column prop="lake_name" label="LAKE" align="center"></el-table-column>
      <el-table-column prop="ip" label="IP" align="center"></el-table-column>
      <el-table-column prop="state" label="状态" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.state === 0" type="success">恢复</el-tag>
          <el-tag v-if="scope.row.state === 1" type="danger">告警</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="frequent_switch_times" label="频繁切换次数" align="center"></el-table-column>
      <el-table-column prop="create_time" label="创建时间" align="center">
        <template slot-scope='scope'>
          <span>{{ (scope.row.create_time * 1000) | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="update_time" label="更新时间" align="center">
        <template slot-scope='scope'>
          <span>{{ (scope.row.update_time * 1000) | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="180">
        <template slot-scope="scope">
          <div>
            <el-button type="text" @click="handleAbnormalRecord(scope.row)">磁盘异常记录</el-button>
          </div>
          <div>
            <el-button type="text" @click="handleBizAbnormalRecord(scope.row)">业务异常记录</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-row style="text-align: center; padding-top: 20px">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.page_size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      ></el-pagination>
    </el-row>

    <el-row>
      <!-- 磁盘异常记录 -->
      <disk-abnormal-detail-dialog
        v-if="dialogVisible"
        :rowData="rowData"
        @close="dialogVisible = false"
        @refresh="onSearch"
      ></disk-abnormal-detail-dialog>
      <!-- 业务异常记录 -->
      <biz-abnormal-detail-dialog
        v-if="bizDialogVisible"
        :rowData="bizRowData"
        @close="bizDialogVisible = false"
        @refresh="onSearch"
      ></biz-abnormal-detail-dialog>
    </el-row>

  </el-card>
</template>
<script>

import http from "@/api/http.js"
import { mapState } from "vuex";
import dateFormat from "@/utils/dateFormat"
import diskAbnormalDetailDialog from "@/views/disk-alarm-config/disk-alarm-record/dialog/diskAbnormalDetailDialog.vue"
import bizAbnormalDetailDialog from "@/views/disk-alarm-config/disk-alarm-record/dialog/bizAbnormalDetailDialog.vue"

export default {
  name: "disk-alarm-record",
  components: {
    diskAbnormalDetailDialog,
    bizAbnormalDetailDialog
  },
  filters: {
    dateFormat: dateFormat
  },
  props: [],
  data() {
    return {
      searchForm: {
        lake_id: '',
        ip: ''
      },
      querying: false,
      tableData: [],
      ipList: [],
      multipleSelection: [],
      dialogVisible: false,
      rowData: {},
      bizDialogVisible: false,
      bizRowData: {},
      pagination: {
        total: 0,
        page: 1,
        page_size: 20,
      },
    };
  },
  computed: {
    ...mapState({
      lakeList: (state) => state.baseData.lakeList,
      ipListArr: (state) => state.baseData.ipList,
    }),
  },
  watch: {
    ipListArr: {
      deep: true,
      handler(val) {
        this.ipList = structuredClone(val)
      },
      immediate: true
    }
  },
  created() {},
  mounted() {
    this.onSearch()
  },
  methods: {
    async lakeChange(lake_id) {
      let params = {
        lake_id: lake_id,
        lake_type: 4,
      };
      try {
        await http.get(`/sda/basic_data/hosts`, params).then((res) => {
          this.ipList = res && res.data
        });
      } catch (error) {
        let message = error
        this.$message.error(message)
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    // 手动恢复
    async handleRecover() {
      if (!(this.multipleSelection && this.multipleSelection.length > 0)) {
        this.$message.warning("请先选择数据！")
        return;
      }
      const length = this.multipleSelection && this.multipleSelection.length
      await this.$confirm(`已选中${length}条数据，确定手动恢复该条数据吗？`, "提示", {
        type: "warning",
      })
      const ids = this.multipleSelection && this.multipleSelection.map(item => item.id)
      let params = {
        ids: ids,
        operator: window.localStorage.getItem('userInfo'),
      };
      const res = await http.post(`/sda/disks/dispatcher/records/recover`, params)
      if (res && res.code === 100000) {
        let msg = (res && res.message) || "手动恢复成功"
        this.$message.success(msg);
        this.onSearch()
      } else {
        let message = (res && res.message) || "手动恢复失败"
        this.$message.error(message);
      }
    },
    // 点击：磁盘异常记录，展开弹窗：磁盘异常记录
    async handleAbnormalRecord(row) {
      this.dialogVisible = true
      this.rowData = JSON.parse(JSON.stringify(row))
    },
    // 点击：业务异常记录
    async handleBizAbnormalRecord(row) {
      this.bizDialogVisible = true
      this.bizRowData = JSON.parse(JSON.stringify(row))
    },
    onSearch() {
      this.pagination.page = 1;
      this.query();
    },
    async query() {
      let params = {
        lake_id: this.searchForm.lake_id,
        ip: this.searchForm.ip,
        page: this.pagination.page,
        page_size: this.pagination.page_size,
      };
      this.querying = true;
      try {
        await http.get(`/sda/disks/dispatcher/records`, params).then((res) => {
          this.tableData = res && res.data && res.data.items;
          this.pagination.total = res && res.data && res.data.total;
          this.querying = false;
        });
      } catch (error) {
        this.querying = false;
      } finally {
        this.querying = false;
      }
    },
    handleSizeChange(val) {
      this.pagination.page = 1;
      this.pagination.page_size = val;
      this.query();
    },
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.query();
    },
  },
};
</script>
<style scoped lang="scss">
.btn {
  float: right;
}
</style>
