<template>
  <el-card>
    <!-- 待审任务 -->
    <!-- 查询条件 -->
    <el-form :model="searchForm" ref="searchForm" label-position="left" inline>
      <el-row>
        <el-form-item label="" prop="pg_id">
          <el-select v-model="searchForm.pg_id" placeholder="请选择解析组名称" filterable clearable style="width:300px">
            <el-option v-for="(item, index) in parseGroupList" :key="index" :label="item.pg_name" :value="item.pg_id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="">
          <el-input v-model="searchForm.task_name" clearable placeholder="请输入任务编号"></el-input>
        </el-form-item>
        
        <el-form-item>
          <el-button size="medium" type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-row>
    </el-form>

    <el-table
      :data="tableData"
      ref="tableData"
      v-loading="querying"
      :span-method="listSpanMethod"
      :cell-style="listCellStyle"
      border
    >
      <el-table-column prop="task_name" label="任务编号" align="center" key="task_name"></el-table-column>
      <el-table-column prop="operator_name" label="任务提交人" align="center" key="operator_name"></el-table-column>
      <el-table-column prop="id" label="子任务ID" align="center" key="sub_task_id"></el-table-column>
      <el-table-column prop="parse_group_name" label="解析组名称" align="center" key="parse_group_name"></el-table-column>
      <el-table-column label="操作" align="center" width="180" key="operate">
        <template slot-scope="scope">
          <div>
            <el-button type="text" @click="handleCheck(scope.row, 'check')">审核</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-row style="text-align: center; padding-top: 20px">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.page_size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      ></el-pagination>
    </el-row>

    <el-row>
      <!-- 审核中，点击：审核 -->
      <check-dialog
        v-if="checkDialogVisible"
        :checkRowData="checkRowData"
        :checkDialogType="checkDialogType"
        @close="checkDialogVisible = false"
        @refresh="onSearch"
      ></check-dialog>
    </el-row>

  </el-card>
</template>
<script>

import http from "@/api/http.js"
import checkDialog from "@/views/parse-group-config/task-list/dialog/checkDialog.vue"
import dateFormat from "@/utils/dateFormat"

export default {
  name: "audit-task",
  components: {
    checkDialog,
  },
  filters: {
    dateFormat: dateFormat
  },
  props: [],
  data() {
    return {
      querying: false,
      tableData: [],
      checkDialogVisible: false, // 审核弹窗
      rowData: {},
      checkRowData: {},
      dialogType: '',
      checkDialogType: '',
      parseGroupList: [],
      pagination: {
        total: 0,
        page: 1,
        page_size: 20,
      },
      searchForm: {
        task_name: '',
        id: '',
        pg_id: '',
      },
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {
    this.onSearch()
    this.getParseGroupList()
  },
  methods: {
    // 点击：审核
    handleCheck(row, type) {
      this.checkDialogType = type
      this.checkRowData = JSON.parse(JSON.stringify(row))
      this.checkDialogVisible = true
    },
    onSearch() {
      this.pagination.page = 1;
      this.query();
    },
    async query() {
      this.querying = true;
      let params = {
        pg_id: this.searchForm.pg_id,
        task_name: this.searchForm.task_name,
        state: 1,
        page: this.pagination.page,
        page_size: this.pagination.page_size,
      };
      try {
        let res = await http.get(`/sda/change_cover/task/list`, params)
        let result = (res && res.data && res.data.items) || []
        const data = this.handleTableData(result)
        this.tableData = data
        this.pagination.total = res && res.data && res.data.total;
        this.querying = false;
      } catch (error) {
        this.querying = false;
      } finally {
        this.querying = false;
      }
    },
    handleTableData(data) {
      const arrData = []
      for (let i = 0; i < data.length; i++) {
        let detail = data[i].detail
        if (!detail) {
          detail = [{}]
        }
        for (let j = 0; j < detail.length; j++) {
          const info = {
            span_num: j === 0 ? detail.length : 0,
            auditor_id: data[i].auditor_id,
            auditor_name: data[i].auditor_name,
            create_time: data[i].create_time,
            finish_time: data[i].finish_time,
            operator_id: data[i].operator_id,
            operator_name: data[i].operator_name,
            state: data[i].state,
            submit_time: data[i].submit_time,
            task_id: data[i].task_id,
            task_name: data[i].task_name,
            update_time: data[i].update_time,
            detail: data[i].detail,
            id: detail[j].id,
            parse_group_id: detail[j].parse_group_id,
            parse_group_name: detail[j].parse_group_name,
            result: detail[j].result,
            msg: detail[j].msg,
            subTaskId: detail[j].task_id,
          }
          arrData.push(info)
        }
      }
      return arrData;
    },
    listSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex < 2 || columnIndex > 3) {
        if (row.span_num > 0) {
          return {
            rowspan: row.span_num,
            colspan: 1,
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          };
        }
      }
    },
    listCellStyle({ row, column, rowIndex, columnIndex }) {
      if (columnIndex < 2 || columnIndex > 3) {
        return {
          backgroundColor: "transparent",
        };
      }
    },
    handleSizeChange(val) {
      this.pagination.page = 1;
      this.pagination.page_size = val;
      this.query();
    },
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.query();
    },
    getParseGroupList() {
      http.get(`/sda/parse_group/get`).then(res => {
        this.parseGroupList = (res && res.data && res.data.items) || []
      })
    },
  },
};
</script>
<style scoped lang="scss">
</style>
