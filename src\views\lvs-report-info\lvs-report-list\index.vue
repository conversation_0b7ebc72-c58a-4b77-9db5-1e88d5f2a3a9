<template>
  <el-card>
    <!-- LVS上报信息 -->
    <!-- 查询条件 -->
    <el-form :model="searchForm" ref="searchForm" label-position="left" inline>
      <div class="flex-container">
        <el-row>
          <el-form-item label="IP" prop="ip">
            <el-input v-model="searchForm.ip" placeholder="请输入IP" style="width:180px"></el-input>
          </el-form-item>
          <el-form-item label="LVS编码" prop="lvs_code">
            <el-input v-model="searchForm.lvs_code" placeholder="请输入LVS编码" style="width:180px"></el-input>
          </el-form-item>
          <el-form-item label="LVS名称" prop="lvs_name">
            <el-input v-model="searchForm.lvs_name" placeholder="请输入LVS名称" style="width:180px"></el-input>
          </el-form-item>
          <el-form-item label="线路更新时间距现在超过" prop="time_interval">
            <el-input v-model="searchForm.time_interval" placeholder="" style="width:150px">
              <template slot="append">
                <el-select v-model="searchForm.time_unit" style="width:80px">
                  <el-option label="秒" value="second"></el-option>
                  <el-option label="分钟" value="minute"></el-option>
                  <el-option label="小时" value="hour"></el-option>
                  <el-option label="天" value="day"></el-option>
                </el-select>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-row>
      </div>
    </el-form>

    <el-table :data="tableData" v-loading="querying" border class="table-style">
      <el-table-column prop="ip" label="IP" align="center"></el-table-column>
      <el-table-column prop="line_state" label="线路更新状态" align="center">
        <template slot-scope="scope">
          <span :style="{ color: scope.row.line_state === 1 ? '#67C23A' : '#F56C6C' }">
            {{ getLineStateText(scope.row.line_state) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="line_up_ts" label="线路更新时间" align="center">
        <template slot-scope="scope">
          <span :style="{ color: scope.row.timeout === 1 ? 'red' : '' }">{{ formatTime(scope.row.line_up_ts) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="msg" label="更新日志" align="center"></el-table-column>
      <el-table-column prop="lvs_id" label="LVS ID" align="center"></el-table-column>
      <el-table-column prop="lvs_name" label="LVS名称" align="center"></el-table-column>
      <el-table-column prop="lvs_code" label="LVS编码" align="center"></el-table-column>
      <el-table-column prop="lake_name" label="Lake名称" align="center"></el-table-column>
      <el-table-column prop="lake_code" label="Lake编码" align="center"></el-table-column>
      <el-table-column prop="update_time" label="上报时间" align="center">
        <template slot-scope="scope">
          <span>{{ formatTime(scope.row.update_time) }}</span>
        </template>
      </el-table-column>
    </el-table>
    
    <el-row style="text-align: center; padding-top: 20px">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.page_size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      ></el-pagination>
    </el-row>

  </el-card>
</template>
<script>
import http from "@/views/lvs-report-info/http.js"
import dateFormat from "@/utils/dateFormat.js"

export default {
  name: "lvs-report-list",
  components: {},
  props: [],
  data() {
    return {
      querying: false,
      tableData: [],
      pagination: {
        total: 0,
        page: 1,
        page_size: 20,
      },
      searchForm: {
        ip: '',
        lvs_code: '',
        lvs_name: '',
        time_interval: '',
        time_unit: 'minute' // 默认分钟
      },
    };
  },
  computed: {},
  created() {},
  watch: {},
  mounted() {
    this.onSearch()
  },
  methods: {
    onSearch() {
      this.pagination.page = 1;
      this.query();
    },
    resetSearch() {
      this.$refs.searchForm.resetFields();
      this.onSearch();
    },
    async query() {
      // 将时间单位转换为秒
      let timeIntervalInSeconds = 0;
      if (this.searchForm.time_interval) {
        const interval = Number(this.searchForm.time_interval);
        if (!isNaN(interval)) {
          switch (this.searchForm.time_unit) {
            case 'second':
              timeIntervalInSeconds = interval;
              break;
            case 'minute':
              timeIntervalInSeconds = interval * 60;
              break;
            case 'hour':
              timeIntervalInSeconds = interval * 60 * 60;
              break;
            case 'day':
              timeIntervalInSeconds = interval * 60 * 60 * 24;
              break;
            default:
              timeIntervalInSeconds = interval * 60; // 默认按分钟计算
          }
        }
      }

      let params = {
        ip: this.searchForm.ip,
        lvs_code: this.searchForm.lvs_code,
        lvs_name: this.searchForm.lvs_name,
        time_interval: timeIntervalInSeconds || undefined,
        page: this.pagination.page,
        page_size: this.pagination.page_size,
      };
      this.querying = true;
      try {
        const res = await http.getLvsReportList(params);
        if (res && res.code === 100000) {
          if (res.data && Array.isArray(res.data.items)) {
            this.tableData = res.data.items;
            this.pagination.total = res.data.total || 0;
          } else {
            console.error('返回数据格式异常:', res.data);
            this.tableData = [];
            this.pagination.total = 0;
          }
        } else {
          this.$message.error((res && res.message) || "查询失败");
        }
      } catch (error) {
        console.error('API请求错误:', error);
        this.$message.error("查询失败");
      } finally {
        this.querying = false;
      }
    },
    handleSizeChange(val) {
      this.pagination.page = 1;
      this.pagination.page_size = val;
      this.query();
    },
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.query();
    },
    formatTime(timestamp) {
      if (!timestamp) return '--';
      return dateFormat(new Date(timestamp * 1000));
    },
    getLineStateText(state) {
      if (state === 1) return '成功';
      if (state === 2) return '线路同步失败';
      if (state === 3) return 'lvs加载失败';
      return state; // 其他值直接显示数字
    }
  }
};
</script>
<style scoped>
.flex-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.table-style {
  margin-top: 20px;
  width: 100%;
}
</style> 
