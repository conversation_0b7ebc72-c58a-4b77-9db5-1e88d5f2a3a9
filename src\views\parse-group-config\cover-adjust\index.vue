<template>
  <el-card>
    <!-- 覆盖调整 -->
    <el-form :model="searchForm" ref="searchForm" label-position="left" inline>
      <el-row>
        <el-form-item label="" prop="pg_id">
          <el-select v-model="searchForm.pg_id" placeholder="请选择解析组名称" filterable clearable style="width:300px">
            <el-option v-for="(item, index) in parseGroupList" :key="index" :label="item.parse_group_name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button size="medium" type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
        <span style="float:right">
          <el-button size="medium" @click="handleAdd">新增解析组</el-button>
        </span>
      </el-row>
    </el-form>

    <el-table :data="tableData" v-loading="querying">
      <el-table-column prop="pg_id" label="解析组ID" align="center"></el-table-column>
      <el-table-column prop="pg_name" label="解析组名称" align="center"></el-table-column>
      <el-table-column prop="state" label="状态" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.state === 0">启用</span>
          <span v-if="scope.row.state === 1">禁用</span>
        </template>
      </el-table-column>
      <el-table-column prop="priority" label="优先级" align="center"></el-table-column>
      <el-table-column label="操作" align="center" width="180">
        <template slot-scope="scope">
          <div>
            <el-button type="text" @click="showConfig(scope.row)">配置</el-button>
            <el-button type="text" @click="handleEdit(scope.row)">修改</el-button>
            <el-button type="text" @click="handleDelete(scope.row)">删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-row style="text-align: center; padding-top: 20px">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.page_size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      ></el-pagination>
    </el-row>

    <el-row>
      <!-- 新增 -->
      <cover-adjust-dialog
        v-if="addDialog"
        @close="addDialog = false"
        @refresh="onSearch"
      ></cover-adjust-dialog>
      <!-- 修改 -->
      <cover-adjust-dialog
        v-if="editDialog"
        :is-edit="true"
        :rowData="rowData"
        @close="editDialog = false"
        @refresh="onSearch"
      ></cover-adjust-dialog>
    </el-row>

  </el-card>
</template>
<script>

import http from "@/api/http.js"
import coverAdjustDialog from "@/views/parse-group-config/cover-adjust/dialog/coverAdjustDialog.vue"
import { mapState } from "vuex";
import dateFormat from "@/utils/dateFormat"

export default {
  name: "cover-adjust",
  components: {
    coverAdjustDialog,
  },
  filters: {
    dateFormat: dateFormat
  },
  props: [],
  data() {
    return {
      querying: false,
      tableData: [],
      rowData: {},
      addDialog: false,
      editDialog: false,
      pagination: {
        total: 0,
        page: 1,
        page_size: 50,
      },
      searchForm: {
        pg_id: '',
      },
    };
  },
  computed: {
    ...mapState({
      parseGroupList: (state) => state.baseData.parseGroupList,
    }),
  },
  watch: {},
  created() {},
  mounted() {
    this.onSearch()
  },
  methods: {
    handleAdd() {
      this.addDialog = true
    },
    handleEdit(row) {
      this.rowData = structuredClone(row)
      this.editDialog = true
    },
    showConfig(entity) {
      this.$router.push({name: 'parse-config', query: { id: entity.pg_id, from: "coverAdjust" }})
    },
    async handleDelete(row) {
      await this.$confirm("确定删除该条数据吗？", "提示", {
        type: "warning",
      })
      let params = {
        operator: window.localStorage.getItem('userInfo'),
      };
      const res = await http.delete(`/sda/parse_group/delete/${row.id}`, params)
      if (res && res.code === 100000) {
        let msg = (res && res.message) || "删除成功"
        this.$message.success(msg);
        this.onSearch()
      }
    },
    onSearch() {
      this.pagination.page = 1;
      this.query();
    },
    async query() {
      let params = {
        pg_id: this.searchForm.pg_id,
        page: this.pagination.page,
        page_size: this.pagination.page_size,
      };
      this.querying = true;
      try {
        await http.get(`/sda/parse_group/get`, params).then((res) => {
          this.tableData = res && res.data && res.data.items;
          this.pagination.total = res && res.data && res.data.total;
          this.querying = false;
        });
      } catch (error) {
        this.querying = false;
      } finally {
        this.querying = false;
      }
    },
    handleSizeChange(val) {
      this.pagination.page = 1;
      this.pagination.page_size = val;
      this.query();
    },
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.query();
    },
  },
};
</script>
<style scoped lang="scss">
.btn {
  float: right;
}
</style>
