<template>
  <el-dialog
    append-to-body
    :title="title"
    :visible="true"
    :close-on-click-modal="false"
    width="560px"
    @close="handleCancel"
  >
    <el-row>
      <el-form :inline="true" ref="dialogForm" :model="dialogForm" :rules="rules" label-width="100px" label-position="right">
        <el-row>
          <el-form-item label="解析组" prop="parse_group_id">
            <el-select v-model="dialogForm.parse_group_id" placeholder="请选择解析组" style="width:300px" :disabled="isEdit" filterable clearable>
              <el-option :value="-1" label="ALL" />
              <el-option v-for="(item, index) in parseGroupList" :key="index" :label="item.parse_group_name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <!-- 灰度开关 -->
          <el-form-item label="灰度开关" prop="enable">
            <el-switch
              v-model="dialogForm.enable"
              :inactive-value="0"
              :active-value="1"
            ></el-switch>
          </el-form-item>
        </el-row>
      </el-form>
    </el-row>

    <div slot="footer" class="dialog-footer">
      <el-button size="medium" @click="handleCancel">取 消</el-button>
      <el-button size="medium" type="primary" @click="handleSubmit" :loading="submitLoading" :disabled="submitLoading">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import http from '@/api/http.js'
import { mapState } from "vuex";

export default {
  components: {},
  props: {
    isEdit: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: null
    },
  },
  data() {
    return {
      submitLoading: false,
      dialogForm: {
        parse_group_id: "",
        enable: 0,
      },
      rules: {
        parse_group_id: [
          { required: true, message: "请选择解析组", trigger: "change"}
        ],
      },
    }
  },
  computed: {
    title() {
      return this.isEdit ? "修改" : "新增";
    },
    ...mapState({
      parseGroupList: (state) => state.baseData.parseGroupList,
    }),
  },
  mounted() {
    if (this.isEdit) {
      this.handleInitData()
    }
  },
  methods: {
    async handleSubmit() {
      try {
        await new Promise((resolve, reject) => {
          this.$refs.dialogForm.validate(valid => {
            valid ? resolve() : reject('检查错误')
          })
        })
      } catch (err) {
        return false
      }
      this.submitLoading = true
      let params = {
        ...this.dialogForm,
        operator: window.localStorage.getItem('userInfo'),
      }
      let res = {}
      let successMsg = "添加成功"
      if (this.isEdit) {
        // 修改
        res = await http.patch(`/sda/parse_group/redundancy/${this.rowData.id}`, params)
        successMsg = "修改成功"
      } else {
        // 新增
        res = await http.post(`/sda/parse_group/redundancy`, params)
      }
      if (res && res.code === 100000) {
        successMsg = res && res.message;
        this.$message.success(successMsg);
        this.$emit("close")
        this.$emit("refresh")
        this.submitLoading = false
      } else {
        this.submitLoading = false
      }
    },
    handleCancel() {
      this.$emit("close")
      this.$emit("refresh")
    },
    /**
     * 数据回填
     */
    handleInitData() {
      const row = structuredClone(this.rowData)
      this.dialogForm.parse_group_id = row.parse_group_id
      this.dialogForm.enable = row.enable
    }
  },
}
</script>

<style scoped lang="scss"></style>