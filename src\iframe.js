// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from 'vue'
import App from './App'
import router from './router'
import ElementUI from 'element-ui'
import Package from './package'
import store from './store'
import './components/index.js'
import './styles/iframe.scss'
import axios from 'axios'
import { ajaxAuth } from "@/api/ajax";
import dayjs from 'dayjs';
import liteVirtualList from "lite-virtual-list"
import SafeVHtml from '@/lib/v-safe-html-directive'

Vue.prototype.$axiosTools = axios;
Vue.prototype.$dayjs = dayjs;
Vue.use(ElementUI, { size: 'small' })
Vue.use(Package)
Vue.use(liteVirtualList)
Vue.use(SafeVHtml)

// 初始化基础数据
store.dispatch("setLakeList")
store.dispatch("setFullLakeList")
store.dispatch("setIpList")
store.dispatch("setNodeList")
store.dispatch("setParseGroupList")
store.dispatch("setViewList")
store.dispatch("setAllDomainList")
store.dispatch("setAccessList")
store.dispatch("setHostGroupList")
store.dispatch("setIspList")
store.dispatch("setAreaList") // 获取区域数据（大区、省份、城市）

// 路由权限过滤
if (JSON.parse(localStorage.getItem('attachGroup'))) {
  var localRouter = router.options.routes;
  for (var i = 0; i < localRouter.length; i++) {
    if (localRouter[i].name == 'rightsManage') {
      localRouter[i].hidden = false
    }
  }
}

function getUserInfo() {
  return ajaxAuth.get('/v1/auth/getUserInfo').then(res => {
    if (res.data.status.code === 0) {
      localStorage.setItem("userInfo", res.data.info.name);
      localStorage.setItem("uid", res.data.info.uid);
      localStorage.setItem('loginUser', res.data.info.name);
      localStorage.setItem('userPhoto', res.data.info.avatar);
    }
  })
}

// 请求文件内容及创建实例
async function main() {
  await getUserInfo()
  Vue.prototype.$eventBus = new Vue
  new Vue({
    el: '#app',
    router,
    store,
    render: h => h(App),
    template: '<App/>'
  })
}

// 方法初始执行
main();

