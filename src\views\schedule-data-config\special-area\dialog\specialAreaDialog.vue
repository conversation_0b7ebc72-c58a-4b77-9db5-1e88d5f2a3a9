<template>
  <el-dialog
    append-to-body
    :title="title"
    :visible="true"
    :close-on-click-modal="false"
    width="600px"
    @close="handleCancel"
  >
    <el-row>
      <el-form :inline="true" ref="dialogForm" :model="dialogForm" :rules="rules" label-width="140px" label-position="right">
        <el-row>
          <el-form-item label="解析组" prop="pg_id">
            <el-select v-model="dialogForm.pg_id" placeholder="请选择解析组" filterable clearable :disabled="isEdit" style="width:300px" :loading="parge_group_loading">
              <el-option v-for="(item, index) in parseGroupList" :key="index" :label="item.parse_group_name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="区域" prop="view_id">
            <el-select v-model="dialogForm.view_id" placeholder="请选择区域" filterable clearable :disabled="isEdit" style="width:300px">
              <el-option v-for="(item, index) in viewList" :key="index" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="v4比例" prop="v4_rate">
            <el-input
              v-model.number="dialogForm.v4_rate"
              placeholder="请输入v4比例"
              style="width:300px"
            ></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="v6比例" prop="v6_rate">
            <el-input
              v-model.number="dialogForm.v6_rate"
              placeholder="请输入v6比例"
              style="width:300px"
            ></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="备注" prop="message">
            <el-input
              v-model="dialogForm.message"
              placeholder="请输入备注"
              style="width:300px"
            ></el-input>
          </el-form-item>
        </el-row>
      </el-form>
    </el-row>

    <div slot="footer" class="dialog-footer">
      <el-button size="medium" @click="handleCancel">取 消</el-button>
      <el-button size="medium" type="primary" @click="handleSubmit" :loading="submiting" :disabled="submiting">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import http from '@/api/http.js'
import { mapState } from "vuex";

export default {
  components: {},
  props: {
    isEdit: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: null
    },
  },
  data() {
    return {
      submiting: false,
      dialogForm: {
        pg_id: "",
        pg_name: "",
        view_id: "",
        view_name: "",
        v4_rate: "",
        v6_rate: "",
        message: "",
      },
      rules: {
        pg_id: [
          { required: true, message: "请选择解析组", trigger: "change"}
        ],
        view_id: [
          { required: true, message: "请选择区域", trigger: "change"}
        ],
        v4_rate: [
          { required: true, message: "请输入v4比例", trigger: "blur"},
          { trigger: "blur", pattern: /^[0-9]*$/, message: "请输入整数，最大100，最小0" },
          {
            validator: (rule, value, callback) => {
              if (value > 100 || value < 0) {
                callback(new Error("请输入整数，最大100，最小0"));
              } else callback();
            },
            trigger: ["blur", "change"],
          },
        ],
        v6_rate: [
          { required: true, message: "请输入v6比例", trigger: "blur"},
          { trigger: "blur", pattern: /^[0-9]*$/, message: "请输入整数，最大100，最小0" },
          {
            validator: (rule, value, callback) => {
              if (value > 100 || value < 0) {
                callback(new Error("请输入整数，最大100，最小0"));
              } else callback();
            },
            trigger: ["blur", "change"],
          },
        ]
      },
    }
  },
  computed: {
    title() {
      return this.isEdit ? "修改" : "新增";
    },
    ...mapState({
      parseGroupList: (state) => state.baseData.parseGroupList,
      viewList: (state) => state.baseData.viewList,
      parge_group_loading: (state) => state.baseData.parge_group_loading,
    }),
  },
  mounted() {
    if (this.isEdit) {
      this.handleInitData()
    }
  },
  methods: {
    async handleSubmit() {
      try {
        await new Promise((resolve, reject) => {
          this.$refs.dialogForm.validate(valid => {
            valid ? resolve() : reject('检查错误')
          })
        })
      } catch (err) {
        return false
      }
      let parseGroup = this.parseGroupList.find(item => item.id === this.dialogForm.pg_id)
      let viewItem = this.viewList.find(item => item.id === this.dialogForm.view_id)
      this.submiting = true
      let params = {
        ...this.dialogForm,
        operator: window.localStorage.getItem('userInfo'),
      }
      params.pg_name = parseGroup && parseGroup.parse_group_name
      params.view_name = viewItem && viewItem.name
      let res = {}
      let successMsg = "添加成功"
      if (this.isEdit) {
        // 修改
        res = await http.post(`/sdcp/pvbw/special_conf/update/${this.rowData.id}`, params)
        successMsg = "修改成功"
      } else {
        // 新增
        res = await http.post(`/sdcp/pvbw/special_conf/create`, params)
      }
      if (res && res.code === 100000) {
        this.$message.success(successMsg);
        this.$emit("close")
        this.$emit("refresh")
        this.submiting = false
      } else {
        this.submiting = false
      }
    },
    handleCancel() {
      this.$emit("close")
      this.$emit("refresh")
    },
    handleInitData() {
      const row = structuredClone(this.rowData)
      this.dialogForm.pg_id = row.pg_id
      this.dialogForm.pg_name = row.pg_name
      this.dialogForm.view_id = row.view_id
      this.dialogForm.view_name = row.view_name
      this.dialogForm.v4_rate = row.v4_rate
      this.dialogForm.v6_rate = row.v6_rate
      this.dialogForm.message = row.message
    },
  },
}
</script>

<style scoped lang="scss"></style>