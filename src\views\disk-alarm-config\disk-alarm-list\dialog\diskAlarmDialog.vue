<template>
  <el-dialog
    append-to-body
    :title="title"
    :visible="true"
    :close-on-click-modal="false"
    width="600px"
    @close="handleCancel"
  >
    <el-row>
      <el-form :inline="true" ref="dialogForm" :model="dialogForm" :rules="rules" label-width="140px" label-position="right">
        <el-row>
          <el-form-item label="lake" prop="lake_id">
            <el-select v-model="dialogForm.lake_id" placeholder="请选择lake" style="width:300px" :disabled="isEdit" filterable clearable>
              <el-option :value="-1" label="ALL" />
              <el-option v-for="(item, index) in lakeList" :key="index" :label="item.bk_inst_name" :value="item.bk_inst_id"></el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="Iowait异常阈值" prop="iowait_limit">
            <el-input
              v-model.number="dialogForm.iowait_limit"
              placeholder="请输入Iowait异常阈值"
              clearable
              style="width:300px"
            ></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="持续异常周期（s）" prop="abnormal_period">
            <el-input
              v-model.number="dialogForm.abnormal_period"
              placeholder="请输入持续异常周期（s）"
              clearable
              style="width:300px"
            ></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="持续异常次数" prop="abnormal_times">
            <el-input
              v-model.number="dialogForm.abnormal_times"
              placeholder="请输入持续异常次数"
              clearable
              style="width:300px"
            ></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="消警时长（s）" prop="normal_period">
            <el-input
              v-model.number="dialogForm.normal_period"
              placeholder="请输入消警时长（s）"
              clearable
              style="width:300px"
            ></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="频繁切换次数" prop="frequent_switch_times">
            <el-input
              v-model.number="dialogForm.frequent_switch_times"
              placeholder="请输入频繁切换次数"
              clearable
              style="width:300px"
            ></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="状态" prop="state">
            <el-select
              v-model="dialogForm.state"
              filterable
              placeholder="请选择状态"
              clearable
              style="width:300px"
            >
              <el-option :value="0" label="启用"></el-option>
              <el-option :value="1" label="禁用"></el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="参考域名5xx个数" prop="adopt_req_status">
            <el-select
              v-model="dialogForm.adopt_req_status"
              filterable
              placeholder="请选择状态"
              clearable
              @change="adopt_req_status_change"
              style="width:300px"
            >
              <el-option :value="0" label="是"></el-option>
              <el-option :value="1" label="否"></el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row v-if="dialogForm.adopt_req_status === 0">
          <el-form-item label="域名5xx个数" prop="abnormal_count">
            <el-input v-model.number="dialogForm.abnormal_count" placeholder="请输入域名5xx个数" clearable style="width:300px"></el-input>
          </el-form-item>
        </el-row>
      </el-form>
    </el-row>

    <div slot="footer" class="dialog-footer">
      <el-button size="medium" @click="handleCancel">取 消</el-button>
      <el-button size="medium" type="primary" @click="handleSubmit" :loading="submiting" :disabled="submiting">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import http from '@/api/http.js'
import { mapState } from "vuex";

export default {
  components: {},
  props: {
    isEdit: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: null
    },
  },
  data() {
    return {
      submiting: false,
      dialogForm: {
        lake_id: "", // lake
        iowait_limit: "", // Iowait异常阈值
        abnormal_period: "", // 持续异常周期
        abnormal_times: "", // 持续异常次数
        normal_period: "", // 消警时长（s）
        frequent_switch_times: "", // 频繁切换次数
        state: 0, // 状态
        adopt_req_status: 0, // 参考域名5xx个数
        abnormal_count: 0, // 域名5xx个数
      },
      rules: {
        lake_id: [
          { required: true, message: "请选择lake", trigger: "change"}
        ],
        iowait_limit: [
          { required: true, message: "请输入Iowait异常阈值", trigger: "blur"},
          { trigger: "blur", pattern: /^[0-9]*$/, message: "请输入大于0的整数" },
          {
            validator: (rule, value, callback) => {
              if (value < 1) {
                callback(new Error("请输入大于0的整数"));
              } else callback();
            },
            trigger: ["blur", "change"],
          },
        ],
        abnormal_period: [
          { required: true, message: "请输入持续异常周期", trigger: "blur"},
          { trigger: "blur", pattern: /^[0-9]*$/, message: "请输入大于0的整数" },
          {
            validator: (rule, value, callback) => {
              if (value < 1) {
                callback(new Error("请输入大于0的整数"));
              } else callback();
            },
            trigger: ["blur", "change"],
          },
        ],
        abnormal_times: [
          { required: true, message: "请输入持续异常次数", trigger: "blur"},
          { trigger: "blur", pattern: /^[0-9]*$/, message: "请输入大于0的整数" },
          {
            validator: (rule, value, callback) => {
              if (value < 1) {
                callback(new Error("请输入大于0的整数"));
              } else callback();
            },
            trigger: ["blur", "change"],
          },
        ],
        normal_period: [
          { required: true, message: "请输入消警时长（s）", trigger: "blur"},
          { trigger: "blur", pattern: /^[0-9]*$/, message: "请输入大于0的整数" },
          {
            validator: (rule, value, callback) => {
              if (value < 1) {
                callback(new Error("请输入大于0的整数"));
              } else callback();
            },
            trigger: ["blur", "change"],
          },
        ],
        frequent_switch_times: [
          { required: true, message: "请输入频繁切换次数", trigger: "blur"},
          { trigger: "blur", pattern: /^[0-9]*$/, message: "请输入大于0的整数" },
          {
            validator: (rule, value, callback) => {
              if (value < 1) {
                callback(new Error("请输入大于0的整数"));
              } else callback();
            },
            trigger: ["blur", "change"],
          },
        ],
        state: [
          { required: true, message: "请选择状态", trigger: "change"}
        ],
        adopt_req_status: [
          { required: true, message: "请选择参考域名5xx个数", trigger: "change"}
        ],
        abnormal_count: [
          { required: true, message: "请输入域名5xx个数", trigger: "blur"},
          { trigger: "blur", pattern: /^[0-9]*$/, message: "请输入大于0的整数" },
          {
            validator: (rule, value, callback) => {
              if (this.dialogForm.adopt_req_status === 0 && value < 1) {
                callback(new Error("请输入大于0的整数"));
              } else callback();
            },
            trigger: ["blur", "change"],
          },
        ],
      },
    }
  },
  computed: {
    title() {
      return this.isEdit ? "修改" : "新增";
    },
    ...mapState({
      lakeList: (state) => state.baseData.lakeList,
    }),
  },
  mounted() {
    if (this.isEdit) {
      this.handleInitData()
    }
  },
  methods: {
    async handleSubmit() {
      try {
        await new Promise((resolve, reject) => {
          this.$refs.dialogForm.validate(valid => {
            valid ? resolve() : reject('检查错误')
          })
        })
      } catch (err) {
        return false
      }
      this.submiting = true
      let params = {
        ...this.dialogForm,
        operator: window.localStorage.getItem('userInfo'),
      }
      let res = {}
      let successMsg = "添加成功"
      if (this.isEdit) {
        // 修改
        res = await http.patch(`/sda/disks/iowaits/${this.rowData.id}`, params)
        successMsg = "修改成功"
      } else {
        // 新增
        res = await http.post(`/sda/disks/iowaits`, params)
      }
      if (res && res.code === 100000) {
        this.$message.success(successMsg);
        this.$emit("close")
        this.$emit("refresh")
        this.submiting = false
      } else {
        this.submiting = false
        // let message = res && res.message ? res.message : this.isEdit ? "修改失败" : "添加失败"
        // this.$message.error(message);
      }
    },
    adopt_req_status_change(val) {
      if (val === 1) {
        this.dialogForm.abnormal_count = 0
      }
    },
    handleCancel() {
      this.$emit("close")
      this.$emit("refresh")
    },
    /**
     * 初始化表单数据
     * 此方法用于将rowData对象的数据复制到dialogForm对象中
     * 它创建rowData的深拷贝以避免直接修改原始数据
     */
    handleInitData() {
      // 创建rowData的深拷贝以避免潜在的原始数据污染
      const row = structuredClone(this.rowData)
  
      // 将rowData中的lake_id赋值给dialogForm的lake_id属性
      this.dialogForm.lake_id = row.lake_id
  
      // 将rowData中的iowait_limit赋值给dialogForm的iowait_limit属性
      this.dialogForm.iowait_limit = row.iowait_limit
  
      // 将rowData中的abnormal_period赋值给dialogForm的abnormal_period属性
      this.dialogForm.abnormal_period = row.abnormal_period
  
      // 将rowData中的abnormal_times赋值给dialogForm的abnormal_times属性
      this.dialogForm.abnormal_times = row.abnormal_times
  
      // 将rowData中的normal_period赋值给dialogForm的normal_period属性
      this.dialogForm.normal_period = row.normal_period
  
      // 将rowData中的frequent_switch_times赋值给dialogForm的frequent_switch_times属性
      this.dialogForm.frequent_switch_times = row.frequent_switch_times
  
      // 将rowData中的state赋值给dialogForm的state属性
      this.dialogForm.state = row.state
      this.dialogForm.adopt_req_status = row.adopt_req_status
      this.dialogForm.abnormal_count = row.abnormal_count
    }
  },
}
</script>

<style scoped lang="scss"></style>