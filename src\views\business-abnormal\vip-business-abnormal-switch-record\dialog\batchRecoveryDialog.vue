<template>
  <el-dialog
    append-to-body
    title="批量恢复"
    :visible="true"
    :close-on-click-modal="false"
    width="550px"
    @close="handleCancel"
  >
    <el-row>
      <div class="mb16">注：您已选择了{{ selectedCount }}个任务</div>
      <el-form :inline="true" ref="dialogForm" :model="dialogForm" :rules="rules" label-width="100px" label-position="right">
        <el-row>
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="dialogForm.remark"
              placeholder="请输入备注"
              clearable
              style="width:300px"
              type="textarea"
              :autosize="{ minRows: 5 }"
            ></el-input>
          </el-form-item>
        </el-row>
      </el-form>
    </el-row>

    <div slot="footer" class="dialog-footer">
      <el-button size="medium" @click="handleCancel">取 消</el-button>
      <el-button size="medium" type="primary" @click="handleSubmit" :loading="submiting" :disabled="submiting">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import http from '@/api/http.js'

export default {
  components: {},
  props: {
    multipleSelection: {
      type: Array,
      default: []
    },
  },
  data() {
    return {
      submiting: false,
      dialogForm: {
        remark: "", // 备注
      },
      rules: {
        remark: [
          { required: true, message: "请输入备注", trigger: "blur"}
        ],
      },
    }
  },
  computed: {
    selectedCount() {
      return this.multipleSelection && this.multipleSelection.length
    },
  },
  mounted() {},
  methods: {
    // 批量恢复
    async handleSubmit() {
      try {
        await new Promise((resolve, reject) => {
          this.$refs.dialogForm.validate(valid => {
            valid ? resolve() : reject('检查错误')
          })
        })
      } catch (err) {
        return false
      }
      this.submiting = true
      const ids = this.multipleSelection && this.multipleSelection.map(item => item.id)
      let params = {
        ids: ids,
        remark: this.dialogForm.remark,
        operator: window.localStorage.getItem('userInfo'),
      }
      const res = await http.post(`/sda/req_abnormal/vip_dispatcher/records/recovery`, params)
      if (res && res.code === 100000) {
        let msg = (res && res.message) || "操作成功"
        this.$message.success(msg);
        this.$emit("close")
        this.$emit("refresh")
        this.submiting = false
      } else {
        this.submiting = false
      }
    },
    handleCancel() {
      this.$emit("close")
      this.$emit("refresh")
    },
  },
}
</script>

<style scoped lang="scss">
.mb16 {
  margin-bottom: 30px;
  font-weight: 600;
}
</style>