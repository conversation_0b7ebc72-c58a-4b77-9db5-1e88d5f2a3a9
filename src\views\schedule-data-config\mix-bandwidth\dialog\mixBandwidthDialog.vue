<template>
  <el-dialog
    append-to-body
    :title="title"
    :visible="true"
    :close-on-click-modal="false"
    width="600px"
    @close="handleCancel"
  >
    <el-row>
      <el-form :inline="true" ref="dialogForm" :model="dialogForm" :rules="rules" label-width="140px" label-position="right">
        <el-row>
          <el-form-item label="接入层" prop="access_id">
            <el-select
              v-model="dialogForm.access_id"
              placeholder="请选择接入层"
              filterable
              multiple
              clearable
              :disabled="isEdit"
              :filter-method="(value) => filterAccess(value)"
              style="width:300px"
            >
              <div style="padding-left:20px">
                <el-button type="text" @click="handleSelectAll()"><i class="el-icon-circle-check" />全选</el-button>
                <el-button type="text" @click="handleRemoveAll()"><i class="el-icon-close" />清空</el-button>
              </div>
              <el-option value="-1" label="all" :disabled="accessIdDisabled" />
              <el-option
                v-for="(item, index) in currentAccessList"
                :key="index"
                :label="item.access_name"
                :value="item.access_id"
                :disabled="item.disabled"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="开关" prop="switch">
            <el-switch
              v-model="dialogForm.switch"
              :inactive-value="0"
              :active-value="1"
              active-color="#13ce66"
              active-text="开启"
              inactive-text="关闭"
            ></el-switch>
          </el-form-item>
        </el-row>
      </el-form>
    </el-row>

    <div slot="footer" class="dialog-footer">
      <el-button size="medium" @click="handleCancel">取 消</el-button>
      <el-button size="medium" type="primary" @click="handleSubmit" :loading="submitLoading" :disabled="submitLoading">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import http from '@/api/http.js'
import { mapState } from "vuex";

export default {
  components: {},
  props: {
    isEdit: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: null
    },
    accessIdList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      dialogForm: {
        access_id: [],
        switch: 0,
      },
      submitLoading: false,
      filterKey: '',
      currentAccessList: [],
      rules: {
        access_id: [
          { required: true, message: "请选择接入层", trigger: "change"}
        ],
        switch: [
          { required: true, message: "请选择开关", trigger: "change"}
        ],
      },
    }
  },
  computed: {
    title() {
      return this.isEdit ? "修改" : "新增";
    },
    accessIdDisabled() {
      if (this.isEdit) return;
      return this.accessIdList.includes('-1')
    },
    ...mapState({
      accessList: (state) => state.baseData.accessList,
    }),
  },
  mounted() {
    this.handleDisabledAccess()
    if (this.isEdit) {
      this.handleInitData()
    }
  },
  methods: {
    async handleSubmit() {
      // 表单校验
      try {
        await new Promise((resolve, reject) => {
          this.$refs.dialogForm.validate(valid => {
            valid ? resolve() : reject('表单项有错误，请检查')
          })
        })
      } catch (err) {
        this.$message({
          showClose: true,
          message: err.message || err,
          type: 'error'
        })
        return false
      }

      this.submitLoading = true
      
      // 传参
      let params = {
        switch: this.dialogForm.switch,
      }
      let res = {}
      let successMsg = "添加成功"
      if (this.isEdit) {
        // 修改
        let access_id = this.dialogForm.access_id[0]
        // 根据 access_id 来获取 access_name
        let accessItem = this.accessList.find(item => {
          return item.access_id === access_id
        })
        let access_name = accessItem && accessItem.access_name
        params.access_id = access_id
        params.access_name = access_name
        params.id = this.rowData.id
        res = await http.post(`/sdcp/mix_bw/conf/update/${this.rowData.id}`, params)
        successMsg = "修改成功"
      } else {
        // 新增
        let access_id_list = structuredClone(this.dialogForm.access_id)
        let accessArr = []
        if (access_id_list.includes('-1')) {
          accessArr.push({
            access_id: '-1',
            access_name: 'all'
          })
        }
        this.accessList.map(item => {
          if (access_id_list.includes(item.access_id)) {
            accessArr.push(item)
          }
        })
        // 根据access_id去重
        const uniqueAccessArr = Array.from(new Set(accessArr.map(item => item.access_id))).map(accessIdItem => {
          return accessArr.find(item => item.access_id === accessIdItem);
        });
        params.data = uniqueAccessArr
        res = await http.post(`/sdcp/mix_bw/conf/create`, params)
      }
      if (res && res.code === 100000) {
        this.$message.success(successMsg);
        this.$emit("close")
        this.$emit("refresh")
        this.submitLoading = false
      } else {
        this.submitLoading = false
      }
    },
    // 列表中如果已存在access_id，则不能再添加，需要将接入层下拉框已存在的access_id置灰不可选
    handleDisabledAccess() {
      let currentAccessList = structuredClone(this.accessList)
      currentAccessList.map(item => {
        if (this.accessIdList.includes(item.access_id)) {
          item.disabled = true
        } else {
          item.disabled = false
        }
      })
      this.currentAccessList = structuredClone(currentAccessList)
    },
    // 根据输入的值进行过滤
    filterAccess(query) {
      this.filterKey = query
      this.currentAccessList = this.currentAccessList.filter(item => item.access_name.includes(query))
    },
    // 接入层下拉框: 对搜索结果进行全选
    handleSelectAll() {
      let result = []
      this.currentAccessList.forEach(item => {
        if (!item.disabled) {
          result.push(item)
        }
      })
      this.dialogForm.access_id = result.map(item => item.access_id)
    },
    // 接入层下拉框: 将选中的清空
    handleRemoveAll() {
      this.dialogForm.access_id = []
      this.handleDisabledAccess()
    },
    handleCancel() {
      this.$emit("close")
      this.$emit("refresh")
    },
    handleInitData() {
      const row = structuredClone(this.rowData)
      this.dialogForm.access_id = [row.access_id]
      this.dialogForm.switch = row.switch
    },
  },
}
</script>

<style scoped lang="scss"></style>