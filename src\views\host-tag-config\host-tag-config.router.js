import EmptyLayout from "@/package/src/components/EmptyLayout";

export default [
  {
    path: '/hostTagConfig',
    component: EmptyLayout,
    name: "hostTagConfig",
    meta: {
      title: "主机标签规划",
    },
    children: [
      {
        path: 'tagConfig',
        name: 'tagConfig',
        component: resolve => require(['@/views/host-tag-config/tag-config/index.vue'], resolve),
        meta: {
          title: '标签配置',
          keepAlive: true
        },
        hidden: false
      },
      {
        path: 'hostTagConfig',
        name: 'hostTagConfig',
        component: resolve => require(['@/views/host-tag-config/host-tag-config/index.vue'], resolve),
        meta: {
          title: '主机标签配置',
          keepAlive: true
        },
        hidden: false
      },
        {
        path: 'tagRuleConfig',
        name: 'tagRuleConfig',
        component: resolve => require(['@/views/host-tag-config/tag-rule-config/index.vue'], resolve),
        meta: {
          title: '标签规则配置',
          keepAlive: true
        },
        hidden: false
      },
      {
        path: 'hostGroupConfig',
        name: 'hostGroupConfig',
        component: resolve => require(['@/views/host-tag-config/host-group-config/index.vue'], resolve),
        meta: {
          title: '主机组规划',
          keepAlive: true
        },
        hidden: false
      }
    ]
  }
] 
