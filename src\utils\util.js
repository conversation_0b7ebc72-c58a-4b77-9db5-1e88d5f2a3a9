import { MessageBox } from "element-ui"

export const assignPropValue = (obj, chain, value) => {
  if (!chain.includes('.')) {
    obj[chain] = value
  } else {
    const [first, ...rest] = chain.split('.')
    assignPropValue(obj[first], rest.join('.'), value)
  }
}

/**
 * Asynchronously displays a confirmation message box with customizable text and buttons, and resolves with a boolean value indicating whether the user confirmed or cancelled the action.
 *
 * @param {string} msg - The message to display in the confirmation box. Default is an empty string.
 * @param {string} title - The title of the confirmation box. Default is an empty string.
 * @param {string} type - The type of the confirmation box. Default is 'warning'.
 * @param {string} cancelText - The text to display for the cancel button. Default is '取消'.
 * @param {string} confirmText - The text to display for the confirm button. Default is '确定'.
 * @return {Promise<boolean>} A promise that resolves with a boolean value indicating whether the user confirmed (true) or cancelled (false) the action.
 */
export const confirmPromise = async (msg = '', title = '', type = 'warning', cancelText = '取消', confirmText = '确定') => {
  return await new Promise((resolve) => {
    MessageBox.confirm(msg, title, {
      confirmButtonText: confirmText,
      cancelButtonText: cancelText,
      type
    }).then(() => {
      resolve(true)
    }).catch(() => {
      resolve(false)
    })
  })
}

/**
 * 去掉对象中为空值的属性
 * @param {Object} obj - 需要处理的对象
 * @returns {Object} 处理后的对象
 */
export const removeEmptyProps = (obj, exclusion = []) => {
  return Object.fromEntries(Object.entries(obj).filter(([_, value]) => !!value || exclusion.includes(value)))
}
