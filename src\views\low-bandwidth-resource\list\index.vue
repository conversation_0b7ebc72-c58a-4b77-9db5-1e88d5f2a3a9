<template>
  <el-card>
    <div class="flex-container">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="管理IP">
          <el-input
            v-model="searchForm.ip"
            placeholder="请输入管理IP"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="大区">
          <el-select
            v-model="searchForm.area"
            placeholder="请选择大区"
            clearable
            @focus="loadAreaOptions"
            :loading="areaLoading"
          >
            <el-option
              v-for="item in areaOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="省份">
          <el-select
            v-model="searchForm.province"
            placeholder="请选择省份"
            clearable
            @focus="loadProvinceOptions"
            :loading="provinceLoading"
          >
            <el-option
              v-for="item in provinceOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="资源类型">
          <el-select
            v-model="searchForm.resource_type"
            placeholder="请选择资源类型"
            clearable
          >
            <el-option label="ACDN" value="ACDN"></el-option>
            <el-option label="LCDN" value="LCDN"></el-option>
            <el-option label="PCDN" value="PCDN"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="模块名称">
          <el-select
            v-model="searchForm.module_name"
            placeholder="请选择模块名称"
            clearable
            @focus="loadModuleNameOptions"
            :loading="moduleNameLoading"
          >
            <el-option
              v-for="item in moduleNameOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="设备编码">
          <el-input
            v-model="searchForm.asset_id"
            placeholder="请输入设备编码"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="主机状态">
          <el-select
            v-model="searchForm.host_state"
            placeholder="请选择主机状态"
            clearable
          >
            <el-option label="硬件故障" value="0"></el-option>
            <el-option label="正常" value="1"></el-option>
            <el-option label="空闲" value="2"></el-option>
            <el-option label="删除" value="3"></el-option>
            <el-option label="软件故障" value="4"></el-option>
            <el-option label="系统故障(作废)" value="5"></el-option>
            <el-option label="网络故障(作废)" value="6"></el-option>
            <el-option label="磁盘故障(作废)" value="7"></el-option>
            <el-option label="维修中(作废)" value="8"></el-option>
            <el-option label="轻故" value="9"></el-option>
            <el-option label="待报废" value="10"></el-option>
            <el-option label="已报废" value="11"></el-option>
            <el-option label="交维中" value="12"></el-option>
            <el-option label="报废中" value="13"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="Lake">
          <el-input
            v-model="searchForm.lake"
            placeholder="请输入Lake"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="计费类型">
          <el-select
            v-model="searchForm.account_type"
            placeholder="请选择计费类型"
            clearable
          >
            <el-option label="免费" value="1"></el-option>
            <el-option label="分成" value="2"></el-option>
            <el-option label="月95峰值" value="3"></el-option>
            <el-option label="月平均带宽" value="4"></el-option>
            <el-option label="买断" value="5"></el-option>
            <el-option label="采购" value="6"></el-option>
            <el-option label="混合" value="7"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="资源分组">
          <el-input
            v-model="searchForm.resource_group"
            placeholder="请输入资源分组"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="资源层级">
          <el-select
            v-model="searchForm.resource_level"
            placeholder="请选择资源层级"
            clearable
          >
            <el-option label="边缘" :value="1"></el-option>
            <el-option label="一层父" :value="2"></el-option>
            <el-option label="二层父" :value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="供应商">
          <el-select
            v-model="searchForm.supplier"
            placeholder="请选择供应商"
            clearable
            @focus="loadSupplierOptions"
            :loading="supplierLoading"
          >
            <el-option
              v-for="item in supplierOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="运营商">
          <el-select
            v-model="searchForm.isp"
            placeholder="请选择运营商"
            clearable
            @focus="loadIspOptions"
            :loading="ispLoading"
          >
            <el-option
              v-for="item in ispOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="VIP业务标签">
          <el-input
            v-model="searchForm.vip_biz_tags"
            placeholder="请输入VIP业务标签"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="解析组">
          <el-input
            v-model.trim="searchForm.parse_groups"
            placeholder="请输入解析组"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="可用线路数量">
          <div class="range-input">
            <el-input
              v-model.number="searchForm.available_line_count_min"
              placeholder="最小值"
              clearable
              type="number"
            ></el-input>
            <span class="range-separator">-</span>
            <el-input
              v-model.number="searchForm.available_line_count_max"
              placeholder="最大值"
              clearable
              type="number"
            ></el-input>
          </div>
        </el-form-item>
        <el-form-item label="总线路数量">
          <div class="range-input">
            <el-input
              v-model.number="searchForm.line_count_min"
              placeholder="最小值"
              clearable
              type="number"
            ></el-input>
            <span class="range-separator">-</span>
            <el-input
              v-model.number="searchForm.line_count_max"
              placeholder="最大值"
              clearable
              type="number"
            ></el-input>
          </div>
        </el-form-item>
        <el-form-item label="单线路带宽(M)">
          <div class="range-input">
            <el-input
              v-model="searchForm.single_line_up_bw_min"
              placeholder="最小值"
              clearable
              type="number"
            ></el-input>
            <span class="range-separator">-</span>
            <el-input
              v-model="searchForm.single_line_up_bw_max"
              placeholder="最大值"
              clearable
              type="number"
            ></el-input>
          </div>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
      <div>
        <el-button plain @click="handleColumnConfig">字段配置</el-button>
        <el-button
          plain
          @click="handleBatchExport"
          :loading="batchExportLoading"
          >批量导出</el-button
        >
      </div>
    </div>

    <el-table
      :data="tableData"
      v-loading="loading"
      border
      class="table-style"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection"></el-table-column>
      <el-table-column
        v-if="visibleColumns.includes('ip')"
        prop="ip"
        label="管理IP"
        align="center"
        width="140"
      ></el-table-column>
      <el-table-column
        v-if="visibleColumns.includes('area')"
        prop="area"
        label="大区"
        align="center"
        width="80"
      ></el-table-column>
      <el-table-column
        v-if="visibleColumns.includes('province')"
        prop="province"
        label="省份"
        align="center"
        width="100"
      ></el-table-column>
      <el-table-column
        v-if="visibleColumns.includes('city')"
        prop="city"
        label="城市"
        align="center"
        width="100"
      ></el-table-column>
      <el-table-column
        v-if="visibleColumns.includes('resource_type')"
        prop="resource_type"
        label="资源类型"
        align="center"
        width="100"
      ></el-table-column>
      <el-table-column
        v-if="visibleColumns.includes('module_name')"
        prop="module_name"
        label="模块名称"
        align="center"
        width="120"
      ></el-table-column>
      <el-table-column
        v-if="visibleColumns.includes('asset_id')"
        prop="asset_id"
        label="设备编码"
        align="center"
        width="140"
      ></el-table-column>
      <el-table-column
        v-if="visibleColumns.includes('sn')"
        prop="sn"
        label="SN"
        align="center"
        width="140"
      ></el-table-column>
      <el-table-column
        v-if="visibleColumns.includes('host_state')"
        label="主机状态"
        align="center"
        width="100"
      >
        <template slot-scope="scope">
          {{ formatHostState(scope.row.host_state) }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="visibleColumns.includes('lake')"
        prop="lake"
        label="Lake"
        align="center"
        width="140"
      ></el-table-column>
      <el-table-column
        v-if="visibleColumns.includes('account_type')"
        label="计费类型"
        align="center"
        width="100"
      >
        <template slot-scope="scope">
          {{ formatAccountType(scope.row.account_type) }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="visibleColumns.includes('resource_group')"
        prop="resource_group"
        label="资源分组"
        align="center"
        width="140"
      ></el-table-column>
      <el-table-column
        v-if="visibleColumns.includes('resource_level')"
        label="资源层级"
        align="center"
        width="100"
      >
        <template slot-scope="scope">
          {{ formatResourceLevel(scope.row.resource_level) }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="visibleColumns.includes('parse_groups')"
        label="服务解析组"
        align="center"
        width="300"
      >
        <template slot-scope="scope">
          <div class="parse-groups">
            <el-tag
            type="info"
              v-for="(itm, idx) in (scope.row.parse_groups || '').split(',').filter(v => !!v)"
              :key="itm + idx"
              >{{ itm }}</el-tag
            >
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="visibleColumns.includes('available_line_count')"
        prop="available_line_count"
        label="可用线路数"
        align="center"
        width="100"
      ></el-table-column>
      <el-table-column
        v-if="visibleColumns.includes('total_line_count')"
        prop="total_line_count"
        label="线路数"
        align="center"
        width="80"
      ></el-table-column>
      <el-table-column
        v-if="visibleColumns.includes('abnormal_line_count')"
        prop="abnormal_line_count"
        label="非正常状态线路数"
        align="center"
        width="80"
      ></el-table-column>
      <el-table-column
        v-if="visibleColumns.includes('single_line_up_bw')"
        label="单线路上行带宽(M)"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          {{ formatBandwidth(scope.row.single_line_up_bw) }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="visibleColumns.includes('available_total_up_bw')"
        label="设备总可用上行带宽(M)"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          {{ formatBandwidth(scope.row.available_total_up_bw) }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="visibleColumns.includes('total_up_bw')"
        label="设备总上行带宽(M)"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          {{ formatBandwidth(scope.row.total_up_bw) }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="visibleColumns.includes('rt_netcard_bw')"
        label="实时网卡带宽(M)"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          {{ formatBandwidth(scope.row.rt_netcard_bw) }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="visibleColumns.includes('rt_bw')"
        label="实时业务带宽(M)"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          {{ formatBandwidth(scope.row.rt_bw) }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="visibleColumns.includes('rt_available_bw')"
        label="实时可用建设带宽(M)"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          {{ formatBandwidth(scope.row.rt_available_bw) }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="visibleColumns.includes('supplier')"
        prop="supplier"
        label="供应商"
        align="center"
        width="120"
      ></el-table-column>
      <el-table-column
        v-if="visibleColumns.includes('isp')"
        prop="isp"
        label="运营商"
        align="center"
        width="80"
      ></el-table-column>
      <el-table-column
        v-if="visibleColumns.includes('online_status')"
        prop="online_status"
        label="在线状态"
        align="center"
        width="80"
      >
        <template slot-scope="scope">
          {{ {
            0: '失联',
            1: '在线',
          }[scope.row.online_status] || scope.row.online_status }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="visibleColumns.includes('allow_out_province_ratio')"
        prop="allow_out_province_ratio"
        label="允许出省比例(%)"
        align="center"
        width="80"
      ></el-table-column>
      <el-table-column
        v-if="visibleColumns.includes('vip_biz_tags')"
        prop="vip_biz_tags"
        label="标签"
        align="center"
        min-width="220"
      >
        <template slot-scope="scope">
          <div v-if="!scope.row.vip_biz_tags">{{ "" }}</div>
          <div v-else style="display: flex; gap: 4px; flex-wrap: wrap">
            <el-tag
              v-for="(tag, idx) in scope.row.vip_biz_tags.split(',')"
              :key="tag + idx"
              >{{ tag }}</el-tag
            >
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="visibleColumns.includes('create_time')"
        prop="create_time"
        label="录入时间"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          {{ formatDate(scope.row.create_time) }}
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="pagination.page"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pagination.page_size"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pagination.total"
      class="pagination"
    >
    </el-pagination>

    <!-- 字段配置弹窗 -->
    <el-dialog
      title="字段配置"
      :visible.sync="columnConfigVisible"
      width="600px"
      append-to-body
    >
      <div class="column-config">
        <el-checkbox-group v-model="selectedColumns">
          <div
            class="column-item"
            v-for="column in allColumns"
            :key="column.key"
          >
            <el-checkbox :label="column.key">{{ column.label }}</el-checkbox>
          </div>
        </el-checkbox-group>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="columnConfigVisible = false">取消</el-button>
        <el-button @click="handleResetColumns">重置</el-button>
        <el-button type="primary" @click="handleSaveColumns">确定</el-button>
      </div>
    </el-dialog>
  </el-card>
</template>

<script>
import { removeEmptyProps } from "@/utils/util.js";
import http from "../http.js";
import dayjs from "dayjs";

export default {
  name: "low-bandwidth-resource-list",
  data() {
    return {
      loading: false,
      tableData: [],
      columnConfigVisible: false,
      multipleSelection: [],
      batchExportLoading: false,
      searchForm: {
        ip: "",
        area: "",
        province: "",
        resource_type: "",
        module_name: "",
        asset_id: "",
        host_state: "",
        lake: "",
        account_type: "",
        resource_group: "",
        resource_level: "",
        supplier: "",
        isp: "",
        vip_biz_tags: "",
        parse_groups: "",
        available_line_count_min: "",
        available_line_count_max: "",
        line_count_min: "",
        line_count_max: "",
        single_line_up_bw_min: "",
        single_line_up_bw_max: "",
      },
      // 下拉框选项数据
      areaOptions: [],
      provinceOptions: [],
      moduleNameOptions: [],
      supplierOptions: [],
      ispOptions: [],
      // 下拉框loading状态
      areaLoading: false,
      provinceLoading: false,
      moduleNameLoading: false,
      supplierLoading: false,
      ispLoading: false,
      pagination: {
        total: 0,
        page: 1,
        page_size: 20,
      },
      // 所有可用字段
      allColumns: [
        { key: "ip", label: "管理IP" },
        { key: "area", label: "大区" },
        { key: "province", label: "省份" },
        { key: "city", label: "城市" },
        { key: "resource_type", label: "资源类型" },
        { key: "module_name", label: "模块名称" },
        { key: "asset_id", label: "设备编码" },
        { key: "sn", label: "SN" },
        { key: "host_state", label: "主机状态" },
        { key: "lake", label: "Lake" },
        { key: "account_type", label: "计费类型" },
        { key: "resource_group", label: "资源分组" },
        { key: "resource_level", label: "资源层级" },
        { key: "parse_groups", label: "服务解析组" },
        { key: "available_line_count", label: "可用线路数" },
        { key: "total_line_count", label: "线路数" },
        { key: "abnormal_line_count", label: "非正常状态线路数" },
        { key: "single_line_up_bw", label: "单线路上行带宽(M)" },
        { key: "available_total_up_bw", label: "设备总可用上行带宽(M)" },
        { key: "total_up_bw", label: "设备总上行带宽(M)" },
        { key: "rt_netcard_bw", label: "实时网卡带宽(M)" },
        { key: "rt_bw", label: "实时业务带宽(M)" },
        { key: "rt_available_bw", label: "实时可用建设带宽(M)" },
        { key: "supplier", label: "供应商" },
        { key: "isp", label: "运营商" },
        { key: "online_status", label: "在线状态" },
        { key: "allow_out_province_ratio", label: "允许出省比例" },
        { key: "vip_biz_tags", label: "标签" },
        { key: "create_time", label: "录入时间" },
      ],
      // 默认显示的字段
      defaultColumns: [
        "ip",
        "province",
        "resource_type",
        "module_name",
        "host_state",
        "resource_group",
        "resource_level",
        "available_line_count",
        "single_line_up_bw",
        "available_total_up_bw",
        "supplier",
        "isp",
      ],
      selectedColumns: [],
    };
  },
  computed: {
    visibleColumns() {
      return this.selectedColumns.length > 0
        ? this.selectedColumns
        : this.defaultColumns;
    },
  },
  created() {
    this.loadColumnConfig();
    this.getList();
  },
  methods: {
    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return "-";
      return dayjs(dateString * 1000).format("YYYY-MM-DD HH:mm:ss");
    },
    async getList() {
      this.loading = true;
      try {
        // 创建一个新对象用于请求参数，避免修改原始searchForm
        const requestParams = {
          page: this.pagination.page,
          page_size: this.pagination.page_size,
          ...this.searchForm,
        };

        // 将单线路带宽的单位从Mb转换为b（乘以1000000）
        if (
          requestParams.single_line_up_bw_min ||
          requestParams.single_line_up_bw_min === 0
        ) {
          requestParams.single_line_up_bw_min =
            Number(requestParams.single_line_up_bw_min) > 0
              ? Number(requestParams.single_line_up_bw_min) * 1000000
              : Number(requestParams.single_line_up_bw_min);
        }
        if (
          requestParams.single_line_up_bw_max ||
          requestParams.single_line_up_bw_max === 0
        ) {
          requestParams.single_line_up_bw_max =
            Number(requestParams.single_line_up_bw_max) > 0
              ? Number(requestParams.single_line_up_bw_max) * 1000000
              : Number(requestParams.single_line_up_bw_max);
        }

        const res = await http.getLowBandwidthResourceList(
          removeEmptyProps(requestParams, [0])
        );
        this.tableData = res?.data?.items || [];
        this.pagination.total = res?.data?.total || 0;
      } catch (error) {
        console.error(error);
      } finally {
        this.loading = false;
      }
    },
    handleSizeChange(val) {
      this.pagination.page_size = val;
      this.pagination.page = 1;
      this.getList();
    },
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.getList();
    },
    handleSearch() {
      this.pagination.page = 1;
      this.getList();
    },
    handleReset() {
      this.searchForm = {
        ip: "",
        area: "",
        province: "",
        resource_type: "",
        module_name: "",
        asset_id: "",
        host_state: "",
        lake: "",
        account_type: "",
        resource_group: "",
        resource_level: "",
        supplier: "",
        isp: "",
        vip_biz_tags: "",
        parse_groups: "",
        available_line_count_min: "",
        available_line_count_max: "",
        line_count_min: "",
        line_count_max: "",
        single_line_up_bw_min: "",
        single_line_up_bw_max: "",
      };
      this.pagination.page = 1;
      this.getList();
    },
    // 字段配置相关方法
    handleColumnConfig() {
      this.selectedColumns = [...this.visibleColumns];
      this.columnConfigVisible = true;
    },
    async handleBatchExport() {
      if (!this.multipleSelection?.length) {
        return this.$message.warning("请先选择数据！");
      }
      try {
        this.batchExportLoading = true;
        const res = await http.batchExport({
          ips: this.multipleSelection.map((itm) => itm.ip),
        });
        console.log("res = ", res);
        if (res) {
          // 处理文件下载
          const blob = new Blob([res], {
            type: "text/csv;charset=utf-8",
          });
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement("a");
          link.href = url;

          // 根据导出类型设置文件名
          let fileName = `小带宽资源设备信息_export_${+new Date()}`;
          link.download = `${fileName}.csv`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);
          this.$message.success(`小带宽资源设备信息导出成功`);
        }
      } catch (err) {
        console.warn(err);
      } finally {
        this.batchExportLoading = false;
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = JSON.parse(JSON.stringify(val));
    },
    handleSaveColumns() {
      localStorage.setItem(
        "lowBandwidthResourceColumns",
        JSON.stringify(this.selectedColumns)
      );
      this.columnConfigVisible = false;
    },
    handleResetColumns() {
      this.selectedColumns = [...this.defaultColumns];
    },
    loadColumnConfig() {
      const savedColumns = localStorage.getItem("lowBandwidthResourceColumns");
      if (savedColumns) {
        try {
          this.selectedColumns = JSON.parse(savedColumns);
        } catch (e) {
          this.selectedColumns = [...this.defaultColumns];
        }
      } else {
        this.selectedColumns = [...this.defaultColumns];
      }
    },
    // 格式化方法
    formatBandwidth(value) {
      if (value === -1 || value === null || value === undefined) {
        return "-";
      }
      // 将bps转换为Mbps
      return Math.round(value / 1000000);
    },
    formatHostState(state) {
      const stateMap = {
        0: "硬件故障",
        1: "正常",
        2: "空闲",
        3: "删除",
        4: "软件故障",
        5: "系统故障(作废)",
        6: "网络故障(作废)",
        7: "磁盘故障(作废)",
        8: "维修中(作废)",
        9: "轻故",
        10: "待报废",
        11: "已报废",
        12: "交维中",
        13: "报废中",
      };
      return stateMap[state] || state;
    },
    formatAccountType(type) {
      const typeMap = {
        1: "免费",
        2: "分成",
        3: "月95峰值",
        4: "月平均带宽",
        5: "买断",
        6: "采购",
        7: "混合",
      };
      return typeMap[type] || type;
    },
    formatResourceLevel(level) {
      const levelMap = {
        1: "边缘",
        2: "一层父",
        3: "二层父",
      };
      return levelMap[level] || '';
    },
    // 加载下拉框选项数据的方法
    async loadAreaOptions() {
      if (this.areaOptions.length > 0) return;
      this.areaLoading = true;
      try {
        const res = await http.getAreaParams();
        this.areaOptions =
          res?.data?.map((item) => ({
            label: item.name || item.label || item,
            value: item.value || item.code || item,
          })) || [];
      } catch (error) {
        console.error("获取大区数据失败:", error);
      } finally {
        this.areaLoading = false;
      }
    },
    async loadProvinceOptions() {
      this.provinceLoading = true;
      try {
        const res = await http.getProvinceParams();
        this.provinceOptions =
          res?.data?.map((item) => ({
            label: item.name || item.label || item,
            value: item.value || item.code || item,
          })) || [];
      } catch (error) {
        console.error("获取省份数据失败:", error);
      } finally {
        this.provinceLoading = false;
      }
    },
    async loadModuleNameOptions() {
      this.moduleNameLoading = true;
      try {
        const res = await http.getModuleNameParams();
        this.moduleNameOptions =
          res?.data?.map((item) => ({
            label: item.name || item.label || item,
            value: item.value || item.code || item,
          })) || [];
      } catch (error) {
        console.error("获取模块名称数据失败:", error);
      } finally {
        this.moduleNameLoading = false;
      }
    },
    async loadSupplierOptions() {
      this.supplierLoading = true;
      try {
        const res = await http.getSupplierParams();
        this.supplierOptions =
          res?.data?.map((item) => ({
            label: item.name || item.label || item,
            value: item.value || item.code || item,
          })) || [];
      } catch (error) {
        console.error("获取供应商数据失败:", error);
      } finally {
        this.supplierLoading = false;
      }
    },
    async loadIspOptions() {
      this.ispLoading = true;
      try {
        const res = await http.getIspParams();
        this.ispOptions =
          res?.data?.map((item) => ({
            label: item.name || item.label || item,
            value: item.value || item.code || item,
          })) || [];
      } catch (error) {
        console.error("获取运营商数据失败:", error);
      } finally {
        this.ispLoading = false;
      }
    },
  },
};
</script>

<style scoped>
.search-form .el-input {
  width: 200px;
}

.search-form .el-select {
  width: 200px;
}

.flex-container {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
}

.table-style {
  margin-bottom: 20px;
}

.pagination {
  text-align: center;
}

.parse-groups {
  text-align: left;
  font-size: 12px;
  line-height: 1.5;
  word-break: break-all;
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.column-config {
  max-height: 400px;
  overflow-y: auto;
}

.column-item {
  margin-bottom: 10px;
  padding: 5px 0;
}

.dialog-footer {
  text-align: right;
}

.range-input {
  display: flex;
  align-items: center;
  width: 240px;
}

.range-input .el-input {
  width: 180px;
}

.range-separator {
  margin: 0 5px;
}
</style>
