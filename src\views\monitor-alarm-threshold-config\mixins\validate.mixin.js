export default {
  filters: {},
  components: {},
  data() {
    return {
      rules: {
        // 域名
        domain: [
          { required: true, message: "请输入域名", trigger: "change"}
        ],
        // 持续异常时间
        persistentAbnormal: [
          { required: true, message: "请输入", trigger: "blur"},
          { trigger: "blur", pattern: /^[0-9]*$/, message: "请输入[0~10000]之间的整数" },
          {
            validator: (rule, value, callback) => {
              const data = parseInt(value)
              if (data < 0 || data > 10000) {
                callback(new Error("请输入[0~10000]之间的整数"));
              } else callback();
            },
            trigger: ["blur", "change"],
          },
        ],
        // 首帧记录数（ms）
        firstFrameCount: [
          { required: true, message: "请输入", trigger: "blur"},
          { trigger: "blur", pattern: /^[0-9]*$/, message: "请输入[30~50000]之间的整数" },
          { required: true, validator: this.commonValid, trigger: "blur" },
        ],
        // 首帧时长阈值
        firstFrameTimeCostThre: [
          { required: true, message: "请输入", trigger: "blur"},
          { trigger: "blur", pattern: /^[0-9]*$/, message: "请输入[200~3000]之间的整数" },
          {
            validator: (rule, value, callback) => {
              const data = parseInt(value)
              if (data < 200 || data > 3000) {
                callback(new Error("请输入[200~3000]之间的整数"));
              } else callback();
            },
            trigger: ["blur", "change"],
          },
        ],
        // 首帧突增比例阈值
        firstFrameIncreasePercentThre: [
          { required: true, message: "请输入", trigger: "blur"},
          { trigger: "blur", pattern: /^[0-9]*$/, message: "请输入[5~200]之间的整数" },
          {
            validator: (rule, value, callback) => {
              const data = parseInt(value)
              if (data < 5 || data > 200) {
                callback(new Error("请输入[5~200]之间的整数"));
              } else callback();
            },
            trigger: ["blur", "change"],
          },
        ],


        // 渲染百秒卡顿时长记录数
        videoRenderLagTimeCostCount: [
          { required: true, message: "请输入", trigger: "blur"},
          { trigger: "blur", pattern: /^[0-9]*$/, message: "请输入[30~50000]之间的整数" },
          { required: true, validator: this.commonValid, trigger: "blur" },
        ],
        // 渲染百秒卡顿时长阈值
        videoRenderLagTimeCostThre: [
          { required: true, message: "请输入", trigger: "blur"},
          { trigger: "blur", pattern: /^[0-9]*$/, message: "请输入[1000~20000]之间的整数" },
          {
            validator: (rule, value, callback) => {
              const data = parseInt(value)
              if (data < 1000 || data > 20000) {
                callback(new Error("请输入[1000~20000]之间的整数"));
              } else callback();
            },
            trigger: ["blur", "change"],
          },
        ],
        // 渲染百秒卡顿时长突增比例阈值
        videoRenderLagTimeCostIncreasePercentThre: [
          { required: true, message: "请输入", trigger: "blur"},
          { trigger: "blur", pattern: /^[0-9]*$/, message: "请输入[5~200]之间的整数" },
          {
            validator: (rule, value, callback) => {
              const data = parseInt(value)
              if (data < 5 || data > 200) {
                callback(new Error("请输入[5~200]之间的整数"));
              } else callback();
            },
            trigger: ["blur", "change"],
          },
        ],
        // 渲染百秒卡顿次数记录数
        videoRenderLagCount: [
          { required: true, message: "请输入", trigger: "blur"},
          { trigger: "blur", pattern: /^[0-9]*$/, message: "请输入[30~50000]之间的整数" },
          { required: true, validator: this.commonValid, trigger: "blur" },
        ],
        // 渲染百秒卡顿次数阈值
        videoRenderLagCountThre: [
          { required: true, message: "请输入", trigger: "blur"},
          { trigger: "blur", pattern: /^[0-9]*$/, message: "请输入[2~20]之间的整数" },
          {
            validator: (rule, value, callback) => {
              const data = parseInt(value)
              if (data < 2 || data > 20) {
                callback(new Error("请输入[2~20]之间的整数"));
              } else callback();
            },
            trigger: ["blur", "change"],
          },
        ],
        // 渲染百秒卡顿次数突增比例阈值
        videoRenderLagCountIncreasePercentThre: [
          { required: true, message: "请输入", trigger: "blur"},
          { trigger: "blur", pattern: /^[0-9]*$/, message: "请输入[5~200]之间的整数" },
          {
            validator: (rule, value, callback) => {
              const data = parseInt(value)
              if (data < 5 || data > 200) {
                callback(new Error("请输入[5~200]之间的整数"));
              } else callback();
            },
            trigger: ["blur", "change"],
          },
        ],
        // review拉流记录数
        newSumPlayCount: [
          { required: true, message: "请输入", trigger: "blur"},
          { trigger: "blur", pattern: /^[0-9]*$/, message: "请输入[30~50000]之间的整数" },
          { required: true, validator: this.commonValid, trigger: "blur" },
        ],
        // review拉流成功率阈值
        newSumPlaySuccessRateThre: [
          { required: true, message: "请输入", trigger: "blur"},
          { trigger: "blur", pattern: /^[0-9]*$/, message: "请输入[70~100]之间的整数" },
          {
            validator: (rule, value, callback) => {
              const data = parseInt(value)
              if (data < 70 || data > 100) {
                callback(new Error("请输入[70~100]之间的整数"));
              } else callback();
            },
            trigger: ["blur", "change"],
          },
        ],
        // review拉流成功率突降比例阈值
        newSumPlaySuccessRateDecreasePercentThre: [
          { required: true, message: "请输入", trigger: "blur"},
          { trigger: "blur", pattern: /^[0-9]*$/, message: "请输入[5~50]之间的整数" },
          {
            validator: (rule, value, callback) => {
              const data = parseInt(value)
              if (data < 5 || data > 50) {
                callback(new Error("请输入[5~50]之间的整数"));
              } else callback();
            },
            trigger: ["blur", "change"],
          },
        ],
        // 首包记录数
        firstPackageCount: [
          { required: true, message: "请输入", trigger: "blur"},
          { trigger: "blur", pattern: /^[0-9]*$/, message: "请输入[30~50000]之间的整数" },
          { required: true, validator: this.commonValid, trigger: "blur" },
        ],
        // 首包时间阈值
        firstPackageTimeCostThre: [
          { required: true, message: "请输入", trigger: "blur"},
          { trigger: "blur", pattern: /^[0-9]*$/, message: "请输入[10~3000]之间的整数" },
          {
            validator: (rule, value, callback) => {
              const data = parseInt(value)
              if (data < 10 || data > 3000) {
                callback(new Error("请输入[10~3000]之间的整数"));
              } else callback();
            },
            trigger: ["blur", "change"],
          },
        ],
        // 首包时间突增比例阈值
        firstPackageIncreasePercentThre: [
          { required: true, message: "请输入", trigger: "blur"},
          { trigger: "blur", pattern: /^[0-9]*$/, message: "请输入[5~200]之间的整数" },
          {
            validator: (rule, value, callback) => {
              const data = parseInt(value)
              if (data < 5 || data > 200) {
                callback(new Error("请输入[5~200]之间的整数"));
              } else callback();
            },
            trigger: ["blur", "change"],
          },
        ],
        // 响应的302请求数
        response302QueryCount: [
          { required: true, message: "请输入", trigger: "blur"},
          { trigger: "blur", pattern: /^[0-9]*$/, message: "请输入[30~50000]之间的整数" },
          { required: true, validator: this.commonValid, trigger: "blur" },
        ],
        // 指定省份运营商收到302请求数比例阈值
        ispProvince302QueryPercentThre: [
          { required: true, message: "请输入", trigger: "blur"},
          { trigger: "blur", pattern: /^[0-9]*$/, message: "请输入[50~100]之间的整数" },
          {
            validator: (rule, value, callback) => {
              const data = parseInt(value)
              if (data < 50 || data > 100) {
                callback(new Error("请输入[50~100]之间的整数"));
              } else callback();
            },
            trigger: ["blur", "change"],
          },
        ],
        // 指定省份运营商收到302请求数比例突降阈值
        ispProvince302QueryDecreasePercentThre: [
          { required: true, message: "请输入", trigger: "blur"},
          { trigger: "blur", pattern: /^[0-9]*$/, message: "请输入[5~50]之间的整数" },
          {
            validator: (rule, value, callback) => {
              const data = parseInt(value)
              if (data < 5 || data > 50) {
                callback(new Error("请输入[5~50]之间的整数"));
              } else callback();
            },
            trigger: ["blur", "change"],
          },
        ],
      }
    };
  },
  methods: {
    async commonValid(rule, value, callback) {
      const data = parseInt(value)
      if (data < 30 || data > 50000) {
        callback(new Error("请输入[30~50000]之间的整数"));
      } else callback();
      return callback();
    },
  }
}
