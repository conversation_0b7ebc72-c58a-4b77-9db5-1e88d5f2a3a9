import http from '@/api/http.js'
import timeFormat from '@/utils/timeFormat'
import { download } from "@/utils/downloadCsv";

export default {
  filters: {
    timeFormat: timeFormat
  },
  components: {
    download
  },
  data() {
    return {};
  },
  methods: {
    handleSearch() {
      this.paging.page = 1;
      this.search();
    },
    search() {
      if (this.queryForm.template_ids && this.queryForm.template_ids.length === 0) {
        this.$message.warning("资源池不能为空")
        return;
      }
      if (this.timeRange === null) {
        this.$message.warning("请先选择时间")
        return;
      }
      const currentTime = new Date().getTime(); // 返回当前时间的毫秒表示
      let enddate = this.timeRange ? Number(new Date(this.timeRange[1]).getTime()) : null
      if (enddate > currentTime) {
        this.$message.warning("结束时间不能超过当前时间")
        return;
      }
      if (this.queryForm.include_future_lake === '') {
        this.$message.warning("请选择考虑上下架节点")
        return;
      }
      if (this.queryForm.lake_weight === '') {
        this.$message.warning("请输入Lake带宽比例")
        return;
      }
      if (this.queryForm.lake_weight && typeof this.queryForm.lake_weight !== 'number') {
        this.$message.warning("Lake带宽比例只能输入数字")
        return;
      }
      if (this.queryForm.lake_weight < 0 || this.queryForm.lake_weight > 100) {
        this.$message.warning("Lake带宽比例取值范围：0 ~ 100")
        return;
      }
      if (this.queryForm.host_group_weight === '') {
        this.$message.warning("请输入组能力比例")
        return;
      }
      if (this.queryForm.host_group_weight && typeof this.queryForm.host_group_weight !== 'number') {
        this.$message.warning("组能力比例只能输入数字")
        return;
      }
      if (this.queryForm.host_group_weight < 0 || this.queryForm.host_group_weight > 100) {
        this.$message.warning("组能力比例取值范围：0 ~ 100")
        return;
      }
      this.loading = true
      this.$eventBus.$emit("loadingData", true);
      let vm = this
      let startTime = this.timeRange ? Number(new Date(this.timeRange[0]).getTime().toString().substr(0, 10)) : ""
      let endTime = this.timeRange ? Number(new Date(this.timeRange[1]).getTime().toString().substr(0, 10)) : ""
      let params = {
        ...this.queryForm,
        start_time: startTime,
        end_time: endTime,
        page: vm.paging.page,
        page_size: vm.paging.page_size
      }
      http.post(`/sda/redundancy/host_group`, params)
      .then(response => {
        if (response && response.code === 100000) {
          this.loading = false
          this.$eventBus.$emit("loadingData", false);
          let edgeTableData = response && response.data && response.data.edge
          let firstTableData = response && response.data && response.data.parent_first
          let secondTableData = response && response.data &&  response.data.parent_second
          this.$eventBus.$emit("edgeTableData", edgeTableData);
          this.$eventBus.$emit("firstTableData", firstTableData);
          this.$eventBus.$emit("secondTableData", secondTableData);
        }
      })
      .finally(() => {
        this.loading = false
        this.$eventBus.$emit("loadingData", false);
      })
    },
    exportPart() {
      let timestamp = new Date().getTime()
      let time = timeFormat(timestamp)
      let downloadTime = time.replace(/[-:]/g, '')
      this.exportExcelInfo.excelName = this.exportPartInfo.excelName+'-' + downloadTime + '.xlsx'
      this.exportExcelInfo.excelId = this.exportPartInfo.excelId
      // 需要延时调导出方法，为了等待数据初始化到列表中
      setTimeout(() => {
        this.$refs.myChild.exportExcel()
      }, 100)
    },
    exportAll() {
      let str = `大区,省份,Lake名称,组名称,资源分组,组层级,运营商,Lake出口带宽,rs数,正常rs数,内存,cpu核数,磁盘,额定单机,健康值,组额定能力,组动态额定,组额定qps,组业务带宽,组折算带宽,峰值qps,组能力冗余,组动态能力冗余,组冗余qps,Lake带宽冗余,是否存在无效字段\n`
      this.filterList.forEach((item => {
        str += item.area_view + ",";
        str += item.province + ",";
        str += item.lake_name + ",";
        str += item.host_group + ",";
        str += `${"\"" + item.resource_groups + "\"" }` + ",";
        str += this.hg_level_map[item.hg_level] + ",";
        str += item.isp + ",";
        str += item.lake_upper_limit_bw + ",";
        str += item.rip_count + ",";
        str += item.healthy_rip_count + ",";
        str += item.memory + ",";
        str += item.cpu + ",";
        str += item.disk + ",";
        str += item.rip_rated_bw + ",";
        str += item.qfc + ",";
        str += item.hg_rated_bw + ",";
        str += item.hg_dyn_rated_bw + ",";
        str += item.hg_rated_qps + ",";
        str += item.hg_cur_bw + ",";
        str += item.hg_dis_cur_bw + ",";
        str += item.hg_peak_qps + ",";
        str += item.hg_redu_bw + ",";
        str += item.hg_dyn_redu_bw + ",";
        str += item.hg_redu_qps + ",";
        str += item.lake_redu_bw + ",";
        str += this.has_invalid_field_map[item.has_invalid_field] + ",\n";
      }))
      let timestamp = new Date().getTime()
      let time = timeFormat(timestamp)
      let downloadTime = time.replace(/[-:]/g, '')
      let name = this.exportAllName + '-' + downloadTime
      download(name, str)
    },
  }
}
