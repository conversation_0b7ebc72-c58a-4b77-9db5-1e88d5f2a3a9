<template>
  <el-card>
    <!-- Lake列表 -->
    <!-- 查询条件 -->
    <el-form :model="searchForm" ref="searchForm" label-position="left" inline>
      <div class="flex-container">
        <el-row>
          <el-form-item label="LAKE名称" prop="lake_id">
            <el-select v-model="searchForm.lake_id" placeholder="请选择LAKE名称" style="width:300px" filterable clearable>
              <el-option v-for="(item, index) in lakeList" :key="index" :label="item.bk_inst_name" :value="item.bk_inst_id"></el-option>
            </el-select>
          </el-form-item>
          
          <el-form-item>
            <el-button size="medium" type="primary" @click="onSearch">查询</el-button>
          </el-form-item>
        </el-row>
        <div>
          <el-button size="medium" @click="openCoverChangePlanDialog">覆盖变更方案生成</el-button>
        </div>
      </div>
    </el-form>

    <el-table :data="tableData" v-loading="querying" border class="table-style">
      <el-table-column type="expand">
        <template slot-scope="child">
          <span v-if="child.row.pv_bw && child.row.pv_bw.length > 0">
            <el-table
              :data="handleTableData(child.row.pv_bw)"
              :span-method="listSpanMethod"
              :cell-style="listCellStyle"
              class="sub-table-style"
            >
              <el-table-column prop="pg_name" label="解析组" align="center"></el-table-column>
              <el-table-column prop="sub_view_name" label="区域" align="center"></el-table-column>
              <el-table-column label="昨日峰值(v4:v6)/M" align="center">
                <template slot-scope="scope">
                  <span>{{ parseBwByteToM(scope.row.v4_bw_peak_yesterday) + ':' + parseBwByteToM(scope.row.v6_bw_peak_yesterday) }}</span>
                </template>
              </el-table-column>
              <el-table-column label="今日峰值(v4:v6)/M" align="center">
                <template slot-scope="scope">
                  <span>{{ parseBwByteToM(scope.row.v4_bw_peak_today) + ':' + parseBwByteToM(scope.row.v6_bw_peak_today) }}</span>
                </template>
              </el-table-column>
              <el-table-column label="实时带宽(v4:v6)/M" align="center">
                <template slot-scope="scope">
                  <span>{{ parseBwByteToM(scope.row.v4_bw_current) + ':' + parseBwByteToM(scope.row.v6_bw_current) }}</span>
                </template>
              </el-table-column>
            </el-table>
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="lake_id" label="LakeId" align="center"></el-table-column>
      <el-table-column prop="lake_name" label="Lake名称" align="center"></el-table-column>
      <el-table-column prop="biz_status" label="业务状态" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.biz_status ? 'danger' : 'success'">
            {{ scope.row.biz_status ? '不可用' : '可用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="supplier_name" label="供应商" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.supplier_id">{{ scope.row.supplier_name }} ({{ scope.row.supplier_code }})</span>
        </template>
      </el-table-column>
      <el-table-column prop="view_id" label="区域Id" align="center"></el-table-column>
      <el-table-column prop="view_name" label="区域名称" align="center"></el-table-column>
      <el-table-column prop="view_name" label="v4:v6" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.v4_ratio + ':' + scope.row.v6_ratio }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="v4_count" label="v4个数" align="center"></el-table-column>
      <el-table-column prop="v6_count" label="v6个数" align="center"></el-table-column>
      <el-table-column prop="lake_upper_bw" label="上限带宽/M" align="center">
        <template slot-scope="scope">
          <span>{{ parseBwByteToM(scope.row.lake_upper_bw) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="live_upper_bw" label="直播上限带宽/M" align="center">
        <template v-slot:header>
          <div>直播上限带宽/M</div>
          <div>(v4:v6)</div>
        </template>
        <template slot-scope="scope">
          <span>{{ parseBwByteToM(scope.row.live_upper_v4bw) + ':' + parseBwByteToM(scope.row.live_upper_v6bw) }}</span>
          <!-- <span>{{ scope.row.live_upper_v4bw + ':' + scope.row.live_upper_v6bw }}</span> -->
        </template>
      </el-table-column>
      <el-table-column prop="vod_upper_bw" label="点播上限带宽/M" align="center">
        <template v-slot:header>
          <div>点播上限带宽/M</div>
          <div>(v4:v6)</div>
        </template>
        <template slot-scope="scope">
          <span>{{ parseBwByteToM(scope.row.vod_upper_v4bw) + ':' + parseBwByteToM(scope.row.vod_upper_v6bw) }}</span>
          <!-- <span>{{ scope.row.vod_upper_v4bw + ':' + scope.row.vod_upper_v6bw }}</span> -->
        </template>
      </el-table-column>
      <el-table-column prop="cover_gen_switch" label="自动生成覆盖变更方案" align="center">
        <template slot-scope="scope">
          <el-switch :value="scope.row.cover_gen_switch === 1" inactive-color="#aab2bf" disabled />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="180">
        <template slot-scope="scope">
          <div>
            <el-button type="text" @click="handleEdit(scope.row)">修改</el-button>
            <el-button type="text" @click="handleDelete(scope.row)">删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-row style="text-align: center; padding-top: 20px">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.page_size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      ></el-pagination>
    </el-row>

    <el-row>
      <lake-dialog
        v-if="editDialog"
        :is-edit="true"
        :rowData="rowData"
        @close="editDialog = false"
        @refresh="onSearch"
      ></lake-dialog>
      <!-- 跑高 lake -->
      <run-high-lake-dialog
        v-if="runHighLakeDialogVisible"
        @close="runHighLakeDialogVisible = false"
        @refresh="onSearch"
      ></run-high-lake-dialog>
    </el-row>

  </el-card>
</template>
<script>

import http from "@/api/http.js"
import { mapState } from "vuex";
import lakeDialog from "@/views/lake-config/lake-list/dialog/lakeDialog.vue"
import runHighLakeDialog from "@/views/lake-config/lake-list/dialog/runHighLakeDialog.vue"
import commonMixin from "@/utils/common";

export default {
  name: "lake-list",
  components: {
    lakeDialog,
    runHighLakeDialog
  },
  mixins: [
    commonMixin
  ],
  props: [],
  data() {
    return {
      querying: false,
      tableData: [],
      editDialog: false,
      rowData: {},
      pagination: {
        total: 0,
        page: 1,
        page_size: 20,
      },
      searchForm: {
        lakeName: '',
      },
      runHighLakeDialogVisible: false,
    };
  },
  computed: {
    ...mapState({
      lakeList: (state) => state.baseData.lakeList,
    }),
  },
  created() {},
  watch: {},
  mounted() {
    this.onSearch()
  },
  methods: {
    onSearch() {
      this.pagination.page = 1;
      this.query();
    },
    async query() {
      let params = {
        lake_id: this.searchForm.lake_id,
        page: this.pagination.page,
        page_size: this.pagination.page_size,
      };
      this.querying = true;
      try {
        await http.get(`/sda/lake_view/get`, params).then((res) => {
          this.tableData = res && res.data && res.data.items;
          this.pagination.total = res && res.data && res.data.total;
          this.querying = false;
        });
      } catch (error) {
        this.querying = false;
      } finally {
        this.querying = false;
      }
    },
    handleEdit(row) {
      this.editDialog = true
      this.rowData = structuredClone(row)
    },
    async handleDelete(row) {
      await this.$confirm("确定删除该条数据吗？", "提示", {
        type: "warning",
      })
      const res = await http.delete(`/sda/lake_view/delete/${row.id}`)
      if (res && res.code === 100000) {
        let msg = (res && res.message) || "删除成功"
        this.$message.success(msg);
        this.onSearch()
      }
    },
    handleSizeChange(val) {
      this.pagination.page = 1;
      this.pagination.page_size = val;
      this.query();
    },
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.query();
    },
    openCoverChangePlanDialog() {
      this.runHighLakeDialogVisible = true
    },
    handleTableData(data) {
      const arrData = []
      for (let i = 0; i < data.length; i++) {
        let viewBw = data[i].view_bw
        if (!viewBw) {
          viewBw = [{}]
        }
        for (let j = 0; j < viewBw.length; j++) {
          const info = {
            span_num: j === 0 ? viewBw.length : 0,
            pg_id: data[i].pg_id,
            pg_name: data[i].pg_name,
            view_bw: data[i].view_bw,

            sub_view_id: viewBw[j].view_id,
            sub_view_name: viewBw[j].view_name,
            v4_bw_peak_today: viewBw[j].v4_bw_peak_today,
            v6_bw_peak_today: viewBw[j].v6_bw_peak_today,
            v4_bw_peak_yesterday: viewBw[j].v4_bw_peak_yesterday,
            v6_bw_peak_yesterday: viewBw[j].v6_bw_peak_yesterday,
            v4_bw_current: viewBw[j].v4_bw_current,
            v6_bw_current: viewBw[j].v6_bw_current,
          }
          arrData.push(info)
        }
      }
      return arrData;
    },
    listSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex < 1) {
        if (row.span_num > 0) {
          return {
            rowspan: row.span_num,
            colspan: 1,
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          };
        }
      }
    },
    listCellStyle({ row, column, rowIndex, columnIndex }) {
      if (columnIndex < 1) {
        return {
          backgroundColor: "transparent",
        };
      }
    },
  },
};
</script>
<style scoped lang="scss">
.flex-container {
  display: flex;
  justify-content: space-between;
}
.table-style {
  /deep/ .el-table__header {
    th {
      background: #cdd0d3;
    }
  }
}
.sub-table-style {
  /deep/ .el-table__header {
    th {
      background: #f6f7f8 !important;
    }
  }
}
</style>
