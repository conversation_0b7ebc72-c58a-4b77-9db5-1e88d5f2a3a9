<template>
  <el-dialog :title="isEdit ? '修改认证信息' : '新增'" :close-on-click-modal="false" :close-on-press-escape="false" :visible.sync="dialogVisible" width="500px" append-to-body @close="handleClose">
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="供应商" prop="supplier_id">
        <el-select v-model="form.supplier_id" placeholder="请选择供应商" style="width: 100%" @change="handleSupplierChange">
          <el-option
            v-for="supplier in supplierList"
            :key="supplier.id"
            :label="`${supplier.supplier_name} (${supplier.supplier_code})`"
            :value="supplier.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="IP段" prop="ips">
        <el-select v-model="form.ips" placeholder="请选择IP段" style="width: 100%" :disabled="!form.supplier_id">
          <el-option
            v-for="ip in ipSegmentList"
            :key="ip.value"
            :label="ip.label"
            :value="ip.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="IP生成个数" prop="ip_count">
        <el-input-number v-model="form.ip_count" :min="1" :max="1000" placeholder="请输入" style="width: 100%"></el-input-number>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import http from "../../http"

export default {
  name: "authInfoDialog",
  props: {
    isEdit: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: true,
      loading: false,
      supplierList: [],
      ipSegmentList: [],
      form: {
        supplier_id: '',
        ips: '',
        ip_count: 1
      },
      rules: {
        supplier_id: [
          { required: true, message: '请选择供应商', trigger: 'change' }
        ],
        ips: [
          { required: true, message: '请选择IP段', trigger: 'change' }
        ],
        ip_count: [
          { required: true, message: '请输入IP生成个数', trigger: 'blur' },
          { type: 'number', min: 1, max: 1000, message: 'IP生成个数必须在1-1000之间', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    ispList() {
      return this.$store.state.baseData.ispList
    }
  },
  created() {
    this.getSupplierList()
  },
  methods: {
    // 获取供应商列表
    async getSupplierList() {
      try {
        const res = await http.getSupplierList({
          page_size: 1000  // 获取所有供应商
        })
        if (res && res.code === 100000) {
          this.supplierList = res.data?.items || []
        }
      } catch (error) {
        console.error('获取供应商列表失败:', error)
      }
    },
    // 供应商变化时获取对应的IP段
    handleSupplierChange(supplierId) {
      const supplier = this.supplierList.find(s => s.id === supplierId)
      this.ipSegmentList = []
      
      if (supplier) {
        // 处理新格式（ips_list 数组）
        if (supplier.ips_list && Array.isArray(supplier.ips_list)) {
          this.ipSegmentList = supplier.ips_list.map(item => ({
            value: item.ips,
            label: `${item.ips} (${this.getIspName(item.isp_code)})`
          }))
        } 
        // 兼容旧格式（ips 字符串）
        else if (supplier.ips && typeof supplier.ips === 'string') {
          const ipsArray = supplier.ips.split(',').map(ip => ip.trim()).filter(ip => ip)
          this.ipSegmentList = ipsArray.map(ip => ({
            value: ip,
            label: ip
          }))
        }
      }
      
      // 清空已选择的IP段
      this.form.ips = ''
    },
    
    // 根据运营商代码获取中文名称
    getIspName(code) {
      if (!code) return '';
      const isp = this.ispList.find(item => item.code === code);
      return isp ? isp.cn_name : code;
    },
    handleClose() {
      this.$refs.form.resetFields()
      this.$emit('close')
    },
    async handleSubmit() {
      try {
        await this.$refs.form.validate()
        this.loading = true

        const params = {
          supplier_id: this.form.supplier_id,
          ips: this.form.ips,
          ip_count: this.form.ip_count
        }

        const res = await http.addAuthInfo(params)
        if (res && res.code === 100000) {
          this.$message.success('新增成功')
          this.dialogVisible = false
          this.$emit('refresh')
        }
      } catch (error) {
        console.error('新增失败:', error)
        this.$message.error('新增失败')
      } finally {
        this.loading = false
      }
    }
  }
}
</script>
