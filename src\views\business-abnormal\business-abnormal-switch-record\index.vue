<template>
  <el-card>
    <!-- 业务异常切换记录 -->
    <!-- 查询条件 -->
    <el-form :model="searchForm" ref="searchForm" label-position="left" inline>
      <div>
        <el-form-item label="任务ID" prop="task_id">
          <el-input v-model="searchForm.task_id" placeholder="请输入任务ID" style="width:260px" clearable></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="state" class="ml32">
          <el-select v-model.number="searchForm.state" placeholder="请选择状态" filterable clearable style="width:120px">
            <el-option v-for="(item, index) in stateList" :key="index" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="LAKE" prop="lake_id">
          <el-select v-model="searchForm.lake_id" placeholder="请选择LAKE" style="width:260px" filterable clearable>
            <el-option :value="-1" label="ALL" />
            <el-option v-for="(item, index) in lakeList" :key="index" :label="item.bk_inst_name" :value="item.bk_inst_id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="主机组" prop="host_group">
          <el-input v-model="searchForm.host_group" placeholder="请输入主机组" style="width:200px" clearable></el-input>
        </el-form-item>
      </div>
      <div>
        <el-form-item label="解析组" prop="pg_id">
          <el-select v-model="searchForm.pg_id" placeholder="请选择解析组" filterable clearable style="width:260px">
            <el-option :value="-1" label="ALL" />
            <el-option v-for="(item, index) in parseGroupList" :key="index" :label="item.parse_group_name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="SMP任务ID" prop="smp_task_id">
          <el-input
            v-model="searchForm.smp_task_id"
            placeholder="请输入SMP任务ID,多个用英文逗号隔开"
            type="textarea"
            :autosize="{ minRows: 2 }"
            style="width:440px"
            clearable
          ></el-input>
        </el-form-item>
        
        <el-form-item>
          <el-button size="medium" type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
        <span style="float:right">
          <el-button size="medium" @click="batchCancelTask">批量取消任务</el-button>
        </span>
      </div>
    </el-form>

    <el-table
      :data="tableData"
      v-loading="querying"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" align="center" width="55"></el-table-column>
      <el-table-column prop="task_id" label="任务ID" align="center" width="160"></el-table-column>
      <el-table-column prop="lake_name" label="LAKE" align="center"></el-table-column>
      <el-table-column prop="host_group" label="主机组" align="center"></el-table-column>
      <el-table-column prop="dns_system_id" label="DNS系统" align="center"></el-table-column>
      <el-table-column prop="pg_id" label="解析组ID" align="center"></el-table-column>
      <el-table-column prop="pg_name" label="解析组" align="center"></el-table-column>
      <el-table-column prop="view" label="区域" align="center"></el-table-column>
      <el-table-column prop="smp_task_id" label="SMP任务ID" align="center" width="140"></el-table-column>
      <el-table-column prop="create_time" label="创建时间" align="center" width="145">
        <template slot-scope='scope'>
          <span>{{ (scope.row.create_time * 1000) | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="update_time" label="恢复时间" align="center" width="145">
        <template slot-scope='scope'>
          <span>{{ (scope.row.update_time * 1000) | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="operator" label="操作人" align="center"></el-table-column>
      <el-table-column prop="remark" label="切换原因" align="center"></el-table-column>
      <el-table-column prop="state" label="当前状态" align="center">
        <template slot-scope="scope">
          <el-tag slot="reference" v-if="scope.row.state === 0">进行中</el-tag>
          <el-tag slot="reference" v-if="scope.row.state === 1" type="success">恢复</el-tag>
          <el-tag slot="reference" v-if="scope.row.state === 2" type="info">取消</el-tag>
          <el-tag slot="reference" v-if="scope.row.state === 3" type="danger">异常</el-tag>
        </template>
      </el-table-column>
    </el-table>
    <el-row style="text-align: center; padding-top: 20px">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.page_size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      ></el-pagination>
    </el-row>

    <el-row>
      <batch-cancel-task-dialog
        v-if="dialogVisible"
        :multipleSelection="multipleSelection"
        @close="dialogVisible = false"
        @refresh="onSearch"
      ></batch-cancel-task-dialog>
    </el-row>

  </el-card>
</template>
<script>

import http from "@/api/http.js"
import { mapState } from "vuex";
import dateFormat from "@/utils/dateFormat"
import batchCancelTaskDialog from "@/views/business-abnormal/business-abnormal-switch-record/dialog/batchCancelTaskDialog.vue"

export default {
  name: "business-abnormal-switch-record",
  components: {
    batchCancelTaskDialog
  },
  filters: {
    dateFormat: dateFormat
  },
  props: [],
  data() {
    return {
      querying: false,
      dialogVisible: false,
      tableData: [],
      pagination: {
        total: 0,
        page: 1,
        page_size: 20,
      },
      multipleSelection: [],
      stateList: [
        { label: "进行中", value: 0 },
        { label: "恢复", value: 1 },
        { label: "取消", value: 2 },
        { label: "异常", value: 3 }
      ],
      searchForm: {
        task_id: '', // 任务ID
        smp_task_id: '', // SMP任务ID
        lake_id: '',
        host_group: '',
        pg_id: '',
        state: 0,
      },
    };
  },
  computed: {
    ...mapState({
      lakeList: (state) => state.baseData.lakeList,
      parseGroupList: (state) => state.baseData.parseGroupList,
    }),
  },
  created() {},
  watch: {},
  mounted() {
    this.onSearch()
  },
  methods: {
    onSearch() {
      this.pagination.page = 1;
      this.query();
    },
    async query() {
      let params = {
        ...this.searchForm,
        page: this.pagination.page,
        page_size: this.pagination.page_size,
      };
      this.querying = true;
      try {
        await http.get(`/sda/req_abnormal/dispatcher/records`, params).then((res) => {
          this.tableData = res && res.data && res.data.items;
          this.pagination.total = res && res.data && res.data.total;
          this.querying = false;
        });
      } catch (error) {
        this.querying = false;
      } finally {
        this.querying = false;
      }
    },

    handleSelectionChange(val) {
      this.multipleSelection = JSON.parse(JSON.stringify(val));
    },
    // 批量取消任务
    async batchCancelTask() {
      if (!(this.multipleSelection && this.multipleSelection.length > 0)) {
        this.$message.warning("请先选择数据！")
        return;
      }
      this.dialogVisible = true
    },
    handleSizeChange(val) {
      this.pagination.page = 1;
      this.pagination.page_size = val;
      this.query();
    },
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.query();
    },
  },
};
</script>
<style scoped lang="scss">
.btn {
  float: right;
}
.ml32 {
  margin-left: 36px;
}
</style>
