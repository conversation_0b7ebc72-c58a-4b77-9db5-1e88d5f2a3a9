<!--
 * @Description: 页面布局组件
 * @Author: l<PERSON><PERSON><PERSON>@chinatelecom.cn
 * @Date: 2020-04-21 09:55:27
 * @LastEditTime: 2020-04-21 10:34:07
 * @LastEditors: <EMAIL>
 -->
<template>
  <div class="view">
    <div class="view--top" v-if="hasTop">
      <slot name="top"></slot>
    </div>
    <div class="view--header" v-if="hasHeader">
      <div class="view--header__left">
        <slot name="header"></slot>
      </div>
      <div class="view--header__right">
        <slot name="header__right"></slot>
      </div>
    </div>

    <main>
      <slot name="default"></slot>
    </main>
  </div>
</template>

<script>
export default {
  name: 'ViewLayout',
  props: {
    hasHeader: {
      required: false,
      type: Boolean,
      default: true
    },
    hasTop: {
      required: false,
      type: <PERSON>olean,
      default: false
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../assets/styles/var.scss';
.view {
  // padding: 0 14px;
  overflow: auto;
  &--top {
    margin-bottom: 16px;
  }

  &--header {
    min-height: 60px;
    margin-bottom: 16px;
    font-size: 16px;
    color: #606266;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: $bg-color-module;
    padding: 16px;
    box-shadow: 0 0 8px 0 rgba(2,7,30,0.06);
    border-radius: $border-radius-primary;

    &__left, &__right {
      display: flex;
      align-items: center;
    }
  }
  main {
    background-color: $bg-color-module;
    box-shadow: 0 0 8px 0 rgba(2,7,30,0.06);
    border-radius: $border-radius-primary;
    padding: 16px;
    margin-bottom: 80px;
    overflow: hidden;
  }
}
</style>