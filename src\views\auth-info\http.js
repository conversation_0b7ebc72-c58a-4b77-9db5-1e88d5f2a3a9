import { ajax } from "@/api/ajax";
import { formPromise } from "@/utils/fetch";

const prefix = "api/v1"
export default {
  // 认证信息查询
  async getAuthInfoList(params) {
    return await formPromise(ajax.get(`${prefix}/sda/auth_gen/get`, {
      params
    }))
  },
  // 认证信息删除
  async deleteAuthInfo(id) {
    return await formPromise(ajax.delete(`${prefix}/sda/auth_gen/delete/${id}`))
  },
  // 认证信息新增
  async addAuthInfo(data) {
    return await formPromise(ajax.post(`${prefix}/sda/auth_gen/create`, data))
  },
  // 自定义认证信息新增
  async addCustomAuthInfo(data) {
    return await formPromise(ajax.post(`${prefix}/sda/auth_gen/custom_create`, data))
  },
  // 认证信息详情
  async getAuthInfoDetail(id) {
    return await formPromise(ajax.get(`${prefix}/sda/auth_gen/detail/${id}`))
  },
  // 认证信息导出
  async exportAuthInfo(data) {
    return await ajax.post(`${prefix}/sda/auth_gen/export`, data, {
      responseType: 'blob'  // 设置响应类型为blob，用于文件下载
    })
  },
  // 权限认证
  async verifyPassword(data) {
    return await formPromise(ajax.post(`${prefix}/sda/auth_gen/verify`, data))
  },
  // 获取供应商列表
  async getSupplierList(params) {
    return await formPromise(ajax.get(`${prefix}/sda/supplier/get`, {
      params
    }))
  },
  // 获取运营商列表
  async getIspList() {
    return await formPromise(ajax.get(`${prefix}/sda/supplier/isp`))
  }
}
