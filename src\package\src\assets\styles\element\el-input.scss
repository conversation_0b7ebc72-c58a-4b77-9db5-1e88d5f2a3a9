@import '../var.scss';
@import '../mixins.scss';

.el-input {
  @include font-text;
  @include is-input;

  &.el-input--prefix {
    .el-input__inner {
      padding-left: 32px;
    }
  }

  .el-input__inner {
    @include global-font-family;

    height: $input-height;
    border-radius: $border-radius-primary;

    &:disabled {
      background-color: $bg-color-disabled;
      color: $font-color-secondary2;
    }

    &:hover, &:active {
      border: 1px solid $color-primary;
    }
  }
}


.el-input__inner, .el-input-group__prepend, .el-input-group__append {
  padding: 6px 14px;
}
.el-input-group--append .el-input__inner {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.el-textarea__inner {
  font-size: $font-size-normal;
  border-radius: $border-radius-primary;
  padding: $input-padding;

  &::placeholder {
    color: $font-color-secondary2;
  }
}
