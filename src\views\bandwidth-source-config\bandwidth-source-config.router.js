import EmptyLayout from "@/package/src/components/EmptyLayout";

export default [
  {
    path: '/bandwidthSourceConfig',
    component: EmptyLayout,
    name: "bandwidthSourceConfig",
    meta: {
      title: "带宽数据源配置",
    },
    children: [
      {
        path: 'lakeBandwidthSourceConfig',
        name: 'lakeBandwidthSourceConfig',
        component: resolve => require(['@/views/bandwidth-source-config/lake-bandwidth-source-config/index.vue'], resolve),
        meta: {
          title: 'Lake带宽数据源配置',
          keepAlive: true
        },
        hidden: false
      },
      {
        path: 'nodeBandwidthSourceConfig',
        name: 'nodeBandwidthSourceConfig',
        component: resolve => require(['@/views/bandwidth-source-config/node-bandwidth-source-config/index.vue'], resolve),
        meta: {
          title: 'Node带宽数据源配置',
          keepAlive: true
        },
        hidden: false
      }
    ]
  }
] 
