import Vue from 'vue'
import {isVNode} from 'element-ui/src/utils/vdom'

// 扫描当前目录下.vue结尾的文件，不扫描子文件夹
const componentsContext = require.context('./', false, /\.vue$/)

// 枚举componentsContext对象的属性(文件名)，结果是一个数组
componentsContext.keys().forEach(component => {
  const componentConfig = componentsContext(component)

  // 兼容 import export 和 require module.export 两种规范
  const comp = componentConfig.default || componentConfig
  Vue.component(comp.name, comp)
})

export default {
  install(Vue) {
    // 包装element-ui原有的notification函数，添加customClass，用于识别不同倾向性的notify。
    const sourceNotification = Vue.prototype.$notify
    const notification = function(options) {
      if (options.type) {
        options.customClass = `${options.customClass || ''} el-notification--${options.type}`
      }
      return sourceNotification(options)
    };
    ['success', 'warning', 'info', 'error'].forEach(type => {
      notification[type] = options => {
        if (typeof options === 'string' || isVNode(options)) {
          options = {
            message: options
          }
        }
        options.type = type
        return notification(options);
      };
    });
    // 将新生成的函数挂载至Vue原型链上，替换原本的函数。
    Vue.prototype.$notify = notification;
  }
}
