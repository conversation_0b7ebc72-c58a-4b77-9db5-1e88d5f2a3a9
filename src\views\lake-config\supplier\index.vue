<template>
  <el-card>
    <div class="flex-container">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <!-- <el-form-item label="ID">
          <el-input v-model="searchForm.id" placeholder="请输入ID" clearable></el-input>
        </el-form-item> -->
        <el-form-item label="供应商名称">
          <el-input
            v-model.trim="searchForm.supplier_name"
            placeholder="请输入供应商名称"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="供应商代码">
          <el-input
            v-model.trim="searchForm.supplier_code"
            placeholder="请输入供应商代码"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="UUID">
          <el-input
            v-model.trim="searchForm.uuid"
            placeholder="请输入UUID"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="账号">
          <el-input
            v-model.trim="searchForm.account"
            placeholder="请输入账号"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button plain type="primary" @click="handleAdd"
            >新增供应商</el-button
          >
        </el-form-item>
        <auth-status />
      </el-form>
    </div>

    <el-table :data="tableData" v-loading="loading" border class="table-style">
      <el-table-column
        prop="id"
        label="ID"
        width="60"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="supplier_name"
        label="供应商名称"
        width="120"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="supplier_code"
        label="供应商代码"
        width="120"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="contact_group_id"
        label="群ID"
        width="80"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="contact_group_name"
        label="群名称"
        width="120"
        align="center"
      ></el-table-column>
      <el-table-column label="联系人信息" align="center" width="200">
        <template slot-scope="scope">
          <div class="contact-info">
            <div>联系人：{{ scope.row.contact || "-" }}</div>
            <div>邮箱：{{ scope.row.email || "-" }}</div>
            <div>电话：{{ scope.row.phone || "-" }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="IP段" align="center" width="400">
        <template slot-scope="scope">
          <div class="ip-segments">
            <div
              v-for="(ipItem, index) in formatIpSegments(
                scope.row.ips_list || scope.row.ips
              )"
              :key="index"
              class="ip-segment-item"
            >
              {{ ipItem.ips }} {{ ipItem.isp_code }}
              {{ ipItem.allow_out_province_ratio }}%
              {{ areaMap[ipItem.area] || ipItem.area || "-" }}
              {{ areaMap[ipItem.province] || ipItem.province || "-" }}
              {{ areaMap[ipItem.city] || ipItem.city || "-" }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="uuid"
        label="UUID"
        width="200"
        align="center"
      ></el-table-column>
      <el-table-column label="账号" width="150" align="center" props="account">
        <template slot-scope="scope">
          <!-- <span v-if="!scope.row.account || scope.row.account === ''">-</span>
          <span v-else-if="!scope.row.isDecrypted">******</span>
          <span v-else>{{ scope.row.account }}</span>
           -->
          {{ scope.row.account }}
        </template>
      </el-table-column>
      <el-table-column label="密码" width="150" align="center">
        <template slot-scope="scope">
          <span v-if="!scope.row.pwd || scope.row.pwd === ''">-</span>
          <span v-else-if="!scope.row.isDecrypted">******</span>
          <span v-else>{{ scope.row.pwd }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" width="80" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
            {{ scope.row.status === 1 ? "启用" : "禁用" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="operator"
        label="操作人"
        align="center"
      ></el-table-column>
      <el-table-column label="操作" align="center" width="350">
        <template slot-scope="scope">
          <el-button
            v-if="!scope.row.isDecrypted"
            type="text"
            size="small"
            @click="handleDecrypt(scope.row)"
            >解密</el-button
          >
          <el-button
            v-else
            type="text"
            size="small"
            @click="handleHide(scope.row)"
            >隐藏</el-button
          >
          <el-button
            type="text"
            size="small"
            @click="handleUpdateAccount(scope.row)"
            >账号修改</el-button
          >
          <el-button type="text" size="small" @click="handleResetPwd(scope.row)"
            >密码重置</el-button
          >
          <el-button
            type="text"
            size="small"
            @click="handleToggleStatus(scope.row)"
            >{{ scope.row.status === 1 ? "禁用" : "启用" }}</el-button
          >
          <el-button type="text" size="small" @click="handleEdit(scope.row)"
            >修改</el-button
          >
          <el-button type="text" size="small" @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="pagination.page"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pagination.page_size"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pagination.total"
      class="pagination"
    ></el-pagination>

    <supplier-dialog
      v-if="dialogVisible"
      :is-edit="isEdit"
      :rowData="rowData"
      @close="dialogVisible = false"
      @refresh="getList"
    ></supplier-dialog>
    <account-update-dialog
      v-if="showAccountDialog"
      :supplierId="selectedRow.id"
      @close="showAccountDialog = false"
      @success="getList"
    ></account-update-dialog>

    <!-- 认证弹窗 -->
    <auth-verify-dialog
      :visible="authDialogVisible"
      :auth-type="authType"
      @close="handleAuthDialogClose"
      @verify-success="handleAuthVerifySuccess"
    />
  </el-card>
</template>

<script>
import { removeEmptyProps } from "@/utils/util.js";
import http from "../http.js";
import supplierDialog from "./dialog/supplierDialog.vue";
import accountUpdateDialog from "./dialog/accountUpdateDialog.vue";
import authMixin from "@/mixins/authMixin.js";
import { decryptPassword } from "@/utils/crypto";

export default {
  name: "supplier",
  mixins: [authMixin],
  components: {
    supplierDialog,
    accountUpdateDialog,
  },
  data() {
    return {
      loading: false,
      tableData: [],
      dialogVisible: false,
      isEdit: false,
      rowData: {},
      showAccountDialog: false,
      selectedRow: {},
      newStatus: null,
      searchForm: {
        id: "",
        supplier_name: "",
        supplier_code: "",
        uuid: "",
        account: "",
      },
      pagination: {
        total: 0,
        page: 1,
        page_size: 20,
      },
    };
  },
  computed: {
    ispList() {
      return this.$store.state.baseData.ispList;
    },
    areaList() {
      return this.$store.state.baseData.areaList;
    },
    areaMap() {
      const map = {};
      if (this.areaList && this.areaList.length) {
        this.areaList.forEach((item) => {
          map[item.area_id] = item.area_cnname;
        });
      }
      console.log("map = ", map);
      return map;
    },
  },
  created() {
    this.getList();
  },
  methods: {
    async getList() {
      this.loading = true;
      try {
        const res = await http.getSupplierList(
          removeEmptyProps({
            page: this.pagination.page,
            page_size: this.pagination.page_size,
            ...this.searchForm,
          })
        );
        this.tableData = (res?.data?.items || []).map((item) => ({
          ...item,
          isDecrypted: false,
          // originalAccount: item.account, // 保存原始加密的账号
          originalPwd: item.pwd, // 保存原始加密的密码
        }));
        this.pagination.total = res?.data?.total || 0;
      } catch (error) {
        console.error(error);
      } finally {
        this.loading = false;
      }
    },
    handleAdd() {
      this.isEdit = false;
      this.rowData = {};
      this.dialogVisible = true;
    },
    handleEdit(row) {
      this.isEdit = true;
      this.rowData = { ...row };
      this.dialogVisible = true;
    },
    async handleDelete(row) {
      try {
        await this.$confirm("确定删除该供应商吗？", "提示", {
          type: "warning",
        });
        const res = await http.deleteSupplier(row.id);
        if (res && res.code === 100000) {
          this.$message.success("删除成功");
          this.getList();
        }
      } catch (error) {
        console.error(error);
      }
    },
    handleSizeChange(val) {
      this.pagination.page_size = val;
      this.pagination.page = 1;
      this.getList();
    },
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.getList();
    },
    handleSearch() {
      this.pagination.page = 1;
      this.getList();
    },
    handleReset() {
      this.searchForm = {
        id: "",
        supplier_name: "",
        supplier_code: "",
        uuid: "",
        account: "",
      };
      this.pagination.page = 1;
      this.getList();
    },
    handleDecrypt(row) {
      this.executeAuthenticatedOperation(
        "decrypt",
        () => this.executeDecrypt(row),
        row
      );
    },
    executeDecrypt(row) {
      try {
        // row.account = decryptPassword(row.account);
        row.pwd = decryptPassword(row.pwd);
        row.isDecrypted = true;
      } catch (error) {
        this.$message.error("解密失败");
      }
    },
    handleUpdateAccount(row) {
      this.selectedRow = row;
      // this.executeAuthenticatedOperation(
      //   "updateAccount",
      //   () => {
      //     this.showAccountDialog = true;
      //   },
      //   row
      // );
      this.showAccountDialog = true;
    },
    async handleResetPwd(row) {
      try {
        await this.$confirm('确定要重置该供应商的密码吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });
        this.selectedRow = row;
        this.executeAuthenticatedOperation(
          "resetPwd",
          async () => {
            try {
              const params = {
                id: row.id,
                verify_pwd: this.$store.state.auth.encryptedPassword,
              };
              const res = await http.resetSupplierPassword(params);
              if (res.code === 100000) {
                this.$message.success("重置成功");
                this.getList();
              }
            } catch (error) {
              console.error("重置失败");
            }
          },
          row
        );
      } catch (error) {
        // 用户取消确认
      }
    },
    handleToggleStatus(row) {
      const newStatus = row.status === 1 ? 2 : 1;
      this.executeAuthenticatedOperation(
        "toggleStatus",
        async () => {
          const params = {
            id: row.id,
            status: newStatus,
            verify_pwd: this.$store.state.auth.encryptedPassword,
          };
          const res = await http.updateSupplierStatus(params);
          if (res.code === 100000) {
            this.$message.success("操作成功");
            this.getList();
          }
        },
        row
      );
    },

    handleHide(row) {
      // 恢复原始加密状态
      // row.account = row.originalAccount;
      row.pwd = row.originalPwd;
      row.isDecrypted = false;
    },
    formatIpSegments(ipsData) {
      if (!ipsData) return [];

      // 如果是新格式（数组）
      if (Array.isArray(ipsData)) {
        return ipsData.map((item) => {
          return {
            ips: item.ips || "",
            isp_code: item.isp_code || "",
            allow_out_province_ratio: item.allow_out_province_ratio || 0,
            area: item.area,
            province: item.province,
            city: item.city,
          };
        });
      }

      // 如果是旧格式（字符串）
      if (typeof ipsData === "string" && ipsData.trim()) {
        const ipsArray = ipsData
          .split(",")
          .map((ip) => ip.trim())
          .filter((ip) => ip);
        return ipsArray.map((ip) => ({
          ips: ip,
          isp_code: "", // 旧格式没有运营商代码
          allow_out_province_ratio: 0, // 旧格式没有出省比例
          area_id: "", // 旧格式没有大区信息
          province_id: "", // 旧格式没有省份信息
          city_id: "", // 旧格式没有城市信息
        }));
      }

      return [];
    },

    // 根据运营商代码获取中文名称
    getIspName(code) {
      if (!code) return "";
      const isp = this.ispList.find((item) => item.code === code);
      return isp ? isp.cn_name : code;
    },
  },
};
</script>

<style scoped>
.flex-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
}

.table-style {
  margin-bottom: 20px;
}

.pagination {
  text-align: center;
}

.contact-info {
  text-align: left;
  font-size: 12px;
  line-height: 1.5;
}

.contact-info div {
  margin-bottom: 2px;
}

.contact-info div:last-child {
  margin-bottom: 0;
}

.ip-segments {
  text-align: left;
  font-size: 12px;
  line-height: 1.5;
}

.ip-segment-item {
  margin-bottom: 4px;
  padding: 2px 6px;
  background-color: #f5f7fa;
  border-radius: 3px;
  display: inline-block;
  width: 100%;
}

.ip-segment-item:last-child {
  margin-bottom: 0;
}
</style>
