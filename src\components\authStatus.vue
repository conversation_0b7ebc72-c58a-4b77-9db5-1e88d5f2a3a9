<template>
  <div style="display: flex; align-items: center">
    <el-button
      v-if="$store.state.auth.isVerified"
      type="text"
      @click="$store.dispatch('auth/resetAuthState')"
      style="color: #909399"
    >
      <i class="el-icon-unlock"></i> 清除认证状态
    </el-button>
    <span
      v-if="$store.state.auth.isVerified"
      style="color: #67c23a; margin-left: 10px; font-size: 12px"
    >
      <i class="el-icon-success"></i> 已认证，本次会话内无需重复输入密码
    </span>
  </div>
</template>
<script>
export default {
  name: "AuthStatus",
};
</script>
