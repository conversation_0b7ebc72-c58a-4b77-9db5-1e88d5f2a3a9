<template>
  <el-dialog
    title="解析组配置修改确认"
    :visible="true"
    @close="onClose()"
    width="700px"
    class="confirm-dlg"
    center
    :append-to-body="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <h3 align="center" v-if="noChange">无变更</h3>
    <h3 v-if="changes.bizTagChange.__edits.length > 0">解析组业务标签修改</h3>
    <el-card class="box-card" v-for="change in changes.bizTagChange.__edits" :key="change.__client_index">
      
      <ol>
        <li v-for="tag in change.insertBizTags" :key="tag.tag_code" class="text-success">添加：【{{tag.tag_cnname}}】业务标签</li>
        <li v-for="tag in change.deleteBizTags" :key="tag.tag_code" class="text-error">删除：【{{tag.tag_cnname}}】业务标签</li>
      </ol>
    </el-card>
    <h3 class="m20" v-if="changes.mainChange.__inserts.length > 0 || changes.mainChange.__edits.length > 0">主覆盖配置修改</h3>
    <el-card class="box-card" v-for="change in changes.mainChange.__inserts" :key="change.__client_index">
      <div slot="header" class="clearfix text-success">
        <span>新增：【{{change.area_name}}】</span>
      </div>
      <ol>
        <li v-for="group in change.groups" :key="group.uuid" class="text-success">添加：【{{group.name}}】主机组</li>
      </ol>
    </el-card>
    <el-card class="box-card" v-for="change in changes.mainChange.__edits" :key="change.__client_index">
      <div slot="header" class="clearfix">
        <span>修改：【{{change.area_name}}】</span>
      </div>
      <ol>
        <li v-for="group in change.insertGroups" :key="group.uuid" class="text-success">添加：【{{group.name}}】主机组</li>
        <li v-for="group in change.editGroups" :key="group.uuid">
          修改：【{{group.name}}】主机组
          <ul>
            <li v-for="vip in group.vipChanges">
              <span style="margin-right: 30px;">{{vip.uuid}}</span>
              <span v-if="vip.is_disabled">打开调度禁用</span>
              <span v-else>关闭调度禁用</span>
            </li>
          </ul>
        </li>
        <li v-for="group in change.deleteGroups" :key="group.uuid" class="text-error">删除：【{{group.name}}】主机组</li>
      </ol>
    </el-card>
    <div slot="footer" class="dialog-footer" v-if="dialogType === 'save'">
      <el-button @click="onClose">取 消</el-button>
      <el-button type="primary" @click="onConfirm" :loading="submitting" :disabled="noChange">确认保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import http from "@/api/http.js"
  import ParamsMixin from './params'

  export default {
    props: [
      'configData',
      'changes',
      'areaConfigMap',
      'candidateHostGroupArray',
      'copyParseGroup',
      'selectedLineMap',
      'entity',
      'coverDetailResult',
      'dialogType',
      'from',
      'subTaskId',
    ],
    mixins: [ParamsMixin],
    data() {
      return {
        submitting: false,
        rules: {}
      }
    },
    computed: {
      noChange() {
        return this.changes.mainChange.__inserts.length === 0 && this.changes.mainChange.__edits.length === 0 && this.changes.bizTagChange.__edits.length === 0
      }
    },
    methods: {
      onClose() {
        this.$emit('close')
      },
      checkHostGroupIsp(msgArr, mainBack, coverStr) {
        mainBack && mainBack.forEach(edit => {
          let newIspSet = new Set()
          edit.groups.forEach(hg => {
            if (hg.isp_name) {
              newIspSet.add(hg.isp_name)
            } else {
              console.warn(hg.name + '没有运营商信息，不做校验提示')
            }
          })
          let oldIspSet = new Set()
          edit.oldGroups.forEach(hg => {
            if (hg.isp_name) {
              oldIspSet.add(hg.isp_name)
            }
          })
          // 若原主机组为同运营商，新增主机组的运营商不同，则提示
          if (oldIspSet.size !== 0) {
            if (newIspSet.size === 0) {
              msgArr.push(`${coverStr}【${edit.area_name}】的主机组运营商与变更之前的不一致`)
            } else {
              let s = new Set([...newIspSet, ...oldIspSet])
              if (!(s.size === newIspSet.size && s.size === oldIspSet.size)) {
                msgArr.push(`${coverStr}【${edit.area_name}】的主机组运营商与变更之前的不一致`)
              }
            }
          }
        })
        return msgArr
      },
      saveCover() {
        let params = this.getCoverDetailParams(this.configData, this.areaConfigMap)
        this.postSaveCover(params)
      },
      postSaveCover(data) {
        this.submitting = true
        let cover = { data }
        let params = {
          cover: JSON.stringify(cover)
        }
        let url = ""
        // 从覆盖调整 点击配置过来的，调用create接口, 传参也不完全一样
        if (this.from === "coverAdjust") {
          url = `/sda/change_cover/detail/create`
          params.pg_id = Number(this.entity.id),
          params.pre_cover = JSON.stringify(this.coverDetailResult)
        } else {
          // 从任务列表 点击配置过来的，调用update接口
          url = `/sda/change_cover/detail/update`
          params.id = this.subTaskId
        }
        http.post(url, params)
          .then((res) => {
            if (res && res.code === 100000) {
              this.$notify.success({
                title: '提交成功',
                message: '保存解析组配置成功',
                position: 'bottom-right'
              })
              this.$emit('close')
              setTimeout(() => {
                const from = this.from;
                this.$router.push({name: from})
                // if (this.from === "coverAdjust") {
                //   this.$router.push({name: 'coverAdjust'})
                // } else if (this.from === "taskList") {
                //   this.$router.push({name: 'taskList'})
                // }
              }, 400)
            }
          })
          .finally(() => {
            this.submitting = false
          })
      },
      async onConfirm() {
        let transIspMsg = []
        transIspMsg = this.checkHostGroupIsp(transIspMsg, this.changes.mainChange.__edits, '主覆盖')
        if (transIspMsg.length !== 0) {
          transIspMsg.push(`是否继续？`)
          let confirmText = transIspMsg
          const newDatas = []
          const h = this.$createElement
          for (const i in confirmText) {
            newDatas.push(h('p', null, confirmText[i]))
          }
          this.$confirm(
            '提示',
            {
              title: '提示',
              message: h('div', {style: 'max-height:700px;overflow:auto'}, newDatas),
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }
          )
            .then(() => {
              this.saveCover()
            })
        } else {
          this.saveCover()
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
.confirm-dlg {
  :global(.el-card__header) {
    padding: 10px 20px;
  }
  .box-card {
    font-size: 12px !important;
  }
  .m20 {
    margin: 20px 0;
    font-weight: 500;
  }
  :global(.el-card__body) {
    padding: 10px 20px;
  }

  ol {
    padding: 0;
    margin: 0 0 0 15px;
    list-style-type: decimal;
    li {
      padding: 6px 0;
    }
  }
  h3 {
    padding-bottom: 5px;
    border-bottom: 1px solid #DCDFE6;
    color: #000 !important;
  }
  .text-success {
    color: #67C23A;
  }

  .text-warning {
    color: #E6A23C;
  }

  .text-danger, .text-danger:focus, .text-danger:hover {
    color: #F56C6C;
  }

  .text-info {
    color: #909399;
  }
  .text-error {
    color: #f26562;
  }
}
</style>
