<template>
  <el-dialog
    append-to-body
    title="跑高 lake"
    :visible="true"
    :close-on-click-modal="false"
    width="1200px"
    @close="handleCancel"
  >
    <el-row>
      <el-table :data="tableData" v-loading="submitLoading || queryLoading" border>
        <el-table-column prop="lake_id" label="LakeId" align="center" width="200"></el-table-column>
        <el-table-column prop="lake_name" label="Lake名称" align="center"></el-table-column>
        <el-table-column label="点播上限带宽/M" align="center">
          <template v-slot:header>
            <div>点播上限带宽/M</div>
            <div>(v4:v6)</div>
          </template>
          <template slot-scope="scope">
            <span>{{ scope.row.vod_upper_v4bw + ':' + scope.row.vod_upper_v6bw }}</span>
          </template>
        </el-table-column>
        <el-table-column label="点播实时带宽/M" align="center">
          <template v-slot:header>
            <div>点播实时带宽/M</div>
            <div>(v4:v6)</div>
          </template>
          <template slot-scope="scope">
            <span>{{ scope.row.vod_cur_v4bw + ':' + scope.row.vod_cur_v6bw }}</span>
          </template>
        </el-table-column>
      </el-table>
      <div v-if="resMessage" class="text-wrapper" v-safe-html="resMessage"></div>
    </el-row>

    <div slot="footer" class="dialog-footer">
      <el-button size="medium" @click="handleCancel">取 消</el-button>
      <el-button
        size="medium"
        type="primary"
        :loading="submitLoading || queryLoading"
        :disabled="submitLoading || !isButtonDisabled || queryLoading"
        @click="handleSubmit"
      >生成方案</el-button>
    </div>
  </el-dialog>
</template>

<script>
import http from '@/api/http.js'

export default {
  components: {},
  props: {},
  data() {
    return {
      submitLoading: false,
      queryLoading: false,
      tableData: [],
      resMessage: "",
    }
  },
  computed: {
    isButtonDisabled() {
      // 列表没有数据的时候，生成方案 按钮置灰不可点击
      return this.tableData && this.tableData.length > 0
    }
  },
  mounted() {
    this.getRunHighLakeList()
  },
  methods: {
    async getRunHighLakeList() {
      this.queryLoading = true;
      const res = await http.get(`/sda/change_cover/auto_gen/rh_lake_list`)
      const list = res && res.data
      this.tableData = structuredClone(list)
      this.queryLoading = false;
    },
    async handleSubmit() {
      this.submitLoading = true

      let res = await http.get(`/sda/change_cover/auto_gen/create`)
      this.resMessage = (res && res.message) || ""
      let successMsg = "操作成功"
      if (res && res.code === 100000) {
        successMsg = res && res.message;
        this.$message.success(successMsg);
        this.$emit("close")
        this.$emit("refresh")
        this.submitLoading = false
      } else {
        this.submitLoading = false
      }
    },
    handleCancel() {
      this.$emit("close")
    },
  },
}
</script>

<style scoped lang="scss">
.text-wrapper {
  margin-top: 12px;
  white-space: pre-wrap;
}
</style>