<template>
  <el-card class="node-bandwidth-source-config">
    <div class="search-container">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="Node名称">
          <el-select
            v-model="searchForm.node_name"
            placeholder="请输入/选择Node名称"
            allow-create
            filterable
            default-first-option
            clearable
          >
            <el-option
              v-for="item in nodeOptions"
              :key="item.node_id"
              :label="item.node_name"
              :value="item.node_name"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="Lake名称">
          <el-select
            v-model="searchForm.lake_name"
            placeholder="请输入/选择Lake名称"
            allow-create
            filterable
            default-first-option
            clearable
          >
            <el-option
              v-for="item in lakeOptions"
              :key="item.lake_code"
              :label="item.lake_name"
              :value="item.lake_name"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchData">查询</el-button>
          <el-button @click="resetForm">重置</el-button>
          <el-button type="primary" @click="showAddDialog">新增</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table :data="tableData" style="width: 100%" v-loading="loading">
      <el-table-column prop="id" label="ID"></el-table-column>
      <el-table-column prop="node_id_str" label="NodeId"></el-table-column>
      <el-table-column prop="node_name" label="Node名称"></el-table-column>
      <el-table-column prop="lake_names" label="关联的Lake"></el-table-column>
      <el-table-column prop="bw_source" label="数据源">
        <template slot-scope="scope">
          {{ scope.row.bw_source === 0 ? "子Lake累加" : "交换机带宽" }}
        </template>
      </el-table-column>
      <el-table-column prop="operator" label="操作人"></el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleEdit(scope.row)"
            >修改</el-button
          >
          <el-button size="mini" type="text" @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="currentPage"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
    >
    </el-pagination>

    <!-- 新增/编辑弹窗 -->
    <el-dialog
      append-to-body
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      :show-close="false"
      width="550px"
    >
      <el-form :model="form" :rules="rules" ref="form" label-width="150px">
        <el-form-item label="Node名称" prop="node_name">
          <el-select
            v-model="form.node_id_str"
            placeholder="请选择Node名称"
            filterable
            :disabled="isEdit"
            style="width: 100%"
            @change="handleNodeChange"
          >
            <el-option
              v-for="item in nodeOptions"
              :key="item.node_id_str"
              :label="item.node_name"
              :value="item.node_id_str"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="NodeId" prop="node_id_str">
          <el-input v-model="form.node_id_str" disabled></el-input>
        </el-form-item>
        <el-form-item label="数据源" prop="bw_source">
          <el-radio-group v-model="form.bw_source">
            <el-radio :label="0">子Lake累加</el-radio>
            <el-radio :label="1">交换机带宽</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCloseDialog">取 消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitLoading"
          >确 定</el-button
        >
      </div>
    </el-dialog>

    <!-- 删除确认框 -->
    <el-dialog
      append-to-body
      title="提示"
      :visible.sync="deleteDialogVisible"
      width="400px"
    >
      <div>确定要删除该配置吗？</div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="confirmDelete"
          :loading="deleteLoading"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </el-card>
</template>

<script>
import http from "../http";
import { mapState } from "vuex";

export default {
  name: "NodeBandwidthSourceConfig",
  data() {
    return {
      // 表格数据
      tableData: [],
      loading: false,
      // 分页
      currentPage: 1,
      pageSize: 10,
      total: 0,
      // 搜索
      searchForm: {
        node_name: "",
        lake_name: "",
      },
      // 弹窗
      dialogVisible: false,
      dialogTitle: "新增Node带宽数据源配置",
      isEdit: false,
      submitLoading: false,
      form: {
        id: "",
        node_id_str: "",
        node_name: "",
        bw_source: 0,
      },
      rules: {
        node_name: [
          { required: true, message: "请选择Node名称", trigger: "change" },
        ],
        bw_source: [
          { required: true, message: "请选择数据源", trigger: "change" },
        ],
      },
      // 删除确认框
      deleteDialogVisible: false,
      deleteLoading: false,
      deleteId: null,
    };
  },
  created() {
    // Dispatch action to load nodeList data
    this.fetchData();
  },
  computed: {
    ...mapState({
      nodeList: (state) => state.baseData.nodeList,
      lakeList: (state) => state.baseData.fullLakeList,
    }),
    lakeOptions() {
      return this.lakeList.map((itm) => {
        return {
          lake_name: itm.bk_inst_name,
          lake_id: itm.bk_inst_id,
          lake_code: itm.node_code,
        };
      });
    },
    nodeOptions() {
      return this.nodeList.map((itm) => {
        return {
          node_name: itm.nodeName,
          node_id: itm.nodeId,
          node_id_str: itm.nodeIdStr,
        };
      });
    },
  },
  methods: {
    // 关闭弹窗
    handleCloseDialog() {
      this.dialogVisible = false;
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    },
    handleNodeChange() {
      const nodeOption = this.nodeOptions.find(
        (itm) => itm.node_id_str === this.form.node_id_str
      );
      if (!nodeOption) return;
      this.form.node_name = nodeOption.node_name;
    },
    // 获取表格数据
    async fetchData() {
      this.loading = true;
      try {
        const res = await http.getNodeBandwidthSourceList({
          page: this.currentPage,
          page_size: this.pageSize,
          node_name: this.searchForm.node_name,
          lake_name: this.searchForm.lake_name,
        });

        if (res && res.data) {
          this.tableData = res.data.items || [];
          this.total = res.data.total || 0;
        } else {
          this.tableData = [];
          this.total = 0;
        }

        this.loading = false;
      } catch (error) {
        console.error(error);
        this.loading = false;
        this.$message.error("获取数据失败");
      }
    },

    // 搜索
    searchData() {
      this.currentPage = 1;
      this.fetchData();
    },

    // 重置
    resetForm() {
      this.searchForm = {
        node_name: "",
        lake_name: "",
      };
      this.searchData();
    },

    // 分页
    handleSizeChange(val) {
      this.pageSize = val;
      this.fetchData();
    },

    handleCurrentChange(val) {
      this.currentPage = val;
      this.fetchData();
    },

    // 新增
    showAddDialog() {
      this.dialogTitle = "新增Node带宽数据源配置";
      this.isEdit = false;
      this.form = {
        id: "",
        node_id: "",
        node_name: "",
        bw_source: 0,
      };
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate();
      });
    },

    // 编辑
    handleEdit(row) {
      this.dialogTitle = "修改Node带宽数据源配置";
      this.isEdit = true;
      this.form = {
        id: row.id,
        node_id_str: row.node_id_str,
        node_name: row.node_name,
        bw_source: row.bw_source,
      };
      this.dialogVisible = true;
    },

    // 提交表单
    submitForm() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.submitLoading = true;
          let res;
          try {
            // 准备提交的数据
            const submitData = {
              id: this.form.id,
              node_id_str: this.form.node_id_str,
              node_name: this.form.node_name,
              bw_source: this.form.bw_source,
            };

            if (this.isEdit) {
              // 编辑
              res = await http.updateNodeBandwidthSource(submitData);
              if (!res) {
                this.submitLoading = false;
                return;
              }

              // 重新加载数据
              this.fetchData();
              this.$message.success("修改成功");
            } else {
              delete submitData.id;
              // 新增
              res = await http.addNodeBandwidthSource(submitData);
              if (!res) {
                this.submitLoading = false;
                return;
              }

              // 重新加载数据
              this.fetchData();
              this.$message.success("新增成功");
            }

            this.dialogVisible = false;
          } catch (error) {
            console.error(error);
          } finally {
            this.submitLoading = false;
          }
        } else {
          return false;
        }
      });
    },

    // 删除
    handleDelete(row) {
      this.deleteId = row.id;
      this.deleteDialogVisible = true;
    },

    // 确认删除
    async confirmDelete() {
      if (!this.deleteId) return;

      this.deleteLoading = true;
      try {
        // 调用删除API
        await http.deleteNodeBandwidthSource(this.deleteId);

        // 从表格移除
        const index = this.tableData.findIndex(
          (item) => item.id === this.deleteId
        );
        if (index !== -1) {
          this.tableData.splice(index, 1);
          this.total--;
        }

        this.$message.success("删除成功");
        this.deleteDialogVisible = false;
        this.deleteLoading = false;
      } catch (error) {
        console.error(error);
        this.deleteLoading = false;
        this.$message.error("删除失败");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.node-bandwidth-source-config {
  .el-pagination {
    margin-top: 20px;
    text-align: right;
  }
}
</style> 
