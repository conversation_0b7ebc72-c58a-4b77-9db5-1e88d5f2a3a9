import { numberRegexp } from './regexp'

// 校验数字大小
export const isBiggerThanX = (x, equal = false, trigger = 'blur') => {
  return {
    validator: (rule, value, callback) => {
      if (![null, undefined, ''].includes(value)) {
        if (equal && value < x) {
          return callback(new Error(`请输入大于等于${x}的数字`));
        } else if (!equal && value <= x) {
          return callback(new Error(`请输入大于${x}的数字`));
        }
      }
      return callback();
    },
    trigger
  }
}

export const isSmallerThanX = (x, equal = false, trigger = 'blur') => {
  return {
    validator: (rule, value, callback) => {
      if (value === '' || value === null || value === undefined) return callback()
      if (equal) {
        if (value <= x) return callback()
        else return callback(new Error(`请输入小于等于${x}的数字`))
      } else {
        if (value < x) return callback()
        else return callback(new Error(`请输入小于${x}的数字`))
      }
    },
    trigger
  }
}

/**
 * Generates a number range rule validator function.
 *
 * @param {Function} exp1 - The first expression function.
 * @param {Function} exp2 - The second expression function.
 * @param {string} msg - The error message to display.
 * @param {string} [trigger=blur] - The event trigger for validation.
 * @return {Object} - The validator object with a `validator` function.
 */
export const numberRangeRuleGen = (exp1, exp2, msg, trigger = 'blur') => ({
  validator: (rule, value, callback) => {
    if ([null, undefined, ''].includes(value)) {
      callback()
      return
    }
    if (!numberRegexp.test(value)) {
      callback(new Error('请输入数字'))
      return
    }
    const t = +value
    if (exp1(t) && exp2(t)) {
      callback()
    } else {
      callback(new Error(msg))
    }
  },
  trigger
})

export const percentage = numberRangeRuleGen(
  (x) => x >= 0,
  (x) => x <= 100,
  "请输入0-100之间的数字"
)

export const isInteger = numberRangeRuleGen(
  (x) => x % 1 === 0,
  (x) => x % 1 === 0,
  "请输入整数"
)
