<template>
  <el-card class="host-group-config">
    <!-- 查询条件 -->
    <el-form :model="searchForm" ref="searchFormRef" label-position="left" inline>
      <el-row>
        <el-form-item label="IP地址" prop="ip">
          <el-input
            v-model="searchForm.ip"
            clearable
            placeholder="请输入IP地址"
            style="width: 200px"
          ></el-input>
        </el-form-item>

        <el-form-item label="标签键" prop="tag_key">
          <el-select
            v-model="searchForm.tag_key"
            clearable
            placeholder="请选择标签键"
            style="width: 200px"
            @change="onTagKeyChange"
          >
            <el-option v-for="itm in tagKeyOptions" :key="itm.tag_key" :value="itm.tag_key" :label="itm.tag_key"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="标签值" prop="tag_value">
          <el-select
            v-model="searchForm.tag_value"
            clearable
            placeholder="请选择标签值"
            style="width: 200px"
          >
            <el-option v-for="value in tagValueOptions" :key="value" :value="value" :label="value"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button @click="onReset">重置</el-button>
        </el-form-item>
      </el-row>
    </el-form>

    <!-- 操作按钮 -->
    <el-row style="margin-bottom: 15px;">
      <el-button type="primary" @click="handleAdd">新增</el-button>
      <el-button
        type="danger"
        @click="handleBatchDelete"
        :disabled="selectedRows.length === 0"
      >
        批量删除
      </el-button>
    </el-row>

    <!-- 数据表格 -->
    <el-table
      :data="tableData"
      v-loading="querying"
      border
      @selection-change="handleSelectionChange"
      style="width: 100%"
    >
      <el-table-column type="selection" width="55" align="center"></el-table-column>

      <el-table-column prop="id" label="ID" align="center" width="80">
      </el-table-column>

      <el-table-column prop="v4_vip_num" label="IPv4 VIP个数" align="center" width="120">
      </el-table-column>

      <el-table-column prop="v6_vip_num" label="IPv6 VIP个数" align="center" width="120">
      </el-table-column>

      <el-table-column prop="is_ipv6_enable" label="启用IPv6" align="center" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.is_ipv6_enable ? '是' : '否' }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="lvs_mode" label="LVS模式" align="center" width="100">
        <template slot-scope="scope">
          <span>{{ getLvsModeText(scope.row.lvs_mode) }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="lvs_group" label="LVS组" align="center" width="150">
      </el-table-column>

      <el-table-column prop="isp_code" label="运营商编码" align="center" width="120">
      </el-table-column>

      <el-table-column prop="level" label="资源分组" align="center" width="80">
        <template slot-scope="scope">
          <span>{{ getLevelMap(scope.row.level) }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="resource_group" label="资源分组" align="center" width="120">
      </el-table-column>

      <el-table-column prop="rap_tag_infos" label="RAP标签信息" align="center" width="200">
        <template slot-scope="scope">
          <div v-if="scope.row.rap_tag_infos">
            <el-tag
              v-for="tag in getRapTags(scope.row.rap_tag_infos)"
              :key="tag"
              size="mini"
              style="margin-right: 5px; margin-bottom: 2px;"
            >
              {{ tag }}
            </el-tag>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column prop="port" label="端口" align="center" width="150">
        <template slot-scope="scope">
          <div v-if="scope.row.port">
            <el-tag
              v-for="port in getPorts(scope.row.port)"
              :key="port"
              size="mini"
              type="success"
              style="margin-right: 5px; margin-bottom: 2px;"
            >
              {{ port }}
            </el-tag>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column prop="ip_list" label="IP列表" align="center" min-width="250">
        <template slot-scope="scope">
          <div v-if="scope.row.ip_list && scope.row.ip_list.length > 0">
            <el-tag
              v-for="ip in scope.row.ip_list"
              :key="ip"
              size="mini"
              type="info"
              style="margin-right: 5px; margin-bottom: 2px;"
            >
              {{ ip }}
            </el-tag>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column prop="create_time" label="创建时间" align="center" width="160">
        <template slot-scope="scope">
          <span>{{ formatTime(scope.row.create_time) }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="update_time" label="更新时间" align="center" width="160">
        <template slot-scope="scope">
          <span>{{ formatTime(scope.row.update_time) }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="remark" label="备注" align="center" min-width="150">
      </el-table-column>

      <el-table-column prop="operator" label="操作人" align="center" width="120">
      </el-table-column>

      <el-table-column label="操作" align="center" width="150" fixed="right">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="mini"
            @click="handleEdit(scope.row)"
          >
            修改
          </el-button>
          <el-button
            type="text"
            size="mini"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-row style="text-align: center; padding-top: 20px">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.page_size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      ></el-pagination>
    </el-row>

    <!-- 新增/修改弹窗 -->
    <host-group-plan-dialog
      :visible="dialogVisible"
      :is-edit="isEdit"
      :current-row-data="currentRowData"
      @close="dialogVisible = false"
      @refresh="onSearch"
    ></host-group-plan-dialog>

  </el-card>
</template>

<script>
import http from "@/views/host-tag-config/http.js";
import hostGroupPlanDialog from "./dialog/hostGroupPlanDialog.vue";
import dayjs from 'dayjs';

export default {
  name: "host-group-config",
  components: {
    hostGroupPlanDialog,
  },
  data() {
    return {
      querying: false,
      tableData: [],
      selectedRows: [],
      dialogVisible: false,
      isEdit: false,
      currentRowData: {},

      // 查询表单
      searchForm: {
        ip: "",
        tag_key: "",
        tag_value: "",
      },

      // 分页信息
      pagination: {
        total: 0,
        page: 1,
        page_size: 20,
      },

      // 标签选项数据
      tagOptions: [],
      tagKeyOptions: [],
      tagValueOptions: [],
    };
  },

  mounted() {
    this.onSearch();
  },

  created() {
    this.loadTagOptions();
  },

  methods: {
    // 加载标签选项数据
    async loadTagOptions() {
      try {
        const tagRes = await http.getTagList({ page_size: 1000000 });
        if (tagRes) {
          this.tagOptions = tagRes.data?.items || [];
          // 提取唯一的标签键
          this.tagKeyOptions = this.getUniqueTagKeys(this.tagOptions);
          // 初始化时显示所有标签值
          this.tagValueOptions = this.getAllTagValues(this.tagOptions);
        }
      } catch (error) {
        console.error("加载标签选项数据失败:", error);
      }
    },

    // 获取唯一的标签键
    getUniqueTagKeys(tagOptions) {
      const uniqueKeys = [];
      const keySet = new Set();

      tagOptions.forEach(tag => {
        if (tag.tag_key && !keySet.has(tag.tag_key)) {
          keySet.add(tag.tag_key);
          uniqueKeys.push({ tag_key: tag.tag_key });
        }
      });

      return uniqueKeys;
    },

    // 获取所有可用的标签值选项
    getAllTagValues(tagOptions) {
      const values = [];
      const valueSet = new Set();

      tagOptions.forEach(tag => {
        if (tag.tag_value_list && Array.isArray(tag.tag_value_list)) {
          tag.tag_value_list.forEach(value => {
            if (value && !valueSet.has(value)) {
              valueSet.add(value);
              values.push(value);
            }
          });
        }
      });

      return values;
    },

    // 根据选中的标签键获取对应的标签值选项
    getTagValuesByKey(tagKey) {
      if (!tagKey) {
        // 未选择标签键时，返回所有标签值
        return this.getAllTagValues(this.tagOptions);
      }

      const values = [];
      const valueSet = new Set();

      this.tagOptions.forEach(tag => {
        if (tag.tag_key === tagKey && tag.tag_value_list && Array.isArray(tag.tag_value_list)) {
          tag.tag_value_list.forEach(value => {
            if (value && !valueSet.has(value)) {
              valueSet.add(value);
              values.push(value);
            }
          });
        }
      });

      return values;
    },

    // 标签键变化时的处理
    onTagKeyChange() {
      // 清空标签值
      this.searchForm.tag_value = "";
      // 更新标签值选项：如果有选中的标签键则过滤，否则显示所有
      this.tagValueOptions = this.getTagValuesByKey(this.searchForm.tag_key);
    },

    // 查询数据
    async onSearch() {
      this.querying = true;
      try {
        const params = {
          ...this.searchForm,
          page: this.pagination.page,
          page_size: this.pagination.page_size,
        };

        const response = await http.getHostGroupPlanList(params);
        if (response && response.code === 100000) {
          // 根据API数据结构处理响应数据
          if (response.data && response.data.items) {
            this.tableData = Array.isArray(response.data.items) ? response.data.items : [];
            this.pagination.total = response.data.total || 0;
            this.pagination.page = response.data.page || this.pagination.page;
            this.pagination.page_size = response.data.page_size || this.pagination.page_size;
          } else if (Array.isArray(response.data)) {
            // 兼容旧的数据格式
            this.tableData = response.data;
            this.pagination.total = response.data.length;
          } else {
            this.tableData = [];
            this.pagination.total = 0;
          }
        } else {
          this.tableData = [];
          this.pagination.total = 0;
          if (response && response.message) {
            this.$message.warning(response.message);
          }
        }
      } catch (error) {
        console.error("查询主机组规划列表失败:", error);
        this.tableData = [];
        this.pagination.total = 0;
        this.$message.error("查询失败，请检查网络连接");
      } finally {
        this.querying = false;
      }
    },

    // 重置查询条件
    onReset() {
      this.searchForm = {
        ip: "",
        tag_key: "",
        tag_value: "",
      };
      // 重置标签值选项为所有可用值
      this.tagValueOptions = this.getAllTagValues(this.tagOptions);
      this.pagination.page = 1;
      this.onSearch();
    },

    // 新增
    handleAdd() {
      this.isEdit = false;
      this.currentRowData = {};
      this.dialogVisible = true;
    },

    // 修改
    handleEdit(row) {
      this.isEdit = true;
      this.currentRowData = { ...row };
      this.dialogVisible = true;
    },

    // 删除单个记录
    async handleDelete(row) {
      this.$confirm(`确定要删除这条主机组规划记录吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          try {
            // 获取操作人信息
            const operator = window.localStorage.getItem("userInfo") || "system";
            const response = await http.deleteHostGroupPlan(row.id, { operator });
            if (response && response.code === 100000) {
              this.$message.success("删除成功");
              this.onSearch();
            } else {
              this.$message.error(response?.message || "删除失败");
            }
          } catch (error) {
            console.error("删除主机组规划失败:", error);
            this.$message.error("删除失败");
          }
        })
        .catch(() => {
          // 用户取消删除
        });
    },

    // 批量删除
    async handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.warning("请选择要删除的记录");
        return;
      }

      this.$confirm(
        `确定要删除选中的 ${this.selectedRows.length} 条记录吗？`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(async () => {
          try {
            // 获取操作人信息
            const operator = window.localStorage.getItem("userInfo") || "system";
            const deletePromises = this.selectedRows.map((row) =>
              http.deleteHostGroupPlan(row.id, { operator })
            );
            await Promise.all(deletePromises);
            this.$message.success("批量删除成功");
            this.onSearch();
          } catch (error) {
            console.error("批量删除主机组规划失败:", error);
            this.$message.error("批量删除失败");
          }
        })
        .catch(() => {
          // 用户取消删除
        });
    },

    // 表格选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },

    // 分页大小变化
    handleSizeChange(val) {
      this.pagination.page_size = val;
      this.pagination.page = 1;
      this.onSearch();
    },

    // 当前页变化
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.onSearch();
    },

    // 格式化时间戳
    formatTime(timestamp) {
      if (!timestamp || timestamp === 0) {
        return '-';
      }
      // 如果时间戳是秒级，转换为毫秒级
      const time = timestamp < 10000000000 ? timestamp * 1000 : timestamp;
      return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
    },

    // 获取LVS模式文本
    getLvsModeText(mode) {
      const modeMap = {
        1: '普通模式',
        2: '隧道模式',
      };
      return modeMap[mode] || mode;
    },

    getLevelMap(level) {
      const map = {
        1: '边缘',
        2: '一层父',
        3: '二层父'
      }

      return map[level] || level
    },

    // 获取RAP标签数组
    getRapTags(tagInfos) {
      if (!tagInfos || typeof tagInfos !== 'string') {
        return [];
      }
      return tagInfos.split(',').filter(tag => tag.trim()).map(tag => tag.trim());
    },

    // 获取端口数组
    getPorts(ports) {
      if (!ports || typeof ports !== 'string') {
        return [];
      }
      return ports.split(',').filter(port => port.trim()).map(port => port.trim());
    },
  },
};
</script>

<style scoped lang="scss">
</style>
