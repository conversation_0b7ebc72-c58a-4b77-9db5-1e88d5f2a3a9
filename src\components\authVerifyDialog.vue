<template>
  <el-dialog
    title="权限认证"
    :visible.sync="visible"
    width="400px"
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="请输入密码" prop="password">
        <el-input
          v-model.trim="form.password"
          type="password"
          placeholder="请输入"
          show-password
        ></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit">{{
        buttonText
      }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapActions } from 'vuex'

export default {
  name: "authVerifyDialog",
  data() {
    return {
      loading: false,
      form: {
        password: "",
      },
      rules: {
        password: [{ required: true, message: "请输入密码", trigger: "blur" }],
      },
    };
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    authType: {
      type: String,
      default: 'default'
    }
  },
  computed: {
    buttonText() {
      return "确定";
    },
  },
  methods: {
    ...mapActions('auth', ['verifyPassword']),
    handleClose() {
      this.$refs.form.resetFields();
      this.$emit("close");
    },
    async handleSubmit() {
      try {
        await this.$refs.form.validate();
        this.loading = true;

        // 使用store中的认证方法
        const result = await this.verifyPassword(this.form.password)
        
        if (result.success) {
          this.$message.success(result.message);
          // 通知父组件认证成功
          this.$emit("verify-success", result);
        } else {
          this.$message.error(result.message);
        }
      } catch (error) {
        console.error("权限认证失败:", error);
        this.$message.error("认证失败，请检查密码");
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>
