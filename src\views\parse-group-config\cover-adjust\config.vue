<template>
  <el-container class="is-vertical">
    <el-main class="content-wrap">
      <el-card class="data-table-container page-config-strategy">
        <div class="card-header">
          <h5 class="v-align-middle clearfix">
            <div class="pull-left"><label>解析组配置：{{ entity.name }}</label>
              <span>业务标签：{{ bizTagNames }}</span>
            </div>
            <div class="pull-right">
              <el-form :inline="true">
                <el-form-item>
                  <el-button type="text" @click="back">返回解析组列表</el-button>
                  <el-button @click="preview">预览</el-button>
                  <el-button type="primary" @click="save">保存配置</el-button>
                </el-form-item>
              </el-form>
            </div>
          </h5>
        </div>
        <div class="content clearfix" style="box-sizing: border-box;">
          <div class="left tabs-wrapper">
            <el-scrollbar class="tabs-view-config" :wrapStyle="[{'overflow-x':'auto'}]">
              <el-tree
                ref="tree"
                :data="treeData"
                :props="treeProps"
                node-key="id"
                :default-expanded-keys="expandTreeKeys"
                @node-click="nodeClick"
                :highlight-current="true"
                :expand-on-click-node="false"
              >
                <span class="node-label" slot-scope="{ node, data }">
                  <span>{{ node.label }}</span>
                  <span class="number" v-if="groupsNumber(data.id) > 0">{{ groupsNumber(data.id) }}</span>
                  <span class="number-wrap" v-if="!(v4PeakBandwidth(data.area_code) === 0 && v6PeakBandwidth(data.area_code) === 0)" style="color: ForestYellow ">{{v4PeakBandwidth(data.area_code)}}</span>
                  <span class="number-wrap" v-if="!(v4PeakBandwidth(data.area_code) === 0 && v6PeakBandwidth(data.area_code) === 0)" style="color: ForestYellow ">{{v6PeakBandwidth(data.area_code)}}</span>
                </span>
              </el-tree>
            </el-scrollbar>
          </div>
          <div class="right">
            <el-scrollbar style="height:100%" :wrapStyle="[{'overflow-x':'auto'}]">
              <div style="padding-right:15px">
                <div class="config-item">
                  <div>
                    <h3>标签过滤：
                      <el-select v-model="biz_tag_filter" placeholder="请选择标签" multiple filterable class="du-tag-select">
                        <el-option v-for="(tag, index) in bizTagsAll" :key="index" :value="tag.tag_code" :label="tag.tag_cnname"></el-option>
                      </el-select>
                    </h3>
                    <div class="host-form clearfix">
                      <!-- 加入 -->
                      <div class="pull-left">
                        <el-select v-model="selectGroupId" placeholder="请选择主机组" filterable multiple clearable :filter-method="(value) => filterGroup(value, 'main')">
                          <div style="padding-left:20px">
                            <el-button type="text" @click="selectAllGroup('main')"><i class="el-icon-circle-check" />全选</el-button>
                            <el-button type="text" @click="removeAllGroup('main')"><i class="el-icon-close" />清空</el-button>
                          </div>
                          <el-option v-for="gc in getGroupCandidates('main')" :value="gc.uuid" :label="gc.name" :key="gc.uuid"></el-option>
                        </el-select>
                        <el-button native-type="button" type="primary" @click="addRow()" :disabled="selectGroupId.length === 0">加入</el-button>
                        <!-- 控制调度禁用 -->
                        <el-select v-model="disabledState" placeholder="请选择" class="ml" filterable>
                          <el-option value="0" label="v4+v6"></el-option>
                          <el-option value="1" label="仅v4"></el-option>
                          <el-option value="2" label="仅v6"></el-option>
                        </el-select>
                      </div>
                      <!-- 删除 -->
                      <div class="pull-right">
                        <el-select v-model="selectDelGroupId" placeholder="请选择主机组" filterable multiple clearable :filter-method="(value) => filterGroup(value, 'main', true)">
                          <div style="padding-left:20px">
                            <el-button type="text" @click="selectAllGroup('main', true)"><i class="el-icon-circle-check" />全选</el-button>
                            <el-button type="text" @click="removeAllGroup('main', true)"><i class="el-icon-close" />清空</el-button>
                          </div>
                          <el-option v-for="gc in getGroupCandidates('main', true)" :value="gc.uuid" :label="gc.name" :key="gc.uuid"></el-option>
                        </el-select>
                        <el-button native-type="button" type="primary" @click="addDelRow()" :disabled="selectDelGroupId.length === 0">删除</el-button>
                      </div>
                    </div>
                  </div>
                  <!-- 列表 -->
                  <div class="vip-list" v-if="configMap[selectedAreaNodeId]">
                    <el-collapse v-model="configMap[selectedAreaNodeId].activeNames">
                      <el-collapse-item v-for="group in selectedNodeGroups" :name="group.uuid" :key="group.uuid">
                        <template slot="title">
                          <div class="group-title">
                            <span :title="group.name">{{ '主机组：' + group.name }}</span>
                            <span :title="group.lake_name">{{ '所属节点：' + group.lake_name}}</span>
                            <span>{{ '节点状态：' }}
                              <span v-if="group.lake_service_state === 0">下线</span>
                              <span v-else-if="group.lake_service_state === 1">上线</span>
                              <span v-else-if="group.lake_service_state === 2" class="text-error">故障</span>
                              <span v-else-if="group.lake_service_state === 3" class="text-error">删除</span>
                              <span v-else>未知</span>
                            </span>
                            <span :title="group.lake_up_bandwidth">{{ '节点上线：' + group.lake_up_bandwidth }}</span>
                            <span :title="group.vips" v-if="isContainAip(group)">{{ '是否包含AIP：是'}}</span>
                            <el-button type="text" @click.stop="removeRow(group)">删除</el-button>
                          </div>
                        </template>
                        <!-- 列表 -->
                        <el-table :data="group.vips" border width="540px">
                          <el-table-column label="VIP" prop="uuid" :min-width="200"></el-table-column>
                          <el-table-column label="资源分组" prop="resource_group_name" :min-width="150"></el-table-column>
                          <el-table-column label="业务标签" prop="biz_tag_cname" :min-width="150"></el-table-column>
                          <el-table-column label="类型" :min-width="60">
                            <template slot-scope="scope">
                              <span v-if="scope.row.vip_type === 1">VIP</span>
                              <span v-if="scope.row.vip_type === 2" style="color:red">AIP</span>
                            </template>
                          </el-table-column>
                          <el-table-column label="服务状态" :min-width="60">
                            <template slot-scope="scope">
                              <span v-if="scope.row.state === 1">可用</span>
                              <span v-if="scope.row.state === 2">不可用</span>
                            </template>
                          </el-table-column>
                          <el-table-column label="自动禁用" :min-width="60">
                            <template slot-scope="scope">
                              <el-tooltip v-if="isAutodisabled(scope.row.biz_tag_code)" class="item" effect="dark" content="IP的标签为独用标签，且解析组未包含该独用标签，将会自动禁用" placement="top">
                                <span  style="color:red">是</span>
                              </el-tooltip>
                              <span v-else>否</span>
                            </template>
                          </el-table-column>
                          <el-table-column label="" :min-width="100" >
                            <template slot="header">调度禁用
                              <el-button type="text" @click="updateDisable(group.uuid, 'main', true)" >反选</el-button>
                            </template>
                            <template slot-scope="scope">
                              <el-checkbox v-model="scope.row.is_disabled" @change="updateLineConfig('main', true)"></el-checkbox>
                            </template>
                          </el-table-column>
                        </el-table>
                      </el-collapse-item>
                    </el-collapse>
                  </div>
                </div>
              </div>
            </el-scrollbar>
          </div>
        </div>
      </el-card>
    </el-main>
    <!-- 保存配置 -->
    <StrategyConfirm
      v-if="showConfirmDlg"
      @close="showConfirmDlg = false"
      :entity="entity"
      :dialogType="dialogType"
      :coverDetailResult="coverDetailResult"
      :config-data="configData"
      :selected-line-map="selectedLineListConfigMap"
      :copyParseGroup="copyParseGroup"
      :changes="changes"
      :area-config-map="configMap"
      :candidateHostGroupArray="candidateHostGroupArray"
      :from="from"
      :subTaskId="subTaskId"
    ></StrategyConfirm>
  </el-container>
</template>
<script>

import http from "@/api/http.js"
import dateFormat from "@/utils/dateFormat"
import Vue from 'vue'
import { cloneDeep, groupBy } from "lodash"
import StrategyConfirm from '@/views/parse-group-config/cover-adjust/dialog/confirm.vue'

const GLOBAL_AREA_ID = 10488
const GROUP_TYPE_MAIN = 1

export default {
  name: "config",
  components: {
    StrategyConfirm
  },
  filters: {
    dateFormat: dateFormat
  },
  props: [],
  data() {
    return {
      entity: {
        name: ''
      },
      disabledState: '0',
      from: '',
      subTaskId: '',
      dialogType: '',
      coverDetailResult: {},
      haveClickNode: false,
      treeData: [],
      treeMap: {},
      treeProps: {
        children: 'children',
        label: 'area_cnname'
      },
      biz_tag_filter: [],
      biz_tag_codes: [],
      parseBizTag: [],
      bizTagMap: {},
      orginBizTags: [],
      bizTagsAll: [],
      leafMap: {},
      lvType: 1,
      candidateHostGroupArray: [],
      candidateHostGroupAll: [],
      selectedAreaNode: {},
      changes: [],
      showConfirmDlg: false,
      selectGroupId: [],
      selectDelGroupId: [],
      configData: null,
      configMap: {},
      configMapCopies: {},
      v4PeakBwMap: {},
      v6PeakBwMap: {},
      submitting: false,
      loadingConfig: false,
      loadingTree: false,
      isEnptyMain: false,
      parseGroupType: '',
      coverVipType: ['1', '2'],
      copyParseGroup: {},
      lineConfigList: [], // 线路配置列表
      selectedLineList: [], // 选中的线路配置id
      selectedLineListConfigMap: {}, // 选中的线路配置的具体覆盖配置
      currentSelectedLineNode: {}, // 当前被选中的线路节点
      viewList: [], // view 列表
      filterKey: '',
      filterDelKey: '',
      isDeletedByConfigView: false, // 是否因为view覆盖不一致而被删除的线路配置
      peakData: [], // pv/peak接口返回的值
    }
  },
  computed: {
    expandTreeKeys() {
      if (this.haveClickNode) return
      let keys = []
      if (this.configMap && this.treeData && this.treeMap && Object.keys(this.treeMap).length > 0) {
        for (const key in this.configMap) {
          let areaInfo = this.treeMap[key]
          if (areaInfo && areaInfo.parent_id !== 0) keys.push(areaInfo.parent_id)
        }
      }
      keys = Array.from(new Set(keys))
      // 若不存在覆盖配置，则默认展开所有树
      if (keys.length === 0) {
        for (const key in this.treeMap) {
          keys.push(key)
        }
      }
      return keys
    },
    bizTagNames() {
      let bizTagList = (this.bizTagsAll || []).filter(item => this.biz_tag_codes.includes(item.tag_code))
      let tagNames = (bizTagList.map(item => item.tag_cnname)) || []
      let data = tagNames.toString()
      return data
    },
    selectedAreaNodeId() {
      return this.selectedAreaNode && this.selectedAreaNode.id
    },
    // 对主覆盖的VIP，根据数字进行升序排序
    selectedNodeGroups() {
      return this.configMap[this.selectedAreaNodeId].groups && this.configMap[this.selectedAreaNodeId].groups.map(group => {
        return {
          ...group,
          vips: group.vips.sort((a, b) => a.uuid.localeCompare(b.uuid, 'numeric'))
        }
      })
    },
    groupsNumber() {
      return (areaId) => {
        return this.configMap[areaId] ? (this.configMap[areaId].groups.length) : 0
      }
    },
    v4PeakBandwidth() {
      return (areaId) => {
        return this.v4PeakBwMap[areaId] ? this.parseBwByteString(this.v4PeakBwMap[areaId]) : 0
      }
    },
    v6PeakBandwidth() {
      return (areaId) => {
        let data = Number(areaId)
        return this.v6PeakBwMap[data] ? this.parseBwByteString(this.v6PeakBwMap[data]) : 0
      }
    },
    lineConfigListMap() {
      const map = new Map()
      this.lineConfigList.forEach(line => map.set(line.id, line))
      return map
    },
    viewIdMapToId() {
      const map = new Map()
      this.viewList.forEach(view => {
        map.set(view.view_id, view.id)
      })
      return map
    },
    isAutodisabled() {
      return (bizTagCodes) => {
        let vipTags = bizTagCodes ? bizTagCodes.split(',') : []
        let autodisabled = false
        vipTags.forEach(vipTag => {
          if (vipTag !== '') {
            let vipTagInfo = this.bizTagMap.get(vipTag)
            if (vipTagInfo.is_dedicated === 1 && this.biz_tag_codes.filter((item) => { return item === vipTag }).length === 0) {
              autodisabled = true
            }
          }
        })
        return autodisabled
      }
    },
  },
  watch: {},
  created() {},
  mounted() {
    this.haveClickNode = false
    this.init().catch(() => {}).finally(() => { this.loadingConfig = false })
    this.from = (this.$route.query && this.$route.query.from) || "";
    this.subTaskId = (this.$route.query && this.$route.query.subTaskId) || "";
  },
  methods: {
    nodeClick(node, initData) {
      let id =  this.selectedAreaNode && this.selectedAreaNode.id
      if (initData || id !== node.id) {
        this.selectedAreaNode = node
        this.selectGroupId = []
        let nodeId = node && node.id
        if (!this.configMap[nodeId]) {
          this.initConfigData(node)
        }
      }
      this.haveClickNode = true
    },
    initConfigData(node) {
      let id = node && node.id
      let area_cnname = node && node.area_cnname
      let level = node && node.level
      let obj = {
        id: 0,
        area_id: id,
        area_name: area_cnname,
        area_level: level,
        groups: [],
        activeNames: [],
        bakActiveNames: []
      }
      Vue.set(this.configMap, id, obj)
    },
    updateLineConfig(type = 'main', isFromView = false) {
      if (isFromView) {
        // 清理线路配置
        const config = this.configMap[this.selectedAreaNode.id]
        if (!Object.keys(config).includes('generator')) return
        this.isDeletedByConfigView = true
        this.selectedLineList = this.selectedLineList.filter(id => id !== config.generator)
        const lineInfo = this.lineConfigListMap.get(+config.generator)
        this.$message.warning(
          `线路："${lineInfo.name}" 下的区域 "${this.selectedAreaNode.area_cnname}" 被修改，导致该线路下区域的覆盖不一致，将移除该线路。`
        )

        delete this.selectedLineListConfigMap[config.generator]
        delete config.generator
        return
      }
      const config = this.selectedLineListConfigMap[this.currentSelectedLineNode.id]
      config.views.forEach(view => {
        const id = this.viewIdMapToId.get(view.view_id)
        const viewConfig = this.configMap[id]
        if (!viewConfig) return
        if (type === 'main') {
          viewConfig.groups = cloneDeep(config.cover_config.main)
        }
      })
    },
    updateDisable(uuid, type = 'main', isFromView = false) {
      let groups
      if (isFromView) {
        if (!this.selectedAreaNode) {
          return
        }
        let selectId = this.selectedAreaNode.id
        if (!this.configMap[selectId] || !this.configMap[selectId].groups) {
          return
        }
        groups = this.configMap[selectId].groups
      } else {
        if (!this.currentSelectedLineNode) {
          return
        }
        let selectId = this.currentSelectedLineNode.id
        if (!this.selectedLineListConfigMap[selectId] || !this.selectedLineListConfigMap[selectId].cover_config) {
          return
        }
        let cover = this.selectedLineListConfigMap[selectId].cover_config
        groups = type === 'main' ? cover.main : cover.bak
      }
      let group = groups.find(item => item.uuid === uuid)
      if (!group) {
        return
      }
      group.vips.forEach(element => {
        element.is_disabled = !element.is_disabled
      })
      this.updateLineConfig(type, isFromView)
    },
    addRow(type) {
      let config = this.configMap[this.selectedAreaNode.id]
      if (!config) {
        this.initConfigData(this.selectedAreaNode)
        config = this.configMap[this.selectedAreaNode.id]
      }
      let selectGroupIds = this.selectGroupId
      selectGroupIds.forEach((groupId) => {
        let groupObj = this.candidateHostGroupArray.filter((item) => item.uuid === groupId)[0]
        let obj = JSON.parse(JSON.stringify(groupObj))
        obj.vips.forEach((vip) => {
          vip.is_disabled = false
          // ip_type = 1，表示v4
          // ip_type = 2，表示v6
          // 如果选中的是：仅v4，并且该条数据的 ip_type 等于2(表示该条数据为v6)，点击加入按钮的时候，需要将 is_disabled 设为true，即需要将v6禁用
          if (this.disabledState === '1' && vip.ip_type === 2) {
            vip.is_disabled = true
          }
          // 如果选中的是：仅v6，并且该条数据的 ip_type 等于1(表示该条数据为v4)，点击加入按钮的时候，需要将 is_disabled 设为true，即需要将v4禁用
          if (this.disabledState === '2' && vip.ip_type === 1) {
            vip.is_disabled = true
          }
          let vipType = this.coverVipType.filter(vipType => vipType === vip.vip_type.toString())
          if (!vipType || vipType.length === 0) {
            vip.is_disabled = true
          }
        })
        // 判断config下有没有generator字段，有的话需要删除线路配置，但是保留view
        if (Object.keys(config).includes('generator')) {
          // 删除对应线路配置，并删除该线路下对应的所有view的配置中的generator字段
          const lineConfig = this.lineConfigListMap.get(config.generator)
          lineConfig.views.map(view => {
            const id = this.viewIdMapToId.get(view.view_id)
            if (!this.configMap[id]) return
            delete this.configMap[id].generator
          })
          this.isDeletedByConfigView = true
          this.selectedLineList = this.selectedLineList.filter(id => id !== lineConfig.id)
          this.$message.warning(
            `线路："${lineConfig.name}" 下的区域 "${this.selectedAreaNode.area_cnname}" 被修改，导致该线路下区域的覆盖不一致，将移除该线路。`
          )
          delete this.selectedLineListConfigMap[lineConfig.id]
        }

        config.groups.splice(0, 0, obj)
        config.activeNames = [obj.uuid]
      })
      this.$message.success(
        `主机组批量加入成功，数量：${selectGroupIds.length}`
      )
      this.selectGroupId = []
    },
    addDelRow(type) {
      let config = this.configMap[this.selectedAreaNode.id]
      if (!config) {
        this.initConfigData(this.selectedAreaNode)
        config = this.configMap[this.selectedAreaNode.id]
      }
      let selectDelGroupIds = this.selectDelGroupId
      selectDelGroupIds.forEach((groupId) => {
        let groupObj = this.candidateHostGroupArray.filter((item) => item.uuid === groupId)[0]
        let obj = JSON.parse(JSON.stringify(groupObj))
        obj.vips.forEach((vip) => {
          vip.is_disabled = false
          let vipType = this.coverVipType.filter(vipType => vipType === vip.vip_type.toString())
          if (!vipType || vipType.length === 0) {
            vip.is_disabled = true
          }
        })
        // 判断config下有没有generator字段，有的话需要删除线路配置，但是保留view
        if (Object.keys(config).includes('generator')) {
          // 删除对应线路配置，并删除该线路下对应的所有view的配置中的generator字段
          const lineConfig = this.lineConfigListMap.get(config.generator)
          lineConfig.views.map(view => {
            const id = this.viewIdMapToId.get(view.view_id)
            if (!this.configMap[id]) return
            delete this.configMap[id].generator
          })
          this.isDeletedByConfigView = true
          this.selectedLineList = this.selectedLineList.filter(id => id !== lineConfig.id)
          this.$message.warning(
            `线路："${lineConfig.name}" 下的区域 "${this.selectedAreaNode.area_cnname}" 被修改，导致该线路下区域的覆盖不一致，将移除该线路。`
          )
          delete this.selectedLineListConfigMap[lineConfig.id]
        }

        config.groups = config.groups.filter(item => item.uuid !== obj.uuid)
      })
      this.$message.success(
        `主机组批量删除成功，数量：${selectDelGroupIds.length}`
      )
      this.selectDelGroupId = []
    },
    removeAllGroup(type, isDel) {
      isDel ? this.selectDelGroupId = [] : this.selectGroupId = []
    },
    selectAllGroup(type, isDel) {
      var val = []
      this.getGroupCandidates(type, isDel).map(item => {
        val.push(item.uuid)
      })
      isDel ? this.selectDelGroupId = val : this.selectGroupId = val
    },
    // 返回解析组列表
    back() {
      const from = this.from;
      this.$router.push({name: from})
    },
    preview() {
      this.dialogType = "preview"
      this.compareAll()
      this.showConfirmDlg = true
    },
    save() {
      this.dialogType = "save"
      this.compareAll()
      this.showConfirmDlg = true
    },
    isContainAip(group) {
      let isContain = false
      if (group && group.vips) {
        let aips = group.vips.filter(item => { return item.vip_type === 2 })
        if (aips.length !== 0) {
          isContain = true
        }
      }
      return isContain
    },
    removeRow(group, type) {
      let config = this.configMap[this.selectedAreaNode.id]
      this.updateLineConfig(type || 'main', true)
      config.groups = config.groups.filter((item) => item.uuid !== group.uuid)
      if (config.groups.length === 1) {
        config.activeNames = [config.groups[0].uuid]
      }
    },
    filterGroup(query, type, isDel) {
      if (!isDel) {
        this.filterKey = query
        this.getGroupCandidates(type)
      } else {
        this.filterDelKey = query
        this.getGroupCandidates(type, true)
      }
    },
    async init() {
      let pre1 = this.getLineConfigList()
      let pre2 = this.loadViews()
      this.loadingConfig = true
      await Promise.all([pre1, pre2])
      if (this.$route.query && this.$route.query.copyParseGroup) {
        this.copyParseGroup = JSON.parse(this.$route.query.copyParseGroup)
        this.biz_tag_codes = this.copyParseGroup.biz_tag_codes
        this.biz_tag_filter = this.copyParseGroup.biz_tag_codes
        // 加载复制解析组符合的主机组
        let p0 = this.loadHostGroupsOfCopyParse()
        await Promise.all([p0])
      }
      this.entity = { id: this.$route.query.id }
      let p1 = this.loadTree()
      let p2
      if (this.$route.query.from === "coverAdjust") {
        p2 = this.loadConfigs(this.entity.id)
      } else if (this.$route.query.from !== "coverAdjust") {
        p2 = this.getCoverDetail(this.$route.query.subTaskId)
      }
      let p3 = this.loadParseGroupType(this.entity.id)
      let p6 = this.getPeakCount(this.entity.id)
      await Promise.all([p1, p2, p3, p6])
      let p4 = this.getBizTagsAll()
      await Promise.all([p4])
      let p5 = this.loadHostGroups()
      await Promise.all([p5])
      this.$nextTick().then(this.jumpToViewGlobal)
    },
    jumpToViewGlobal() {
      this.$refs.tree.setCurrentKey(GLOBAL_AREA_ID)
      this.selectedAreaNode = this.$refs.tree.getCurrentNode()
      this.nodeClick(this.selectedAreaNode, true)
    },
    async getLineConfigList() {
      // 获取线路配置列表
      await http.get('/dcp/logicLine/list', {
        page: 1,
        page_size: 100000
      }).then(res => {
        if (!res) return
        this.lineConfigList = Array.isArray(res.data) ? res.data.map(line => {
          return {
            ...line,
            views: line.view_ids.split(',').map((viewId, idx) => ({
              view_id: viewId,
              label: line.view_names.split(',')[idx]
            }))
          }
        }) : []
      })
    },
    async loadViews() {
      return http.get(`/dcp/area/list`).then((res) => {
        this.viewList = res.data || []
      })
    },
    async loadHostGroupsOfCopyParse() {
      return new Promise((resolve, reject) => {
        let lvType = 1
        switch (this.copyParseGroup.type) {
          case 1:
            lvType = 2
            break
          case 2:
            lvType = 3
            break
          case 3:
            lvType = 1
            break
          default:
            break
        }
        http.get(`/dcp/hostGroup/details?ip_type=${this.copyParseGroup.ip_type}` +
          `&template_id=${this.copyParseGroup.template_id}` +
          `&cdn_type=${this.copyParseGroup.cdn_type}` +
          `&is_own_provider=${this.copyParseGroup.is_own_provider}` +
          `&biz_tags=${this.copyParseGroup.biz_tag_codes}` +
          `&lv_type=${lvType}`)
          .then((res) => {
            this.candidateHostGroupAll = res.data
            resolve()
          })
      })
    },
    loadTree() {
      this.loadingTree = true
      return new Promise((resolve, reject) => {
        http.get(`/dcp/area/tree`)
          .then((res) => {
            // this.treeData = this.treeData.concat(res.data)
            let result = (res && res.data) || []
            let mobileTreeData = this.handleTreeData(result)
            this.treeData = this.treeData.concat(mobileTreeData)
            this.getAllLeafNode()
            resolve()
          })
          .finally(() => {
            this.loadingTree = false
          })
      })
    },
    handleTreeData(data) {
      let asiaTreeData = (data[0].children.filter(item => item.area_cnname === "亚洲")) || []
      let eastAsiaTreeData = (asiaTreeData && asiaTreeData[0].children && asiaTreeData[0].children.filter(item => item.area_cnname === "东亚")) || []
      let mainlandTreeData = (eastAsiaTreeData && eastAsiaTreeData[0].children && eastAsiaTreeData[0].children.filter(item => item.area_cnname === "中国大陆")) || []
      let mobileTreeData = (mainlandTreeData && mainlandTreeData[0].children && mainlandTreeData[0].children.filter(item => item.area_cnname === "中国移动" || item.area_cnname === "中国联通")) || []
      return mobileTreeData
    },
    getAllLeafNode() {
      this.treeData.forEach(node => {
        this.treeMap[node.id] = node
        if (node.children && node.children.length > 0) {
          Vue.set(this.leafMap, node.id, false)
          this.getAllChildrenLeafNode(node)
        } else {
          Vue.set(this.leafMap, node.id, true)
        }
      })
    },
    getAllChildrenLeafNode(node) {
      if (node.children && node.children.length > 0) {
        this.treeMap[node.id] = node
        Vue.set(this.leafMap, node.id, false)
        node.children.forEach(child => {
          this.getAllChildrenLeafNode(child)
        })
      } else {
        this.treeMap[node.id] = node
        Vue.set(this.leafMap, node.id, true)
      }
    },
    getPeakCount(parseGroupId) {
      return new Promise((resolve, reject) => {
        http.get(`/sdcp/pv/peak?pg_id=${parseGroupId}`)
          .then((res) => {
            this.peakData = (res && res.data) || []
            this.peakData && this.peakData.forEach((view) => {
              this.v4PeakBwMap[view.view_id] = view.v4_peak_bw
              this.v6PeakBwMap[view.view_id] = view.v6_peak_bw
            })
            resolve()
          })
      })
    },
    // 任务列表，点击配置跳转过来调用的接口
    getCoverDetail(parseGroupId) {
      return new Promise((resolve, reject) => {
        http.get(`/sda/change_cover/detail/get?id=${parseGroupId}`)
          .then((res) => {
            this.coverDetailResult = JSON.parse(JSON.stringify(res))
            let resDetail = res && res.data && res.data.detail
            let detail = JSON.parse(resDetail)
            let data = detail.data
            this.configData = JSON.parse(JSON.stringify(data))
            let parse_area_covers = (detail && detail.data && detail.data.parse_area_covers) || []
            parse_area_covers.forEach((cover) => {
              let obj = {...cover}
              delete obj.bakGroups
              if (this.configMap[cover.area_id]) {
                // 若此区域存在覆盖，则append主机组
                if (cover.type === GROUP_TYPE_MAIN) {
                  this.configMap[cover.area_id].groups.push(cover.host_group)
                } else {
                  this.configMap[cover.area_id].bakGroups.push(cover.host_group)
                }
              } else {
                // 若不存在覆盖，则初始化
                delete obj['host_group']
                Vue.set(this.configMap, cover.area_id, obj)
                // Vue.set(this.configMap[cover.area_id], 'groups', [cover.host_group])
                Vue.set(this.configMap[cover.area_id], cover.type === GROUP_TYPE_MAIN ? 'groups' : 'bakGroups', [cover.host_group])
              }
              if (!this.configMap[cover.area_id].hasOwnProperty('groups')) {
                Vue.set(this.configMap[cover.area_id], 'groups', [])
              }
              if (!this.configMap[cover.area_id].hasOwnProperty('bakGroups')) {
                Vue.set(this.configMap[cover.area_id], 'bakGroups', [])
              }
              let uuids = []
              if (cover.type === GROUP_TYPE_MAIN) {
                if (this.configMap[cover.area_id].groups.length > 0) {
                  uuids.push(this.configMap[cover.area_id].groups[0].uuid)
                }
              } else {
                if (this.configMap[cover.area_id].bakGroups.length > 0) {
                  uuids.push(this.configMap[cover.area_id].bakGroups[0].uuid)
                }
              }
              Vue.set(this.configMap[cover.area_id], 'activeNames', uuids)
            })

            // 如果是从任务列表，点击配置进入，则需要拿 pre_detail 来进行比较
            let resPreDetail = res && res.data && res.data.pre_detail
            let pre_detail = JSON.parse(resPreDetail)
            let pre_parse_area_covers = (pre_detail && pre_detail.data && pre_detail.data.parse_area_covers) || []
            pre_parse_area_covers.forEach((cover) => {
              let obj = {...cover}
              if (this.configMapCopies[cover.area_id]) {
                // 若此区域存在覆盖，则append主机组
                if (cover.type === GROUP_TYPE_MAIN) {
                  this.configMapCopies[cover.area_id].groups.push(cover.host_group)
                } else {
                  this.configMapCopies[cover.area_id].bakGroups.push(cover.host_group)
                }
              } else {
                // 若不存在覆盖，则初始化
                delete obj['host_group']
                Vue.set(this.configMapCopies, cover.area_id, obj)
                // Vue.set(this.configMapCopies[cover.area_id], 'groups', [cover.host_group])
                Vue.set(this.configMapCopies[cover.area_id], cover.type === GROUP_TYPE_MAIN ? 'groups' : 'bakGroups', [cover.host_group])
              }
              if (!this.configMapCopies[cover.area_id].hasOwnProperty('groups')) {
                Vue.set(this.configMapCopies[cover.area_id], 'groups', [])
              }
              if (!this.configMapCopies[cover.area_id].hasOwnProperty('bakGroups')) {
                Vue.set(this.configMapCopies[cover.area_id], 'bakGroups', [])
              }
              let uuids = []
              if (cover.type === GROUP_TYPE_MAIN) {
                if (this.configMapCopies[cover.area_id].groups.length > 0) {
                  uuids.push(this.configMapCopies[cover.area_id].groups[0].uuid)
                }
              } else {
                if (this.configMapCopies[cover.area_id].bakGroups.length > 0) {
                  uuids.push(this.configMapCopies[cover.area_id].bakGroups[0].uuid)
                }
              }
              Vue.set(this.configMapCopies[cover.area_id], 'activeNames', uuids)
            })
            
            this.entity.name = data.name
            this.entity.ip_type = data.ip_type
            this.entity.biz_tag_codes = data.biz_tag_codes
            this.entity.resource_group_id = data.resource_group_id
            this.entity.template_id = data.template_id
            this.entity.cdn_type = data.cdn_type
            this.entity.is_own_provider = data.is_own_provider
            this.entity.server_type = data.server_type
            if (data.type === 1) {
              this.lvType = 2
            } else if (data.type === 2) {
              this.lvType = 3
            } else if (data.type === 3) {
              this.lvType = 1
            }
            // this.configMapCopies = cloneDeep(this.configMap)
            this.combineParseLineCover(data.parse_line_covers || [])
            resolve()
          })
      })
    },
    loadConfigs(parseGroupId) {
      return new Promise((resolve, reject) => {
        http.get(`/dcp/parseGroup/coverDetail?id=${parseGroupId}`)
          .then((res) => {
            this.coverDetailResult = JSON.parse(JSON.stringify(res))
            let data = res.data
            this.configData = JSON.parse(JSON.stringify(data))
            data.parse_area_covers.forEach((cover) => {
              let obj = {...cover}
              if (this.configMap[cover.area_id]) {
                // 若此区域存在覆盖，则append主机组
                if (cover.type === GROUP_TYPE_MAIN) {
                  this.configMap[cover.area_id].groups.push(cover.host_group)
                } else {
                  this.configMap[cover.area_id].bakGroups.push(cover.host_group)
                }
              } else {
                // 若不存在覆盖，则初始化
                delete obj['host_group']
                Vue.set(this.configMap, cover.area_id, obj)
                // Vue.set(this.configMap[cover.area_id], 'groups', [cover.host_group])
                Vue.set(this.configMap[cover.area_id], cover.type === GROUP_TYPE_MAIN ? 'groups' : 'bakGroups', [cover.host_group])
              }
              if (!this.configMap[cover.area_id].hasOwnProperty('groups')) {
                Vue.set(this.configMap[cover.area_id], 'groups', [])
              }
              if (!this.configMap[cover.area_id].hasOwnProperty('bakGroups')) {
                Vue.set(this.configMap[cover.area_id], 'bakGroups', [])
              }
              let uuids = []
              if (cover.type === GROUP_TYPE_MAIN) {
                if (this.configMap[cover.area_id].groups.length > 0) {
                  uuids.push(this.configMap[cover.area_id].groups[0].uuid)
                }
              } else {
                if (this.configMap[cover.area_id].bakGroups.length > 0) {
                  uuids.push(this.configMap[cover.area_id].bakGroups[0].uuid)
                }
              }
              Vue.set(this.configMap[cover.area_id], 'activeNames', uuids)
            })
            
            this.entity.name = data.name
            this.entity.ip_type = data.ip_type
            this.entity.biz_tag_codes = data.biz_tag_codes
            this.entity.resource_group_id = data.resource_group_id
            this.entity.template_id = data.template_id
            this.entity.cdn_type = data.cdn_type
            this.entity.is_own_provider = data.is_own_provider
            this.entity.server_type = data.server_type
            if (data.type === 1) {
              this.lvType = 2
            } else if (data.type === 2) {
              this.lvType = 3
            } else if (data.type === 3) {
              this.lvType = 1
            }
            this.configMapCopies = cloneDeep(this.configMap)
            this.combineParseLineCover(data.parse_line_covers || [])
            resolve()
          })
      })
    },
    compareAll() {
      this.changes = {
        mainChange: this.compare(),
        bizTagChange: this.compareTag()
      }
    },
    compare() {
      let change = {__inserts: [], __edits: []}
      for (let areaId in this.configMap) {
        let config = this.configMap[areaId]
        let groups = config.groups
        if (config.id === 0) {
          if (groups.length > 0) {
            change.__inserts.push(config)
          }
        } else {
          let oldConfig = this.configMapCopies[areaId]
          let oldGroups = oldConfig.groups
          let edit = {
            ...config,
            insertGroups: [],
            deleteGroups: [],
            editGroups: [],
            oldGroups: oldGroups
          }
          groups.forEach(group => {
            let exist = oldGroups.find(og => og.uuid === group.uuid)
            if (!exist) {
              edit.insertGroups.push(group)
            } else {
              let vips = group.vips
              let oldVips = exist.vips
              let item = {
                ...group,
                vipChanges: []
              }
              vips.forEach(vip => {
                let oldVip = oldVips.find(ov => ov.uuid === vip.uuid)
                if ((vip && oldVip) && vip.is_disabled !== oldVip.is_disabled) {
                  item.vipChanges.push(vip)
                }
              })
              if (item.vipChanges.length > 0) {
                edit.editGroups.push(item)
              }
            }
          })
          oldGroups.forEach(oldGroup => {
            let exist = groups.find(group => group.uuid === oldGroup.uuid)
            if (!exist) {
              edit.deleteGroups.push(oldGroup)
            }
          })
          if (edit.editGroups.length > 0 || edit.insertGroups.length > 0 || edit.deleteGroups.length > 0) {
            change.__edits.push(edit)
          }
        }
      }
      return change
    },
    compareTag() {
      let change = {__edits: []}
      let edit = {
        insertBizTags: [],
        deleteBizTags: [],
        oldBizTags: []
      }
      let insertCodes = this.biz_tag_codes.filter((item) => { return this.orginBizTags.indexOf(item) === -1 })
      insertCodes.forEach(code => {
        let bizTagInfo = this.bizTagMap.get(code)
        if (bizTagInfo) {
          edit.insertBizTags.push(bizTagInfo)
        }
      })
      let deleteCodes = this.orginBizTags.filter((item) => { return this.biz_tag_codes.indexOf(item) === -1 })
      deleteCodes.forEach(code => {
        let bizTagInfo = this.bizTagMap.get(code)
        if (bizTagInfo) {
          edit.deleteBizTags.push(bizTagInfo)
        }
      })
      this.orginBizTags.forEach(code => {
        let bizTagInfo = this.bizTagMap.get(code)
        if (bizTagInfo) {
          edit.oldBizTags.push(bizTagInfo)
        }
      })
      if (edit.insertBizTags.length > 0 || edit.deleteBizTags.length > 0) {
        change.__edits.push(edit)
      }
      return change
    },
    combineParseLineCover(parseLineCover) {
      if (!Array.isArray(parseLineCover)) return
      const lineCoverMap = groupBy(parseLineCover, 'line_id')
      Object.keys(lineCoverMap).forEach(lineId => {
        const lineInfo = this.lineConfigList.find(line => line.id === +lineId)
        if (!lineInfo) {
          return
        }
        const hostGroups = groupBy(lineCoverMap[lineId], 'type')
        const coverConfig = {
          // 这里需要用configMap中的hostGroup来赋值，而不是直接用接口返回的主机组
          main: (hostGroups['1'] || []).map(itm => itm.host_group),
          bak: (hostGroups['2'] || []).map(itm => itm.host_group)
        }
        Vue.set(this.selectedLineListConfigMap, lineId, {
          cover_config: coverConfig,
          line_id: lineId,
          views: lineInfo.view_ids.split(',').map((viewId, idx) => ({
            view_id: viewId,
            label: lineInfo.view_names.split(',')[idx]
          }))
        })
        this.selectedLineListConfigMap[lineId].views.map(view => {
          const id = this.viewIdMapToId.get(view.view_id)
          const config = this.configMap[id]
          if (!config) return this.$message.warning('未能读取到解析组配置！')
          config.generator = +lineId
        })
      })
      // 选中线路
      this.isDeletedByConfigView = true
      this.selectedLineList = Object.keys(this.selectedLineListConfigMap).map(Number)
    },
    async loadParseGroupType(parseGroupId) {
      await http.get(`/dcp/parseGroup/get?id=${parseGroupId}`)
      .then((res) => {
        if (res.data) {
          // 加载业务标签
          this.biz_tag_filter = res.data.biz_tag_codes
          this.biz_tag_codes = res.data.biz_tag_codes
          this.coverVipType = res.data.cover_vip_type.split(',')
          this.orginBizTags = res.data.biz_tag_codes
          this.parseGroupType = res.data.type
        }
      })
    },
    getBizTagsAll() {
      http.get(`/dcp/bizTag/all`)
        .then(res => {
          this.bizTagMap = new Map()
          this.bizTagsAll = res.data
          res.data.forEach(tagInfo => {
            this.bizTagMap.set(tagInfo.tag_code, tagInfo)
          })
        })
    },
    async loadHostGroups() {
      return new Promise((resolve, reject) => {
        this.selectGroupId = []
        let bizTags = this.biz_tag_filter.toString()
        http.get(`/dcp/hostGroup/details?ip_type=${this.entity.ip_type}` +
          `&template_id=${this.entity.template_id}` +
          `&cdn_type=${this.entity.cdn_type}` +
          `&is_own_provider=${this.entity.is_own_provider}` +
          `&lv_type=${this.lvType}` +
          (bizTags !== '' ? `&biz_tags=${bizTags}` : ''))
          .then((res) => {
            this.candidateHostGroupArray = res.data
            if (bizTags === '') {
              this.candidateHostGroupAll = res.data
            }
            resolve()
          })
      })
    },
    getGroupCandidates(type, isDel) {
      let existIds = []
      let id = this.selectedAreaNode && this.selectedAreaNode.id
      let config = this.configMap[id]
      if (config) {
        if (config.groups) {
          config.groups.forEach((group) => {
            existIds.push(group.uuid)
          })
        }
      }
      let candidateHostGroup
      candidateHostGroup = this.candidateHostGroupArray.filter((group) => isDel ? existIds.includes(group.uuid) : !existIds.includes(group.uuid))
      let filterKey = isDel ? this.filterDelKey : this.filterKey
      if (filterKey !== '') {
        let result = [] // 存储符合条件的下拉选项
        candidateHostGroup.forEach(val => {
          if (val.name.indexOf(filterKey) !== -1) result.push(val)
        })
        candidateHostGroup = result
      }
      return candidateHostGroup
    },
    // 带宽单位转换
    parseBwKiloString(bwString) {
      let flowVlueBytes = parseInt(bwString)
      // 如果小于1Kb.则显示为bit
      if (flowVlueBytes === 0 || isNaN(flowVlueBytes)) {
        return '0'
      } else if (flowVlueBytes < 1000) {
        return Math.round(flowVlueBytes) + ' K'
      } else if (flowVlueBytes / 1000 < 1000) {
        return Math.round(flowVlueBytes / 1000) + ' M'
      } else if (flowVlueBytes / 1000 / 1000 < 1000) {
        // return Math.round(flowVlueBytes / 1000 / 1000) + ' G'
        return (flowVlueBytes / 1000 / 1000).toFixed(1) + ' G'
      } else {
        return (flowVlueBytes / 1000 / 1000 / 1000).toFixed(1) + ' T'
      }
    },
    parseBwByteString(bwString) {
      let flowVlueBytes = parseInt(bwString)
      // 如果小于1Kb.则显示为bit
      if (flowVlueBytes === 0 || isNaN(flowVlueBytes)) {
        return '0'
      } else if (flowVlueBytes < 1000) {
        return Math.round(flowVlueBytes) + ' b'
      } else if (flowVlueBytes / 1000 < 1000) {
        return Math.round(flowVlueBytes / 1000) + ' K'
      } else if (flowVlueBytes / 1000 / 1000 < 1000) {
        return Math.round(flowVlueBytes / 1000 / 1000) + ' M'
      } else if ((flowVlueBytes / 1000 / 1000 / 1000 < 1000)) {
        return (flowVlueBytes / 1000 / 1000 / 1000).toFixed(1) + ' G'
      } else {
        return (flowVlueBytes / 1000 / 1000 / 1000 / 1000).toFixed(1) + ' T'
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.content-wrap {
  padding: 0 !important;
}
.tabs-wrapper {
  display: flex;
  flex-direction: column;
  width: 350px;
  height: 100%;
  .tabs-view-config {
    height: 100%;
  }
}
.v-align-middle {
  display: flex;
  justify-content: space-between;
}

:global(.page-config-strategy) {
  height: 100%;
  box-sizing: border-box;

  .content {
    height: calc(100vh - 200px);
    // overflow: hidden;
    box-sizing: border-box;
    padding: 1px;

    .left, .right {
      box-sizing: border-box;
      padding: 1px;
      float: left;
      height: 100%;
    }

    .left {
      width: 350px;
      overflow-y: hidden;
      overflow-x: scroll;
    }

    .right {
      width: calc(100% - 350px);
    }

    .config-item {
      h3 {
        margin: 0 0 20px 0;
        color: #333;
        font-size: 14px;
        font-weight: normal;
        border-bottom: 1px solid #DCDFE6;
        padding-bottom: 10px;
      }

      .host-form {
        display: flex;
        justify-content: space-between;
        margin-bottom: 15px;

        * {
          vertical-align: middle;
        }
      }

      .el-collapse {
        /deep/ {
          // .is-active {
          //   background: #eaeef5;
          // }
          .el-collapse-item__header {
            background: #eaeef5;
            padding: 0 16px;
            margin-bottom: 10px;
          }
          // .el-collapse-item:not(:last-child) {
          //   margin-bottom: 10px;
          // }
        }
        .group-title {
          * {
            vertical-align: middle;
          }

          > span {
            margin-right: 20px;
            display: inline-block;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }

          > span:first-child {
            min-width: 200px;
          }

          > span:nth-child(2), > span:nth-child(3), > span:nth-child(4), > span:nth-child(5){
            opacity: 0.55;
            margin-right: 30px;
          }

          > span:nth-child(2) {
            min-width: 150px;
          }

          > span:nth-child(3) {
            min-width: 100px;
          }

          > span:nth-child(4) {
            min-width: 100px;
          }

          > span:nth-child(5) {
            min-width: 100px;
          }
        }
      }
    }
  }

  > :global(.el-card__body) {
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;

    > :global(.el-collapse) {
      flex-grow: 1;
      overflow: auto;
    }
  }

  .number {
    padding: 1px 10px;
    background-color: rgba(255, 152, 50, 0.1);
    font-size: 12px;
    border-radius: 10px;
    color: #ff9832;
    margin-left: 8px;
  }
  .number-wrap {
    padding: 1px 4px;
    font-size: 12px;
    border-radius: 10px;
    color: #ff9832;
    margin-left: 6px;
  }

  .empty-tips {
    text-align: center;
    color: #999;
    line-height: 200px;
  }
  .ml {
    width: 100px;
    margin-left: 16px;
  }

  :global(.el-collapse .el-icon-arrow-right) {
    margin-left: auto;
    width: auto;
  }

  :global(.el-icon-arrow-right:before) {
    content: '\E791';
  }

  :global(.el-collapse-item__content) {
    padding: 20px 15px;
  }

  :global(.el-table__header-wrapper thead th) {
    background-color: #ffffff !important;
    border-bottom: 1px solid #ebeef5 !important;
  }

}

/deep/ {
  .du-select {
    width: 225px;
    margin: 5px;
  }

  .du-select .el-select__tags-text {
    display: inline-block;
    max-width: 145px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .du-select .el-tag__close.el-icon-close {
    top: -6px;
    right: -4px;
  }

  .du-tag-select {
    width: 225px;
    margin: 5px;
  }

  .du-tag-select .el-select__tags-text {
    display: inline-block;
    max-width: 170px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .du-tag-select .el-tag__close.el-icon-close {
    top: -6px;
    right: -4px;
  }
}

</style>
